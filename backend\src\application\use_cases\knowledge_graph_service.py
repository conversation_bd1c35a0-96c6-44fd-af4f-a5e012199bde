"""
Knowledge Graph service.

This module contains use cases for knowledge graph operations including
entity management, relationship creation, and graph visualization.
"""

import uuid
from datetime import UTC, datetime
from typing import Any, Dict, List, Optional, Set

from src.domain.entities.knowledge_graph import (
    EntityType,
    KnowledgeEntity,
    KnowledgeGraph,
    KnowledgeRelationship,
    RelationType,
)
from src.domain.entities.user import Permission, User
from src.domain.repositories.knowledge_graph_repository import (
    KnowledgeEntityRepository,
    KnowledgeGraphRepository,
    KnowledgeRelationshipRepository,
)
from src.infrastructure.logging.setup import get_logger

logger = get_logger(__name__)


class KnowledgeGraphService:
    """Service for knowledge graph operations."""

    def __init__(
        self,
        entity_repository: KnowledgeEntityRepository,
        relationship_repository: KnowledgeRelationshipRepository,
        graph_repository: KnowledgeGraphRepository,
    ) -> None:
        """
        Initialize the knowledge graph service.

        Args:
            entity_repository: Repository for entity operations
            relationship_repository: Repository for relationship operations
            graph_repository: Repository for graph operations
        """
        self.entity_repository = entity_repository
        self.relationship_repository = relationship_repository
        self.graph_repository = graph_repository

    # Entity operations
    async def create_entity(self, entity: KnowledgeEntity, user: User) -> KnowledgeEntity:
        """
        Create a new knowledge entity.

        Args:
            entity: The entity to create
            user: The user creating the entity

        Returns:
            The created entity

        Raises:
            PermissionError: If user lacks permission
            ValueError: If entity validation fails
        """
        # Check permissions
        if not user.has_permission(Permission.KNOWLEDGE_CREATE):
            raise PermissionError("User lacks permission to create knowledge entities")

        # Set creator
        entity.created_by = uuid.UUID(user.id)
        entity.updated_at = datetime.now(UTC)

        logger.info(f"Creating knowledge entity '{entity.name}' for user {user.id}")

        # Create entity
        created_entity = await self.entity_repository.create(entity)

        logger.info(f"Successfully created knowledge entity {created_entity.id}")
        return created_entity

    async def get_entity(self, entity_id: uuid.UUID, user: User) -> Optional[KnowledgeEntity]:
        """
        Get a knowledge entity by ID.

        Args:
            entity_id: The entity ID
            user: The requesting user

        Returns:
            The entity if found and accessible, None otherwise

        Raises:
            PermissionError: If user lacks permission
        """
        # Check permissions
        if not user.has_permission(Permission.KNOWLEDGE_READ):
            raise PermissionError("User lacks permission to read knowledge entities")

        entity = await self.entity_repository.get_by_id(entity_id)

        if not entity:
            return None

        # Check if user can access this entity
        if str(entity.created_by) != user.id and not entity.is_public:
            raise PermissionError("User cannot access this entity")

        return entity

    async def list_entities(
        self,
        user: User,
        entity_type: Optional[EntityType] = None,
        limit: int = 100,
        offset: int = 0,
    ) -> List[KnowledgeEntity]:
        """
        List knowledge entities for a user.

        Args:
            user: The requesting user
            entity_type: Optional entity type filter
            limit: Maximum number of entities to return
            offset: Number of entities to skip

        Returns:
            List of entities

        Raises:
            PermissionError: If user lacks permission
        """
        # Check permissions
        if not user.has_permission(Permission.KNOWLEDGE_READ):
            raise PermissionError("User lacks permission to read knowledge entities")

        return await self.entity_repository.get_by_user(
            uuid.UUID(user.id), entity_type, limit, offset
        )

    async def search_entities(
        self,
        query: str,
        user: User,
        entity_type: Optional[EntityType] = None,
        include_public: bool = True,
        limit: int = 100,
        offset: int = 0,
    ) -> List[KnowledgeEntity]:
        """
        Search knowledge entities.

        Args:
            query: Search query string
            user: The requesting user
            entity_type: Optional entity type filter
            include_public: Whether to include public entities
            limit: Maximum number of entities to return
            offset: Number of entities to skip

        Returns:
            List of matching entities

        Raises:
            PermissionError: If user lacks permission
        """
        # Check permissions
        if not user.has_permission(Permission.KNOWLEDGE_READ):
            raise PermissionError("User lacks permission to read knowledge entities")

        user_id = uuid.UUID(user.id) if not include_public else None
        
        return await self.entity_repository.search(
            query, user_id, entity_type, include_public, limit, offset
        )

    async def update_entity(self, entity: KnowledgeEntity, user: User) -> KnowledgeEntity:
        """
        Update a knowledge entity.

        Args:
            entity: The entity to update
            user: The user updating the entity

        Returns:
            The updated entity

        Raises:
            PermissionError: If user lacks permission
            ValueError: If entity validation fails or not found
        """
        # Check permissions
        if not user.has_permission(Permission.KNOWLEDGE_UPDATE):
            raise PermissionError("User lacks permission to update knowledge entities")

        # Get existing entity to check ownership
        existing_entity = await self.entity_repository.get_by_id(entity.id)
        if not existing_entity:
            raise ValueError(f"Entity {entity.id} not found")

        if str(existing_entity.created_by) != user.id:
            raise PermissionError("User can only update their own entities")

        # Update timestamp
        entity.updated_at = datetime.now(UTC)

        logger.info(f"Updating knowledge entity {entity.id} for user {user.id}")

        # Update entity
        updated_entity = await self.entity_repository.update(entity)

        logger.info(f"Successfully updated knowledge entity {entity.id}")
        return updated_entity

    async def delete_entity(self, entity_id: uuid.UUID, user: User) -> bool:
        """
        Delete a knowledge entity.

        Args:
            entity_id: The entity ID to delete
            user: The user deleting the entity

        Returns:
            True if deleted, False if not found

        Raises:
            PermissionError: If user lacks permission
        """
        # Check permissions
        if not user.has_permission(Permission.KNOWLEDGE_DELETE):
            raise PermissionError("User lacks permission to delete knowledge entities")

        # Get entity to check ownership
        entity = await self.entity_repository.get_by_id(entity_id)
        if not entity:
            return False

        if str(entity.created_by) != user.id:
            raise PermissionError("User can only delete their own entities")

        logger.info(f"Deleting knowledge entity {entity_id} for user {user.id}")

        # Delete entity
        deleted = await self.entity_repository.delete(entity_id)

        if deleted:
            logger.info(f"Successfully deleted knowledge entity {entity_id}")
        else:
            logger.warning(f"Failed to delete knowledge entity {entity_id}")

        return deleted

    # Relationship operations
    async def create_relationship(
        self, relationship: KnowledgeRelationship, user: User
    ) -> KnowledgeRelationship:
        """
        Create a new knowledge relationship.

        Args:
            relationship: The relationship to create
            user: The user creating the relationship

        Returns:
            The created relationship

        Raises:
            PermissionError: If user lacks permission
            ValueError: If relationship validation fails
        """
        # Check permissions
        if not user.has_permission(Permission.KNOWLEDGE_CREATE):
            raise PermissionError("User lacks permission to create knowledge relationships")

        # Verify entities exist and user has access
        source_entity = await self.get_entity(relationship.source_entity_id, user)
        target_entity = await self.get_entity(relationship.target_entity_id, user)

        if not source_entity or not target_entity:
            raise ValueError("Source or target entity not found or not accessible")

        # Set creator
        relationship.created_by = uuid.UUID(user.id)
        relationship.updated_at = datetime.now(UTC)

        logger.info(f"Creating knowledge relationship for user {user.id}")

        # Create relationship
        created_relationship = await self.relationship_repository.create(relationship)

        logger.info(f"Successfully created knowledge relationship {created_relationship.id}")
        return created_relationship

    async def get_entity_relationships(
        self,
        entity_id: uuid.UUID,
        user: User,
        relation_type: Optional[RelationType] = None,
        direction: Optional[str] = None,
    ) -> List[KnowledgeRelationship]:
        """
        Get relationships for an entity.

        Args:
            entity_id: The entity ID
            user: The requesting user
            relation_type: Optional relationship type filter
            direction: Optional direction filter

        Returns:
            List of relationships

        Raises:
            PermissionError: If user lacks permission
        """
        # Check permissions
        if not user.has_permission(Permission.KNOWLEDGE_READ):
            raise PermissionError("User lacks permission to read knowledge relationships")

        # Verify entity exists and user has access
        entity = await self.get_entity(entity_id, user)
        if not entity:
            raise ValueError("Entity not found or not accessible")

        return await self.relationship_repository.get_entity_relationships(
            entity_id, relation_type, direction
        )

    # Graph operations
    async def get_subgraph(
        self,
        entity_ids: Set[uuid.UUID],
        user: User,
        max_depth: int = 2,
    ) -> KnowledgeGraph:
        """
        Get a subgraph containing specified entities and their neighbors.

        Args:
            entity_ids: Set of entity IDs to include
            user: The requesting user
            max_depth: Maximum depth for neighbor traversal

        Returns:
            The subgraph

        Raises:
            PermissionError: If user lacks permission
        """
        # Check permissions
        if not user.has_permission(Permission.KNOWLEDGE_READ):
            raise PermissionError("User lacks permission to read knowledge graphs")

        # Verify user has access to all entities
        for entity_id in entity_ids:
            entity = await self.get_entity(entity_id, user)
            if not entity:
                raise ValueError(f"Entity {entity_id} not found or not accessible")

        return await self.graph_repository.get_subgraph(
            entity_ids, uuid.UUID(user.id), max_depth
        )

    async def search_entities_in_context(
        self,
        query: str,
        context_entity_ids: Set[uuid.UUID],
        user: User,
        limit: int = 100,
    ) -> List[KnowledgeEntity]:
        """
        Search for entities within the context of specified entities.

        Args:
            query: Search query string
            context_entity_ids: Set of context entity IDs
            user: The requesting user
            limit: Maximum number of entities to return

        Returns:
            List of matching entities

        Raises:
            PermissionError: If user lacks permission
        """
        # Check permissions
        if not user.has_permission(Permission.KNOWLEDGE_READ):
            raise PermissionError("User lacks permission to search knowledge entities")

        return await self.graph_repository.search_entities_in_context(
            query, context_entity_ids, uuid.UUID(user.id), limit
        )

    async def find_entity_paths(
        self,
        source_entity_id: uuid.UUID,
        target_entity_id: uuid.UUID,
        user: User,
        max_depth: int = 5,
    ) -> List[List[uuid.UUID]]:
        """
        Find paths between two entities.

        Args:
            source_entity_id: The source entity ID
            target_entity_id: The target entity ID
            user: The requesting user
            max_depth: Maximum path depth

        Returns:
            List of paths (each path is a list of entity IDs)

        Raises:
            PermissionError: If user lacks permission
        """
        # Check permissions
        if not user.has_permission(Permission.KNOWLEDGE_READ):
            raise PermissionError("User lacks permission to read knowledge graphs")

        # Verify user has access to both entities
        source_entity = await self.get_entity(source_entity_id, user)
        target_entity = await self.get_entity(target_entity_id, user)

        if not source_entity or not target_entity:
            raise ValueError("Source or target entity not found or not accessible")

        return await self.graph_repository.get_entity_paths(
            source_entity_id, target_entity_id, max_depth
        )
