\"\"\"
Concrete Note Repository Implementation
\"\"\"

from uuid import UUID

from sqlalchemy.ext.asyncio import AsyncSession

from src.domain.entities.note import Note
from src.domain.repositories.note_repository import NoteRepository as AbstractNoteRepository
from src.infrastructure.database.models import Note as NoteModel


class NoteRepository(AbstractNoteRepository):
    \"\"\"Concrete implementation of the NoteRepository interface using SQLAlchemy.\"\"\"

    def __init__(self, session: AsyncSession):
        self.session = session

    async def get_by_id(self, note_id: UUID) -> Note | None:
        \"\"\"Get a note by its ID.\"\"\"
        try:
            note_model = await self.session.get(NoteModel, note_id)
            if note_model:
                return Note.from_orm(note_model)
            return None
        except Exception as e:
            print(f"Error getting note by id: {e}")
            return None

    async def get_all_by_user(
        self,
        user_id: UUID,
        folder_id: UUID | None = None,
        include_archived: bool = False,
        only_starred: bool = False,
        tags: list[str] | None = None,
        search_query: str | None = None,
    ) -> list[Note]:
        \"\"\"Get all notes for a user with optional filtering.\"\"\"
        try:
            query = self.session.query(NoteModel).filter(NoteModel.user_id == user_id)

            if folder_id:
                query = query.filter(NoteModel.folder_id == folder_id)
            if not include_archived:
                query = query.filter(NoteModel.is_archived == False)  # noqa: E712
            if only_starred:
                query = query.filter(NoteModel.is_starred == True)  # noqa: E712
            if tags:
                query = query.filter(NoteModel.tags.overlap(tags))
            if search_query:
                query = query.filter(NoteModel.title.ilike(f"%{search_query}%") | NoteModel.content.ilike(f"%{search_query}%"))

            note_models = await query.all()
            return [Note.from_orm(note_model) for note_model in note_models]
        except Exception as e:
            print(f"Error getting all notes by user: {e}")
            return []

    async def create(self, note: Note) -> Note:
        \"\"\"Create a new note.\"\"\"
        try:
            note_model = NoteModel.from_orm(note)
            self.session.add(note_model)
            await self.session.commit()
            await self.session.refresh(note_model)
            return Note.from_orm(note_model)
        except Exception as e:
            print(f"Error creating note: {e}")
            return note

    async def update(self, note: Note) -> Note:
        \"\"\"Update an existing note.\"\"\"
        try:
            note_model = await self.session.get(NoteModel, note.id)
            if note_model:
                for key, value in note.dict().items():
                    setattr(note_model, key, value)
                await self.session.commit()
                await self.session.refresh(note_model)
                return Note.from_orm(note_model)
            else:
                raise ValueError(f"Note with id {note.id} not found")
        except Exception as e:
            print(f"Error updating note: {e}")
            return note

    async def delete(self, note_id: UUID) -> bool:
        \"\"\"Delete a note by its ID.\"\"\"
        try:
            note_model = await self.session.get(NoteModel, note_id)
            if note_model:
                await self.session.delete(note_model)
                await self.session.commit()
                return True
            return False
        except Exception as e:
            print(f"Error deleting note: {e}")
            return False
