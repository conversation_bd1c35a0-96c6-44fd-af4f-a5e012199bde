"""
Model Management repository implementations.

This module contains the SQLAlchemy implementations of the model management repositories.
"""

import uuid

from sqlalchemy import and_, desc, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload

from src.domain.entities.model_management import (
    ModelDownloadTask,
    ModelInstance,
    ModelStatus,
)
from src.domain.repositories.model_management_repository import (
    ModelDownloadTaskRepository,
    ModelInstanceRepository,
)
from src.infrastructure.database.models.model_management import (
    ModelDownloadTaskModel,
    ModelInstanceModel,
)
from src.infrastructure.logging.setup import get_logger

logger = get_logger(__name__)


class ModelInstanceRepositoryImpl(ModelInstanceRepository):
    """SQLAlchemy implementation of model instance repository."""

    def __init__(self, session: AsyncSession) -> None:
        """
        Initialize repository with database session.

        Args:
            session: SQLAlchemy async session
        """
        self.session = session

    async def create(self, instance: ModelInstance) -> ModelInstance:
        """Create a new model instance."""
        try:
            instance_model = ModelInstanceModel.from_domain(instance)
            self.session.add(instance_model)
            await self.session.commit()
            await self.session.refresh(instance_model)

            logger.info(f"Created model instance {instance_model.id}")
            return instance_model.to_domain()

        except Exception as e:
            await self.session.rollback()
            logger.error(f"Failed to create model instance: {e}")
            raise

    async def get_by_id(self, instance_id: uuid.UUID) -> ModelInstance | None:
        """Get a model instance by ID."""
        try:
            stmt = (
                select(ModelInstanceModel)
                .options(
                    selectinload(ModelInstanceModel.creator),
                    selectinload(ModelInstanceModel.download_tasks),
                )
                .where(ModelInstanceModel.id == instance_id)
            )
            result = await self.session.execute(stmt)
            instance_model = result.scalar_one_or_none()

            return instance_model.to_domain() if instance_model else None

        except Exception as e:
            logger.error(f"Failed to get model instance {instance_id}: {e}")
            raise

    async def get_by_user(
        self,
        user_id: uuid.UUID,
        status: ModelStatus | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[ModelInstance]:
        """Get model instances by user ID with optional filtering."""
        try:
            conditions = [ModelInstanceModel.created_by == user_id]

            if status:
                conditions.append(ModelInstanceModel.status == status)

            stmt = (
                select(ModelInstanceModel)
                .options(
                    selectinload(ModelInstanceModel.creator),
                    selectinload(ModelInstanceModel.download_tasks),
                )
                .where(and_(*conditions))
                .order_by(desc(ModelInstanceModel.updated_at))
                .limit(limit)
                .offset(offset)
            )

            result = await self.session.execute(stmt)
            instance_models = result.scalars().all()

            return [model.to_domain() for model in instance_models]

        except Exception as e:
            logger.error(f"Failed to get model instances for user {user_id}: {e}")
            raise

    async def get_by_model_id(
        self,
        model_id: str,
        user_id: uuid.UUID | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[ModelInstance]:
        """Get model instances by model ID."""
        try:
            conditions = [ModelInstanceModel.model_id == model_id]

            if user_id:
                conditions.append(ModelInstanceModel.created_by == user_id)

            stmt = (
                select(ModelInstanceModel)
                .options(
                    selectinload(ModelInstanceModel.creator),
                    selectinload(ModelInstanceModel.download_tasks),
                )
                .where(and_(*conditions))
                .order_by(desc(ModelInstanceModel.updated_at))
                .limit(limit)
                .offset(offset)
            )

            result = await self.session.execute(stmt)
            instance_models = result.scalars().all()

            return [model.to_domain() for model in instance_models]

        except Exception as e:
            logger.error(f"Failed to get model instances for model {model_id}: {e}")
            raise

    async def get_serving_instances(
        self,
        user_id: uuid.UUID | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[ModelInstance]:
        """Get currently serving model instances."""
        try:
            conditions = [ModelInstanceModel.status == ModelStatus.SERVING]

            if user_id:
                conditions.append(ModelInstanceModel.created_by == user_id)

            stmt = (
                select(ModelInstanceModel)
                .options(
                    selectinload(ModelInstanceModel.creator),
                    selectinload(ModelInstanceModel.download_tasks),
                )
                .where(and_(*conditions))
                .order_by(desc(ModelInstanceModel.last_used_at))
                .limit(limit)
                .offset(offset)
            )

            result = await self.session.execute(stmt)
            instance_models = result.scalars().all()

            return [model.to_domain() for model in instance_models]

        except Exception as e:
            logger.error(f"Failed to get serving model instances: {e}")
            raise

    async def update(self, instance: ModelInstance) -> ModelInstance:
        """Update an existing model instance."""
        try:
            stmt = select(ModelInstanceModel).where(
                ModelInstanceModel.id == instance.id
            )
            result = await self.session.execute(stmt)
            instance_model = result.scalar_one_or_none()

            if not instance_model:
                raise ValueError(f"Model instance {instance.id} not found")

            # Update fields from domain entity
            updated_model = ModelInstanceModel.from_domain(instance)

            # Copy updated fields
            instance_model.status = updated_model.status
            instance_model.download_url = updated_model.download_url
            instance_model.local_path = updated_model.local_path
            instance_model.download_progress = updated_model.download_progress
            instance_model.download_speed = updated_model.download_speed
            instance_model.serving_port = updated_model.serving_port
            instance_model.serving_url = updated_model.serving_url
            instance_model.api_endpoint = updated_model.api_endpoint
            instance_model.load_time_seconds = updated_model.load_time_seconds
            instance_model.memory_usage_mb = updated_model.memory_usage_mb
            instance_model.gpu_memory_usage_mb = updated_model.gpu_memory_usage_mb
            instance_model.configuration = updated_model.configuration
            instance_model.error_message = updated_model.error_message
            instance_model.error_details = updated_model.error_details
            instance_model.updated_at = updated_model.updated_at
            instance_model.last_used_at = updated_model.last_used_at

            await self.session.commit()
            await self.session.refresh(instance_model)

            logger.info(f"Updated model instance {instance.id}")
            return instance_model.to_domain()

        except Exception as e:
            await self.session.rollback()
            logger.error(f"Failed to update model instance {instance.id}: {e}")
            raise

    async def delete(self, instance_id: uuid.UUID) -> bool:
        """Delete a model instance."""
        try:
            stmt = select(ModelInstanceModel).where(
                ModelInstanceModel.id == instance_id
            )
            result = await self.session.execute(stmt)
            instance_model = result.scalar_one_or_none()

            if not instance_model:
                return False

            await self.session.delete(instance_model)
            await self.session.commit()

            logger.info(f"Deleted model instance {instance_id}")
            return True

        except Exception as e:
            await self.session.rollback()
            logger.error(f"Failed to delete model instance {instance_id}: {e}")
            raise

    async def get_usage_statistics(self, user_id: uuid.UUID) -> dict:
        """Get usage statistics for model instances."""
        try:
            # Count instances by status
            status_counts_stmt = (
                select(
                    ModelInstanceModel.status,
                    func.count(ModelInstanceModel.id).label("count"),
                )
                .where(ModelInstanceModel.created_by == user_id)
                .group_by(ModelInstanceModel.status)
            )

            status_result = await self.session.execute(status_counts_stmt)
            status_counts = {row.status: row.count for row in status_result}

            # Get total memory usage
            memory_usage_stmt = select(
                func.sum(ModelInstanceModel.memory_usage_mb)
            ).where(
                and_(
                    ModelInstanceModel.created_by == user_id,
                    ModelInstanceModel.status == ModelStatus.SERVING,
                )
            )

            memory_result = await self.session.execute(memory_usage_stmt)
            total_memory_usage = memory_result.scalar() or 0

            # Get GPU memory usage
            gpu_memory_usage_stmt = select(
                func.sum(ModelInstanceModel.gpu_memory_usage_mb)
            ).where(
                and_(
                    ModelInstanceModel.created_by == user_id,
                    ModelInstanceModel.status == ModelStatus.SERVING,
                )
            )

            gpu_memory_result = await self.session.execute(gpu_memory_usage_stmt)
            total_gpu_memory_usage = gpu_memory_result.scalar() or 0

            return {
                "status_counts": status_counts,
                "total_memory_usage_mb": total_memory_usage,
                "total_gpu_memory_usage_mb": total_gpu_memory_usage,
                "serving_count": status_counts.get(ModelStatus.SERVING, 0),
                "downloading_count": status_counts.get(ModelStatus.DOWNLOADING, 0),
                "error_count": status_counts.get(ModelStatus.ERROR, 0),
            }

        except Exception as e:
            logger.error(f"Failed to get usage statistics for user {user_id}: {e}")
            raise


class ModelDownloadTaskRepositoryImpl(ModelDownloadTaskRepository):
    """SQLAlchemy implementation of model download task repository."""

    def __init__(self, session: AsyncSession) -> None:
        """
        Initialize repository with database session.

        Args:
            session: SQLAlchemy async session
        """
        self.session = session

    async def create(self, task: ModelDownloadTask) -> ModelDownloadTask:
        """Create a new download task."""
        try:
            task_model = ModelDownloadTaskModel.from_domain(task)
            self.session.add(task_model)
            await self.session.commit()
            await self.session.refresh(task_model)

            logger.info(f"Created download task {task_model.id}")
            return task_model.to_domain()

        except Exception as e:
            await self.session.rollback()
            logger.error(f"Failed to create download task: {e}")
            raise

    async def get_by_id(self, task_id: uuid.UUID) -> ModelDownloadTask | None:
        """Get a download task by ID."""
        try:
            stmt = (
                select(ModelDownloadTaskModel)
                .options(
                    selectinload(ModelDownloadTaskModel.instance),
                    selectinload(ModelDownloadTaskModel.creator),
                )
                .where(ModelDownloadTaskModel.id == task_id)
            )
            result = await self.session.execute(stmt)
            task_model = result.scalar_one_or_none()

            return task_model.to_domain() if task_model else None

        except Exception as e:
            logger.error(f"Failed to get download task {task_id}: {e}")
            raise

    async def get_by_user(
        self,
        user_id: uuid.UUID,
        status: str | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[ModelDownloadTask]:
        """Get download tasks by user ID with optional filtering."""
        try:
            conditions = [ModelDownloadTaskModel.created_by == user_id]

            if status:
                conditions.append(ModelDownloadTaskModel.status == status)

            stmt = (
                select(ModelDownloadTaskModel)
                .options(
                    selectinload(ModelDownloadTaskModel.instance),
                    selectinload(ModelDownloadTaskModel.creator),
                )
                .where(and_(*conditions))
                .order_by(desc(ModelDownloadTaskModel.created_at))
                .limit(limit)
                .offset(offset)
            )

            result = await self.session.execute(stmt)
            task_models = result.scalars().all()

            return [model.to_domain() for model in task_models]

        except Exception as e:
            logger.error(f"Failed to get download tasks for user {user_id}: {e}")
            raise

    async def get_active_tasks(self, limit: int = 100) -> list[ModelDownloadTask]:
        """Get active download tasks."""
        try:
            stmt = (
                select(ModelDownloadTaskModel)
                .options(
                    selectinload(ModelDownloadTaskModel.instance),
                    selectinload(ModelDownloadTaskModel.creator),
                )
                .where(ModelDownloadTaskModel.status.in_(["pending", "running"]))
                .order_by(ModelDownloadTaskModel.created_at)
                .limit(limit)
            )

            result = await self.session.execute(stmt)
            task_models = result.scalars().all()

            return [model.to_domain() for model in task_models]

        except Exception as e:
            logger.error(f"Failed to get active download tasks: {e}")
            raise

    async def update(self, task: ModelDownloadTask) -> ModelDownloadTask:
        """Update an existing download task."""
        try:
            stmt = select(ModelDownloadTaskModel).where(
                ModelDownloadTaskModel.id == task.id
            )
            result = await self.session.execute(stmt)
            task_model = result.scalar_one_or_none()

            if not task_model:
                raise ValueError(f"Download task {task.id} not found")

            # Update fields from domain entity
            updated_model = ModelDownloadTaskModel.from_domain(task)

            # Copy updated fields
            task_model.status = updated_model.status
            task_model.progress = updated_model.progress
            task_model.total_size = updated_model.total_size
            task_model.downloaded_size = updated_model.downloaded_size
            task_model.download_speed = updated_model.download_speed
            task_model.started_at = updated_model.started_at
            task_model.completed_at = updated_model.completed_at
            task_model.estimated_completion = updated_model.estimated_completion
            task_model.error_message = updated_model.error_message
            task_model.retry_count = updated_model.retry_count
            task_model.updated_at = updated_model.updated_at

            await self.session.commit()
            await self.session.refresh(task_model)

            logger.info(f"Updated download task {task.id}")
            return task_model.to_domain()

        except Exception as e:
            await self.session.rollback()
            logger.error(f"Failed to update download task {task.id}: {e}")
            raise

    async def delete(self, task_id: uuid.UUID) -> bool:
        """Delete a download task."""
        try:
            stmt = select(ModelDownloadTaskModel).where(
                ModelDownloadTaskModel.id == task_id
            )
            result = await self.session.execute(stmt)
            task_model = result.scalar_one_or_none()

            if not task_model:
                return False

            await self.session.delete(task_model)
            await self.session.commit()

            logger.info(f"Deleted download task {task_id}")
            return True

        except Exception as e:
            await self.session.rollback()
            logger.error(f"Failed to delete download task {task_id}: {e}")
            raise
