"""
Main FastAPI application entry point.

This module sets up the FastAPI application with all necessary middleware,
routers, and configuration following clean architecture principles.
"""

import logging
from collections.abc import AsyncGenerator
from contextlib import asynccontextmanager
from datetime import UTC, datetime

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware

from src.infrastructure.cache.cache_service import CacheService
from src.infrastructure.cache.redis_client import RedisClient
from src.infrastructure.config.settings import get_settings
from src.infrastructure.container import get_container
from src.infrastructure.database.connection import DatabaseManager
from src.infrastructure.error_handling.middleware import ErrorHandlingMiddleware
from src.infrastructure.logging import setup_logging
from src.presentation.api.v1.router import api_v1_router
from src.presentation.middleware.rate_limit import RateLimitMiddleware
from src.presentation.middleware.request_id import RequestIDMiddleware
from src.presentation.middleware.security import SecurityMiddleware

# Initialize settings
settings = get_settings()

# Setup logging
setup_logging(settings.log_level, settings.log_format)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """
    Application lifespan manager.

    Handles startup and shutdown events for the FastAPI application.
    """
    # Startup
    logger.info("Starting Lonors Backend API")

    # Get container
    container = get_container()

    # Initialize database
    db_manager = container.get(DatabaseManager)
    await db_manager.initialize()

    # Initialize Redis
    redis_client = container.get(RedisClient)
    await redis_client.connect()

    # Store references in app state
    app.state.db_manager = db_manager
    app.state.redis_client = redis_client
    app.state.cache_service = container.get(CacheService)

    logger.info("Application startup complete")

    yield

    # Shutdown
    logger.info("Shutting down Lonors Backend API")

    # Dispose of container (handles all cleanup)
    await container.dispose()

    logger.info("Application shutdown complete")


def create_application() -> FastAPI:
    """
    Create and configure the FastAPI application.

    Returns:
        FastAPI: Configured FastAPI application instance
    """
    # Create FastAPI app with lifespan
    app = FastAPI(
        title=settings.app_name,
        description="Lonors Backend API - Modern full-stack application",
        version=settings.app_version,
        debug=settings.debug,
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None,
        openapi_url="/openapi.json" if settings.debug else None,
        lifespan=lifespan,
    )

    # Add middleware (order matters - last added is executed first)

    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins,
        allow_credentials=settings.cors_allow_credentials,
        allow_methods=["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
        allow_headers=["*"],
    )

    # Compression middleware
    app.add_middleware(GZipMiddleware, minimum_size=1000)

    # Trusted host middleware
    if settings.environment == "production":
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=settings.allowed_hosts,
        )

    # Rate limiting middleware
    app.add_middleware(
        RateLimitMiddleware,
        requests_per_minute=settings.rate_limit_per_minute,
        burst_size=settings.rate_limit_burst,
        exclude_paths=["/health", "/docs", "/redoc", "/openapi.json"],
    )

    # Custom middleware
    app.add_middleware(SecurityMiddleware)
    app.add_middleware(ErrorHandlingMiddleware)
    app.add_middleware(RequestIDMiddleware)

    # Include routers
    app.include_router(api_v1_router, prefix="/api/v1")

    return app


# Create the application instance
app = create_application()


@app.get("/", tags=["Root"])
async def root():
    """Root endpoint providing basic API information."""
    return {
        "message": "Lonors Backend API",
        "version": settings.app_version,
        "environment": settings.environment,
        "docs": "/docs" if settings.debug else "Documentation disabled in production",
    }


@app.get("/health", tags=["Health"])
async def health_check():
    """
    Health check endpoint for monitoring and load balancers.

    Returns:
        dict: Health status information
    """
    # Check database connectivity
    db_healthy = True
    try:
        db_manager = app.state.db_manager
        # Perform a simple query to check DB connection
        # This would be implemented in the DatabaseManager
    except Exception:
        db_healthy = False

    # Check Redis connectivity
    redis_healthy = True
    try:
        redis_client = app.state.redis_client
        if redis_client.client:
            await redis_client.client.ping()
    except Exception:
        redis_healthy = False

    return {
        "status": "healthy" if (db_healthy and redis_healthy) else "unhealthy",
        "timestamp": datetime.now(UTC).isoformat(),
        "version": settings.app_version,
        "environment": settings.environment,
        "checks": {
            "database": "healthy" if db_healthy else "unhealthy",
            "redis": "healthy" if redis_healthy else "unhealthy",
        },
    }


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        "src.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.reload,
        log_level=settings.log_level.lower(),
        workers=1 if settings.reload else settings.workers,
    )
