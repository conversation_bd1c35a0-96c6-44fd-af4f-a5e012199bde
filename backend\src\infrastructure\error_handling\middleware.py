"""
Error handling middleware.

This module provides comprehensive error handling middleware for FastAPI
with structured logging and error response formatting.
"""

import traceback
import uuid
from typing import Any, Dict, Optional

from fastapi import HTTPException, Request, Response, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from src.infrastructure.error_handling.exceptions import (
    ConflictError,
    ExternalServiceError,
    LonorsBaseException,
    ModelManagementError,
    NotFoundError,
    PermissionDeniedError,
    RateLimitError,
    ValidationError,
    WebSocketError,
    WorkflowError,
)
from src.infrastructure.logging.setup import get_logger

logger = get_logger(__name__)


class ErrorHandlingMiddleware(BaseHTTPMiddleware):
    """Middleware for handling exceptions and formatting error responses."""

    async def dispatch(self, request: Request, call_next) -> Response:
        """
        Process request and handle any exceptions.

        Args:
            request: FastAPI request
            call_next: Next middleware/handler

        Returns:
            Response with error handling
        """
        # Generate correlation ID for request tracing
        correlation_id = str(uuid.uuid4())
        request.state.correlation_id = correlation_id

        try:
            response = await call_next(request)
            return response

        except Exception as exc:
            return await self._handle_exception(exc, request, correlation_id)

    async def _handle_exception(
        self,
        exc: Exception,
        request: Request,
        correlation_id: str,
    ) -> JSONResponse:
        """
        Handle exception and return appropriate JSON response.

        Args:
            exc: Exception that occurred
            request: FastAPI request
            correlation_id: Request correlation ID

        Returns:
            JSONResponse with error details
        """
        # Log the exception
        await self._log_exception(exc, request, correlation_id)

        # Handle different exception types
        if isinstance(exc, LonorsBaseException):
            return await self._handle_lonors_exception(exc)
        elif isinstance(exc, HTTPException):
            return await self._handle_http_exception(exc, correlation_id)
        else:
            return await self._handle_generic_exception(exc, correlation_id)

    async def _handle_lonors_exception(self, exc: LonorsBaseException) -> JSONResponse:
        """Handle Lonors platform exceptions."""
        status_code = self._get_status_code_for_exception(exc)
        
        return JSONResponse(
            status_code=status_code,
            content={
                "error": exc.to_dict(),
                "success": False,
            },
        )

    async def _handle_http_exception(
        self,
        exc: HTTPException,
        correlation_id: str,
    ) -> JSONResponse:
        """Handle FastAPI HTTP exceptions."""
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": {
                    "error_code": f"HTTP_{exc.status_code}",
                    "message": exc.detail,
                    "correlation_id": correlation_id,
                    "exception_type": "HTTPException",
                },
                "success": False,
            },
        )

    async def _handle_generic_exception(
        self,
        exc: Exception,
        correlation_id: str,
    ) -> JSONResponse:
        """Handle generic Python exceptions."""
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "error": {
                    "error_code": "INTERNAL_SERVER_ERROR",
                    "message": "An unexpected error occurred",
                    "correlation_id": correlation_id,
                    "exception_type": type(exc).__name__,
                },
                "success": False,
            },
        )

    def _get_status_code_for_exception(self, exc: LonorsBaseException) -> int:
        """Get appropriate HTTP status code for Lonors exception."""
        if isinstance(exc, ValidationError):
            return status.HTTP_422_UNPROCESSABLE_ENTITY
        elif isinstance(exc, NotFoundError):
            return status.HTTP_404_NOT_FOUND
        elif isinstance(exc, PermissionDeniedError):
            return status.HTTP_403_FORBIDDEN
        elif isinstance(exc, ConflictError):
            return status.HTTP_409_CONFLICT
        elif isinstance(exc, RateLimitError):
            return status.HTTP_429_TOO_MANY_REQUESTS
        elif isinstance(exc, (ModelManagementError, WorkflowError)):
            return status.HTTP_400_BAD_REQUEST
        elif isinstance(exc, WebSocketError):
            return status.HTTP_400_BAD_REQUEST
        elif isinstance(exc, ExternalServiceError):
            return status.HTTP_502_BAD_GATEWAY
        else:
            return status.HTTP_500_INTERNAL_SERVER_ERROR

    async def _log_exception(
        self,
        exc: Exception,
        request: Request,
        correlation_id: str,
    ) -> None:
        """Log exception with context."""
        # Extract request information
        request_info = {
            "method": request.method,
            "url": str(request.url),
            "headers": dict(request.headers),
            "correlation_id": correlation_id,
        }

        # Add user information if available
        if hasattr(request.state, "user"):
            request_info["user_id"] = request.state.user.id

        # Log based on exception type
        if isinstance(exc, LonorsBaseException):
            logger.warning(
                f"Lonors exception: {exc.error_code}",
                extra={
                    "exception": exc.to_dict(),
                    "request": request_info,
                },
            )
        elif isinstance(exc, HTTPException):
            logger.warning(
                f"HTTP exception: {exc.status_code}",
                extra={
                    "status_code": exc.status_code,
                    "detail": exc.detail,
                    "request": request_info,
                },
            )
        else:
            logger.error(
                f"Unhandled exception: {type(exc).__name__}",
                extra={
                    "exception_type": type(exc).__name__,
                    "exception_message": str(exc),
                    "traceback": traceback.format_exc(),
                    "request": request_info,
                },
            )


class WebSocketErrorHandler:
    """Error handler for WebSocket connections."""

    def __init__(self):
        self.logger = get_logger(__name__)

    async def handle_websocket_error(
        self,
        websocket,
        exc: Exception,
        connection_id: Optional[str] = None,
    ) -> None:
        """
        Handle WebSocket errors.

        Args:
            websocket: WebSocket connection
            exc: Exception that occurred
            connection_id: WebSocket connection ID
        """
        correlation_id = str(uuid.uuid4())
        
        # Log the error
        await self._log_websocket_error(exc, connection_id, correlation_id)

        # Send error message to client if connection is still open
        try:
            if isinstance(exc, LonorsBaseException):
                error_data = exc.to_dict()
            else:
                error_data = {
                    "error_code": "WEBSOCKET_ERROR",
                    "message": str(exc),
                    "correlation_id": correlation_id,
                    "exception_type": type(exc).__name__,
                }

            await websocket.send_json({
                "type": "error",
                "data": error_data,
                "timestamp": correlation_id,
            })

        except Exception as send_error:
            self.logger.error(
                f"Failed to send error message to WebSocket: {send_error}",
                extra={
                    "connection_id": connection_id,
                    "original_error": str(exc),
                    "send_error": str(send_error),
                },
            )

    async def _log_websocket_error(
        self,
        exc: Exception,
        connection_id: Optional[str],
        correlation_id: str,
    ) -> None:
        """Log WebSocket error."""
        context = {
            "connection_id": connection_id,
            "correlation_id": correlation_id,
        }

        if isinstance(exc, LonorsBaseException):
            self.logger.warning(
                f"WebSocket Lonors exception: {exc.error_code}",
                extra={
                    "exception": exc.to_dict(),
                    "context": context,
                },
            )
        else:
            self.logger.error(
                f"WebSocket unhandled exception: {type(exc).__name__}",
                extra={
                    "exception_type": type(exc).__name__,
                    "exception_message": str(exc),
                    "traceback": traceback.format_exc(),
                    "context": context,
                },
            )


# Global error handler instances
websocket_error_handler = WebSocketErrorHandler()


def create_error_response(
    error_code: str,
    message: str,
    details: Optional[Dict[str, Any]] = None,
    correlation_id: Optional[str] = None,
) -> Dict[str, Any]:
    """
    Create standardized error response.

    Args:
        error_code: Machine-readable error code
        message: Human-readable error message
        details: Additional error details
        correlation_id: Request correlation ID

    Returns:
        Standardized error response dictionary
    """
    return {
        "error": {
            "error_code": error_code,
            "message": message,
            "details": details or {},
            "correlation_id": correlation_id or str(uuid.uuid4()),
        },
        "success": False,
    }


def create_validation_error_response(
    field_errors: Dict[str, list[str]],
    correlation_id: Optional[str] = None,
) -> Dict[str, Any]:
    """
    Create validation error response.

    Args:
        field_errors: Field-specific validation errors
        correlation_id: Request correlation ID

    Returns:
        Validation error response dictionary
    """
    return create_error_response(
        error_code="VALIDATION_ERROR",
        message="Validation failed",
        details={"field_errors": field_errors},
        correlation_id=correlation_id,
    )
