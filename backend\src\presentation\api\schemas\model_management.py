"""
Model Management API schemas.

This module contains the Pydantic schemas for model management API requests and responses.
"""

import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field

from src.domain.entities.model_management import (
    ModelDownloadTask,
    ModelInfo,
    ModelInstance,
    ModelProvider,
    ModelStatus,
    ModelType,
)


# Model discovery schemas
class ModelSearchRequest(BaseModel):
    """Request schema for searching models."""
    
    query: str = Field(..., min_length=1, description="Search query")
    model_type: Optional[ModelType] = Field(None, description="Filter by model type")
    provider: Optional[ModelProvider] = Field(None, description="Filter by provider")
    limit: int = Field(50, ge=1, le=100, description="Maximum number of models to return")

    class Config:
        """Pydantic configuration."""
        use_enum_values = True
        schema_extra = {
            "example": {
                "query": "llama",
                "model_type": "language_model",
                "provider": "huggingface",
                "limit": 20
            }
        }


class ModelInfoResponse(BaseModel):
    """Response schema for model information."""
    
    id: str = Field(..., description="Model ID")
    name: str = Field(..., description="Model name")
    display_name: str = Field(..., description="Human-readable model name")
    description: Optional[str] = Field(None, description="Model description")
    model_type: ModelType = Field(..., description="Model type")
    provider: ModelProvider = Field(..., description="Model provider")
    version: str = Field(..., description="Model version")
    size_bytes: Optional[int] = Field(None, description="Model size in bytes")
    size_display: str = Field(..., description="Human-readable size")
    parameter_count: Optional[int] = Field(None, description="Number of parameters")
    parameter_display: str = Field(..., description="Human-readable parameter count")
    context_length: Optional[int] = Field(None, description="Maximum context length")
    min_ram_gb: Optional[float] = Field(None, description="Minimum RAM required in GB")
    min_vram_gb: Optional[float] = Field(None, description="Minimum VRAM required in GB")
    gpu_required: bool = Field(..., description="Whether GPU is required")
    capabilities: List[str] = Field(..., description="Model capabilities")
    supported_formats: List[str] = Field(..., description="Supported input/output formats")
    languages: List[str] = Field(..., description="Supported languages")
    tags: List[str] = Field(..., description="Model tags")
    license: Optional[str] = Field(None, description="Model license")
    homepage_url: Optional[str] = Field(None, description="Model homepage URL")
    repository_url: Optional[str] = Field(None, description="Model repository URL")
    paper_url: Optional[str] = Field(None, description="Research paper URL")
    provider_metadata: Dict[str, Any] = Field(..., description="Provider-specific metadata")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    class Config:
        """Pydantic configuration."""
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
        }

    @classmethod
    def from_domain(cls, model: ModelInfo) -> "ModelInfoResponse":
        """
        Create response schema from domain entity.

        Args:
            model: ModelInfo domain entity

        Returns:
            ModelInfoResponse instance
        """
        return cls(
            id=model.id,
            name=model.name,
            display_name=model.display_name,
            description=model.description,
            model_type=model.model_type,
            provider=model.provider,
            version=model.version,
            size_bytes=model.size_bytes,
            size_display=model.get_size_display(),
            parameter_count=model.parameter_count,
            parameter_display=model.get_parameter_display(),
            context_length=model.context_length,
            min_ram_gb=model.min_ram_gb,
            min_vram_gb=model.min_vram_gb,
            gpu_required=model.gpu_required,
            capabilities=model.capabilities,
            supported_formats=model.supported_formats,
            languages=model.languages,
            tags=model.tags,
            license=model.license,
            homepage_url=model.homepage_url,
            repository_url=model.repository_url,
            paper_url=model.paper_url,
            provider_metadata=model.provider_metadata,
            created_at=model.created_at,
            updated_at=model.updated_at,
        )


class ModelDiscoveryResponse(BaseModel):
    """Response schema for model discovery."""
    
    models: List[ModelInfoResponse] = Field(..., description="Discovered models")
    total: int = Field(..., description="Total number of models found")
    provider: ModelProvider = Field(..., description="Provider searched")
    query: Optional[str] = Field(None, description="Search query used")

    class Config:
        """Pydantic configuration."""
        use_enum_values = True


# Model instance schemas
class ModelInstanceCreateRequest(BaseModel):
    """Request schema for creating a model instance."""
    
    model_id: str = Field(..., description="Model ID to create instance for")
    configuration: Optional[Dict[str, Any]] = Field(None, description="Model configuration")

    class Config:
        """Pydantic configuration."""
        schema_extra = {
            "example": {
                "model_id": "microsoft/DialoGPT-medium",
                "configuration": {
                    "max_length": 1000,
                    "temperature": 0.7,
                    "top_p": 0.9
                }
            }
        }


class ModelInstanceResponse(BaseModel):
    """Response schema for model instance."""
    
    id: uuid.UUID = Field(..., description="Instance ID")
    model_id: str = Field(..., description="Model ID")
    status: ModelStatus = Field(..., description="Current status")
    download_url: Optional[str] = Field(None, description="Download URL")
    local_path: Optional[str] = Field(None, description="Local file path")
    download_progress: float = Field(..., description="Download progress percentage")
    download_speed: Optional[float] = Field(None, description="Download speed in bytes/second")
    serving_port: Optional[int] = Field(None, description="Serving port")
    serving_url: Optional[str] = Field(None, description="Serving URL")
    api_endpoint: Optional[str] = Field(None, description="API endpoint")
    load_time_seconds: Optional[float] = Field(None, description="Model load time in seconds")
    memory_usage_mb: Optional[float] = Field(None, description="Memory usage in MB")
    gpu_memory_usage_mb: Optional[float] = Field(None, description="GPU memory usage in MB")
    configuration: Dict[str, Any] = Field(..., description="Model configuration")
    error_message: Optional[str] = Field(None, description="Error message if status is ERROR")
    error_details: Dict[str, Any] = Field(..., description="Detailed error information")
    created_by: uuid.UUID = Field(..., description="Creator user ID")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    last_used_at: Optional[datetime] = Field(None, description="Last usage timestamp")

    class Config:
        """Pydantic configuration."""
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            uuid.UUID: lambda v: str(v),
        }

    @classmethod
    def from_domain(cls, instance: ModelInstance) -> "ModelInstanceResponse":
        """
        Create response schema from domain entity.

        Args:
            instance: ModelInstance domain entity

        Returns:
            ModelInstanceResponse instance
        """
        return cls(
            id=instance.id,
            model_id=instance.model_id,
            status=instance.status,
            download_url=instance.download_url,
            local_path=instance.local_path,
            download_progress=instance.download_progress,
            download_speed=instance.download_speed,
            serving_port=instance.serving_port,
            serving_url=instance.serving_url,
            api_endpoint=instance.api_endpoint,
            load_time_seconds=instance.load_time_seconds,
            memory_usage_mb=instance.memory_usage_mb,
            gpu_memory_usage_mb=instance.gpu_memory_usage_mb,
            configuration=instance.configuration,
            error_message=instance.error_message,
            error_details=instance.error_details,
            created_by=instance.created_by,
            created_at=instance.created_at,
            updated_at=instance.updated_at,
            last_used_at=instance.last_used_at,
        )


# Download schemas
class ModelDownloadRequest(BaseModel):
    """Request schema for downloading a model."""
    
    force_redownload: bool = Field(default=False, description="Force redownload if already exists")

    class Config:
        """Pydantic configuration."""
        schema_extra = {
            "example": {
                "force_redownload": False
            }
        }


class ModelDownloadTaskResponse(BaseModel):
    """Response schema for model download task."""
    
    id: uuid.UUID = Field(..., description="Task ID")
    instance_id: uuid.UUID = Field(..., description="Model instance ID")
    model_id: str = Field(..., description="Model ID")
    status: str = Field(..., description="Task status")
    progress: float = Field(..., description="Progress percentage")
    download_url: str = Field(..., description="Download URL")
    destination_path: str = Field(..., description="Destination file path")
    total_size: Optional[int] = Field(None, description="Total download size in bytes")
    downloaded_size: int = Field(..., description="Downloaded size in bytes")
    download_speed: Optional[float] = Field(None, description="Download speed in bytes/second")
    started_at: Optional[datetime] = Field(None, description="Task start timestamp")
    completed_at: Optional[datetime] = Field(None, description="Task completion timestamp")
    estimated_completion: Optional[datetime] = Field(None, description="Estimated completion timestamp")
    error_message: Optional[str] = Field(None, description="Error message if failed")
    retry_count: int = Field(..., description="Number of retry attempts")
    max_retries: int = Field(..., description="Maximum retry attempts")
    created_by: uuid.UUID = Field(..., description="Creator user ID")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    class Config:
        """Pydantic configuration."""
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            uuid.UUID: lambda v: str(v),
        }

    @classmethod
    def from_domain(cls, task: ModelDownloadTask) -> "ModelDownloadTaskResponse":
        """
        Create response schema from domain entity.

        Args:
            task: ModelDownloadTask domain entity

        Returns:
            ModelDownloadTaskResponse instance
        """
        return cls(
            id=task.id,
            instance_id=task.instance_id,
            model_id=task.model_id,
            status=task.status,
            progress=task.progress,
            download_url=task.download_url,
            destination_path=task.destination_path,
            total_size=task.total_size,
            downloaded_size=task.downloaded_size,
            download_speed=task.download_speed,
            started_at=task.started_at,
            completed_at=task.completed_at,
            estimated_completion=task.estimated_completion,
            error_message=task.error_message,
            retry_count=task.retry_count,
            max_retries=task.max_retries,
            created_by=task.created_by,
            created_at=task.created_at,
            updated_at=task.updated_at,
        )


# Serving schemas
class ModelServingRequest(BaseModel):
    """Request schema for starting model serving."""
    
    port: Optional[int] = Field(None, ge=1, le=65535, description="Specific port to use")
    configuration: Optional[Dict[str, Any]] = Field(None, description="Serving configuration")

    class Config:
        """Pydantic configuration."""
        schema_extra = {
            "example": {
                "port": 8000,
                "configuration": {
                    "max_concurrent_requests": 10,
                    "timeout_seconds": 30
                }
            }
        }


class ModelHealthResponse(BaseModel):
    """Response schema for model health check."""
    
    status: str = Field(..., description="Health status")
    message: Optional[str] = Field(None, description="Status message")
    response_time_ms: Optional[str] = Field(None, description="Response time in milliseconds")
    memory_usage_mb: Optional[float] = Field(None, description="Memory usage in MB")
    gpu_memory_usage_mb: Optional[float] = Field(None, description="GPU memory usage in MB")
    uptime_seconds: Optional[float] = Field(None, description="Uptime in seconds")
    requests_served: Optional[int] = Field(None, description="Total requests served")
    errors_count: Optional[int] = Field(None, description="Total errors count")

    class Config:
        """Pydantic configuration."""
        schema_extra = {
            "example": {
                "status": "healthy",
                "message": "Model is serving normally",
                "response_time_ms": "150",
                "memory_usage_mb": 2048.5,
                "gpu_memory_usage_mb": 1024.0,
                "uptime_seconds": 3600.0,
                "requests_served": 1250,
                "errors_count": 2
            }
        }
