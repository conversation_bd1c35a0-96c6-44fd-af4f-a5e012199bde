import { apiClient, API_ENDPOINTS } from '@/shared/lib/api';
import { Agent, AgentStatus, AgentType } from '@/shared/types';

export interface AgentListParams {
  status?: AgentStatus;
  agent_type?: AgentType;
  include_public?: boolean;
  limit?: number;
  offset?: number;
}

export interface AgentSearchParams {
  query: string;
  include_public?: boolean;
  limit?: number;
  offset?: number;
}

export interface AgentChatParams {
  message: string;
  conversation_id?: string;
  context?: Record<string, any>;
}

export interface AgentChatResponse {
  conversation_id: string;
  message_id: string;
  response: string;
  tokens_used: number;
  cost: number;
  response_time: number;
}

export interface AgentMetricsSummary {
  total_agents: number;
  active_agents: number;
  total_executions: number;
  total_cost: number;
  total_tokens_used: number;
  average_success_rate: number;
}

export interface AgentListResponse {
  agents: Agent[];
  total: number;
  limit: number;
  offset: number;
}

export interface AgentCreateRequest {
  name: string;
  description: string;
  agent_type: AgentType;
  version?: string;
  configuration: {
    model_name: string;
    temperature: number;
    max_tokens: number;
    timeout_seconds: number;
    retry_attempts: number;
    memory_enabled: boolean;
    tools_enabled: boolean;
    custom_settings: Record<string, any>;
  };
  capabilities: Array<{
    name: string;
    description: string;
    parameters: Record<string, any>;
    required: boolean;
  }>;
  tags: string[];
  is_public: boolean;
}

export interface AgentUpdateRequest {
  name?: string;
  description?: string;
  agent_type?: AgentType;
  version?: string;
  configuration?: Partial<AgentCreateRequest['configuration']>;
  capabilities?: AgentCreateRequest['capabilities'];
  tags?: string[];
  is_public?: boolean;
}

export const agentService = {
  // Get all agents
  async getAgents(params: AgentListParams = {}): Promise<AgentListResponse> {
    const searchParams = new URLSearchParams();
    
    if (params.status) searchParams.append('agent_status', params.status);
    if (params.agent_type) searchParams.append('agent_type', params.agent_type);
    if (params.include_public !== undefined) searchParams.append('include_public', params.include_public.toString());
    if (params.limit) searchParams.append('limit', params.limit.toString());
    if (params.offset) searchParams.append('offset', params.offset.toString());

    const query = searchParams.toString();
    const endpoint = query ? `${API_ENDPOINTS.AGENTS.BASE}?${query}` : API_ENDPOINTS.AGENTS.BASE;
    
    const response = await apiClient.get<AgentListResponse>(endpoint);
    return response.data;
  },

  // Search agents
  async searchAgents(params: AgentSearchParams): Promise<AgentListResponse> {
    const searchParams = new URLSearchParams();
    searchParams.append('query', params.query);
    if (params.include_public !== undefined) searchParams.append('include_public', params.include_public.toString());
    if (params.limit) searchParams.append('limit', params.limit.toString());
    if (params.offset) searchParams.append('offset', params.offset.toString());

    const endpoint = `${API_ENDPOINTS.AGENTS.SEARCH}?${searchParams.toString()}`;
    const response = await apiClient.get<AgentListResponse>(endpoint);
    return response.data;
  },

  // Get agent by ID
  async getAgent(id: string): Promise<Agent> {
    const response = await apiClient.get<Agent>(API_ENDPOINTS.AGENTS.BY_ID(id));
    return response.data;
  },

  // Create new agent
  async createAgent(agent: AgentCreateRequest): Promise<Agent> {
    const response = await apiClient.post<Agent>(API_ENDPOINTS.AGENTS.BASE, agent);
    return response.data;
  },

  // Update agent
  async updateAgent(id: string, updates: AgentUpdateRequest): Promise<Agent> {
    const response = await apiClient.put<Agent>(API_ENDPOINTS.AGENTS.BY_ID(id), updates);
    return response.data;
  },

  // Delete agent
  async deleteAgent(id: string): Promise<void> {
    await apiClient.delete(API_ENDPOINTS.AGENTS.BY_ID(id));
  },

  // Activate agent
  async activateAgent(id: string): Promise<Agent> {
    const response = await apiClient.post<Agent>(API_ENDPOINTS.AGENTS.ACTIVATE(id));
    return response.data;
  },

  // Deactivate agent
  async deactivateAgent(id: string): Promise<Agent> {
    const response = await apiClient.post<Agent>(API_ENDPOINTS.AGENTS.DEACTIVATE(id));
    return response.data;
  },

  // Chat with agent
  async chatWithAgent(id: string, params: AgentChatParams): Promise<AgentChatResponse> {
    const response = await apiClient.post<AgentChatResponse>(
      API_ENDPOINTS.AGENTS.CHAT(id),
      params
    );
    return response.data;
  },

  // Get agent metrics summary
  async getAgentMetrics(): Promise<AgentMetricsSummary> {
    const response = await apiClient.get<AgentMetricsSummary>(API_ENDPOINTS.AGENTS.METRICS);
    return response.data;
  },
};
