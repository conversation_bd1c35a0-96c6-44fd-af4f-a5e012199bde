"""
Workflow API schemas.

This module contains Pydantic schemas for workflow API requests and responses.
"""

import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field

from src.domain.entities.workflow import (
    ExecutionStatus,
    NodeType,
    WorkflowComplexity,
    WorkflowStatus,
)


# Base schemas
class WorkflowNodeSchema(BaseModel):
    """Schema for workflow nodes."""
    
    id: str = Field(..., description="Unique node identifier")
    type: NodeType = Field(..., description="Node type")
    position: Dict[str, float] = Field(..., description="Node position (x, y)")
    data: Dict[str, Any] = Field(default_factory=dict, description="Node configuration data")


class WorkflowEdgeSchema(BaseModel):
    """Schema for workflow edges."""
    
    id: str = Field(..., description="Unique edge identifier")
    source: str = Field(..., description="Source node ID")
    target: str = Field(..., description="Target node ID")
    source_handle: Optional[str] = Field(None, description="Source handle ID")
    target_handle: Optional[str] = Field(None, description="Target handle ID")
    data: Dict[str, Any] = Field(default_factory=dict, description="Edge configuration data")


class WorkflowMetadataSchema(BaseModel):
    """Schema for workflow metadata."""
    
    category: Optional[str] = Field(None, description="Workflow category")
    complexity: WorkflowComplexity = Field(default=WorkflowComplexity.SIMPLE, description="Workflow complexity")
    estimated_duration: Optional[int] = Field(None, description="Estimated execution duration in seconds")
    required_capabilities: List[str] = Field(default_factory=list, description="Required agent capabilities")
    tags: List[str] = Field(default_factory=list, description="Workflow tags")


class WorkflowPerformanceSchema(BaseModel):
    """Schema for workflow performance metrics."""
    
    total_executions: int = Field(default=0, description="Total number of executions")
    successful_executions: int = Field(default=0, description="Number of successful executions")
    failed_executions: int = Field(default=0, description="Number of failed executions")
    average_execution_time: float = Field(default=0.0, description="Average execution time in seconds")
    last_execution_time: Optional[datetime] = Field(None, description="Last execution timestamp")
    total_cost: float = Field(default=0.0, description="Total execution cost")


# Request schemas
class WorkflowCreateRequest(BaseModel):
    """Schema for workflow creation requests."""
    
    name: str = Field(..., min_length=1, max_length=255, description="Workflow name")
    description: Optional[str] = Field(None, max_length=1000, description="Workflow description")
    nodes: List[WorkflowNodeSchema] = Field(default_factory=list, description="Workflow nodes")
    edges: List[WorkflowEdgeSchema] = Field(default_factory=list, description="Workflow edges")
    version: str = Field(default="1.0.0", description="Workflow version")
    status: WorkflowStatus = Field(default=WorkflowStatus.DRAFT, description="Workflow status")
    is_public: bool = Field(default=False, description="Whether workflow is public")
    metadata: WorkflowMetadataSchema = Field(default_factory=WorkflowMetadataSchema, description="Workflow metadata")


class WorkflowUpdateRequest(BaseModel):
    """Schema for workflow update requests."""
    
    name: Optional[str] = Field(None, min_length=1, max_length=255, description="Workflow name")
    description: Optional[str] = Field(None, max_length=1000, description="Workflow description")
    nodes: Optional[List[WorkflowNodeSchema]] = Field(None, description="Workflow nodes")
    edges: Optional[List[WorkflowEdgeSchema]] = Field(None, description="Workflow edges")
    version: Optional[str] = Field(None, description="Workflow version")
    status: Optional[WorkflowStatus] = Field(None, description="Workflow status")
    is_public: Optional[bool] = Field(None, description="Whether workflow is public")
    metadata: Optional[WorkflowMetadataSchema] = Field(None, description="Workflow metadata")


class WorkflowExecuteRequest(BaseModel):
    """Schema for workflow execution requests."""
    
    input_data: Dict[str, Any] = Field(default_factory=dict, description="Input data for workflow execution")


# Response schemas
class WorkflowResponse(BaseModel):
    """Schema for workflow responses."""
    
    id: uuid.UUID = Field(..., description="Unique workflow identifier")
    name: str = Field(..., description="Workflow name")
    description: Optional[str] = Field(None, description="Workflow description")
    nodes: List[WorkflowNodeSchema] = Field(..., description="Workflow nodes")
    edges: List[WorkflowEdgeSchema] = Field(..., description="Workflow edges")
    version: str = Field(..., description="Workflow version")
    status: WorkflowStatus = Field(..., description="Workflow status")
    is_public: bool = Field(..., description="Whether workflow is public")
    owner_id: uuid.UUID = Field(..., description="Workflow owner user ID")
    metadata: WorkflowMetadataSchema = Field(..., description="Workflow metadata")
    performance: WorkflowPerformanceSchema = Field(..., description="Performance metrics")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    class Config:
        """Pydantic configuration."""
        from_attributes = True


class WorkflowListResponse(BaseModel):
    """Schema for workflow list responses."""
    
    workflows: List[WorkflowResponse] = Field(..., description="List of workflows")
    total: int = Field(..., description="Total number of workflows")
    limit: int = Field(..., description="Limit used for pagination")
    offset: int = Field(..., description="Offset used for pagination")


class WorkflowExecutionResponse(BaseModel):
    """Schema for workflow execution responses."""
    
    id: uuid.UUID = Field(..., description="Unique execution identifier")
    workflow_id: uuid.UUID = Field(..., description="Associated workflow ID")
    user_id: uuid.UUID = Field(..., description="User who initiated execution")
    status: ExecutionStatus = Field(..., description="Execution status")
    input_data: Dict[str, Any] = Field(..., description="Execution input data")
    output_data: Dict[str, Any] = Field(..., description="Execution output data")
    error_data: Optional[Dict[str, Any]] = Field(None, description="Error information if failed")
    current_node_id: Optional[str] = Field(None, description="Currently executing node ID")
    completed_nodes: List[str] = Field(..., description="List of completed node IDs")
    execution_log: List[Dict[str, Any]] = Field(..., description="Execution log entries")
    execution_time: Optional[float] = Field(None, description="Total execution time in seconds")
    cost: float = Field(..., description="Execution cost")
    started_at: Optional[datetime] = Field(None, description="Execution start timestamp")
    completed_at: Optional[datetime] = Field(None, description="Execution completion timestamp")
    created_at: datetime = Field(..., description="Creation timestamp")

    class Config:
        """Pydantic configuration."""
        from_attributes = True


class WorkflowExecutionListResponse(BaseModel):
    """Schema for workflow execution list responses."""
    
    executions: List[WorkflowExecutionResponse] = Field(..., description="List of executions")
    total: int = Field(..., description="Total number of executions")
    limit: int = Field(..., description="Limit used for pagination")
    offset: int = Field(..., description="Offset used for pagination")


# Query parameter schemas
class WorkflowListParams(BaseModel):
    """Schema for workflow list query parameters."""
    
    status: Optional[WorkflowStatus] = Field(None, description="Filter by workflow status")
    include_public: bool = Field(True, description="Include public workflows")
    limit: int = Field(100, ge=1, le=1000, description="Maximum number of workflows to return")
    offset: int = Field(0, ge=0, description="Number of workflows to skip")


class WorkflowSearchParams(BaseModel):
    """Schema for workflow search query parameters."""
    
    query: str = Field(..., min_length=1, description="Search query string")
    include_public: bool = Field(True, description="Include public workflows")
    limit: int = Field(100, ge=1, le=1000, description="Maximum number of workflows to return")
    offset: int = Field(0, ge=0, description="Number of workflows to skip")


class WorkflowExecutionListParams(BaseModel):
    """Schema for workflow execution list query parameters."""
    
    workflow_id: Optional[uuid.UUID] = Field(None, description="Filter by workflow ID")
    limit: int = Field(100, ge=1, le=1000, description="Maximum number of executions to return")
    offset: int = Field(0, ge=0, description="Number of executions to skip")


# Error response schemas
class WorkflowErrorResponse(BaseModel):
    """Schema for workflow error responses."""
    
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")


class WorkflowValidationErrorResponse(BaseModel):
    """Schema for workflow validation error responses."""
    
    error: str = Field(default="validation_error", description="Error type")
    message: str = Field(..., description="Error message")
    validation_errors: List[str] = Field(..., description="List of validation errors")


# Success response schemas
class WorkflowDeleteResponse(BaseModel):
    """Schema for workflow deletion responses."""
    
    success: bool = Field(True, description="Whether deletion was successful")
    message: str = Field(default="Workflow deleted successfully", description="Success message")


class WorkflowExecutionDeleteResponse(BaseModel):
    """Schema for workflow execution deletion responses."""
    
    success: bool = Field(True, description="Whether deletion was successful")
    message: str = Field(default="Execution deleted successfully", description="Success message")
