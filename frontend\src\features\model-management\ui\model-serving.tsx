'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Play, Square, Activity, Cpu, HardDrive, Zap, Globe, AlertTriangle, CheckCircle, Clock } from 'lucide-react';
import { useModelHealth, getStatusColor, formatBytes } from '@/entities/model-management/api';
import { ModelInstance, ModelHealth, ModelStatus } from '@/shared/types';
import { Button } from '@/shared/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/ui/card';
import { Badge } from '@/shared/ui/badge';
import { Progress } from '@/shared/ui/progress';
import { Alert, AlertDescription } from '@/shared/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/shared/ui/tabs';
import { useToast } from '@/shared/hooks/use-toast';

interface ModelServingProps {
  instances: ModelInstance[];
  onStartServing?: (instanceId: string, config?: any) => void;
  onStopServing?: (instanceId: string) => void;
  className?: string;
}

export function ModelServing({ instances, onStartServing, onStopServing, className }: ModelServingProps) {
  const [selectedInstance, setSelectedInstance] = useState<ModelInstance | null>(null);
  const { toast } = useToast();

  const servingInstances = instances.filter(instance => 
    instance.status === ModelStatus.SERVING || 
    instance.status === ModelStatus.LOADING ||
    instance.status === ModelStatus.LOADED
  );

  const availableInstances = instances.filter(instance => 
    instance.status === ModelStatus.DOWNLOADED ||
    instance.status === ModelStatus.LOADED
  );

  const handleStartServing = async (instanceId: string) => {
    try {
      await onStartServing?.(instanceId);
      toast({
        title: 'Model Serving Started',
        description: 'The model is now being served.',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to start model serving.',
        variant: 'destructive',
      });
    }
  };

  const handleStopServing = async (instanceId: string) => {
    try {
      await onStopServing?.(instanceId);
      toast({
        title: 'Model Serving Stopped',
        description: 'The model serving has been stopped.',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to stop model serving.',
        variant: 'destructive',
      });
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Model Serving</h2>
          <p className="text-muted-foreground">
            Manage and monitor your serving model instances
          </p>
        </div>
        <Badge variant="outline">
          {servingInstances.length} serving
        </Badge>
      </div>

      <Tabs defaultValue="serving" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="serving">Serving Models ({servingInstances.length})</TabsTrigger>
          <TabsTrigger value="available">Available Models ({availableInstances.length})</TabsTrigger>
        </TabsList>

        <TabsContent value="serving" className="mt-6">
          {servingInstances.length === 0 ? (
            <div className="text-center py-12">
              <Activity className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Models Serving</h3>
              <p className="text-muted-foreground mb-4">
                Start serving a model to see it here
              </p>
            </div>
          ) : (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              <AnimatePresence>
                {servingInstances.map((instance) => (
                  <motion.div
                    key={instance.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.2 }}
                  >
                    <ServingModelCard
                      instance={instance}
                      onStop={() => handleStopServing(instance.id)}
                      onSelect={() => setSelectedInstance(instance)}
                    />
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>
          )}
        </TabsContent>

        <TabsContent value="available" className="mt-6">
          {availableInstances.length === 0 ? (
            <div className="text-center py-12">
              <HardDrive className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Models Available</h3>
              <p className="text-muted-foreground mb-4">
                Download some models to start serving them
              </p>
            </div>
          ) : (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              <AnimatePresence>
                {availableInstances.map((instance) => (
                  <motion.div
                    key={instance.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.2 }}
                  >
                    <AvailableModelCard
                      instance={instance}
                      onStart={() => handleStartServing(instance.id)}
                    />
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Model Details Modal */}
      {selectedInstance && (
        <ModelInstanceDetails
          instance={selectedInstance}
          onClose={() => setSelectedInstance(null)}
        />
      )}
    </div>
  );
}

interface ServingModelCardProps {
  instance: ModelInstance;
  onStop: () => void;
  onSelect: () => void;
}

function ServingModelCard({ instance, onStop, onSelect }: ServingModelCardProps) {
  const { health, loading: healthLoading } = useModelHealth(instance.id);

  const getHealthIcon = (health: ModelHealth | null) => {
    if (!health) return <Clock className="h-4 w-4 text-gray-500" />;
    
    switch (health.status) {
      case 'healthy':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'unhealthy':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'error':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getHealthColor = (health: ModelHealth | null) => {
    if (!health) return 'gray';
    
    switch (health.status) {
      case 'healthy':
        return 'green';
      case 'unhealthy':
        return 'red';
      case 'error':
        return 'red';
      default:
        return 'yellow';
    }
  };

  return (
    <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={onSelect}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              {getHealthIcon(health)}
              <div>
                <CardTitle className="text-base">{instance.model_id}</CardTitle>
                <p className="text-sm text-muted-foreground">
                  Port {instance.serving_port}
                </p>
              </div>
            </div>
          </div>
          
          <Button 
            variant="outline" 
            size="sm" 
            onClick={(e) => {
              e.stopPropagation();
              onStop();
            }}
          >
            <Square className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Badge variant="outline" className={`text-${getStatusColor(instance.status)}-600`}>
              {instance.status}
            </Badge>
            {health && (
              <Badge variant="outline" className={`text-${getHealthColor(health)}-600`}>
                {health.status}
              </Badge>
            )}
          </div>
          
          {/* Performance Metrics */}
          <div className="space-y-2">
            {instance.memory_usage_mb && (
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center gap-1">
                  <Cpu className="h-3 w-3" />
                  RAM
                </div>
                <span>{formatBytes(instance.memory_usage_mb * 1024 * 1024)}</span>
              </div>
            )}
            
            {instance.gpu_memory_usage_mb && (
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center gap-1">
                  <Zap className="h-3 w-3" />
                  GPU
                </div>
                <span>{formatBytes(instance.gpu_memory_usage_mb * 1024 * 1024)}</span>
              </div>
            )}
            
            {health?.requests_served && (
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center gap-1">
                  <Activity className="h-3 w-3" />
                  Requests
                </div>
                <span>{health.requests_served.toLocaleString()}</span>
              </div>
            )}
          </div>

          {/* API Endpoint */}
          {instance.api_endpoint && (
            <div className="text-xs text-muted-foreground">
              <div className="flex items-center gap-1">
                <Globe className="h-3 w-3" />
                {instance.api_endpoint}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

interface AvailableModelCardProps {
  instance: ModelInstance;
  onStart: () => void;
}

function AvailableModelCard({ instance, onStart }: AvailableModelCardProps) {
  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-base">{instance.model_id}</CardTitle>
            <p className="text-sm text-muted-foreground">
              Ready to serve
            </p>
          </div>
          
          <Button size="sm" onClick={onStart}>
            <Play className="h-4 w-4 mr-2" />
            Start
          </Button>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-3">
          <Badge variant="outline" className={`text-${getStatusColor(instance.status)}-600`}>
            {instance.status}
          </Badge>
          
          {instance.local_path && (
            <div className="text-xs text-muted-foreground">
              <div className="flex items-center gap-1">
                <HardDrive className="h-3 w-3" />
                {instance.local_path}
              </div>
            </div>
          )}
          
          {instance.last_used_at && (
            <div className="text-xs text-muted-foreground">
              Last used: {new Date(instance.last_used_at).toLocaleDateString()}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

interface ModelInstanceDetailsProps {
  instance: ModelInstance;
  onClose: () => void;
}

function ModelInstanceDetails({ instance, onClose }: ModelInstanceDetailsProps) {
  const { health } = useModelHealth(instance.id);

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-background rounded-lg shadow-lg max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto"
      >
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold">{instance.model_id}</h2>
            <Button variant="ghost" onClick={onClose}>
              ×
            </Button>
          </div>

          <div className="space-y-6">
            {/* Status and Health */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="font-semibold mb-2">Status</h3>
                <Badge variant="outline" className={`text-${getStatusColor(instance.status)}-600`}>
                  {instance.status}
                </Badge>
              </div>
              
              {health && (
                <div>
                  <h3 className="font-semibold mb-2">Health</h3>
                  <Badge variant="outline" className={`text-${health.status === 'healthy' ? 'green' : 'red'}-600`}>
                    {health.status}
                  </Badge>
                </div>
              )}
            </div>

            {/* Performance Metrics */}
            {(instance.memory_usage_mb || instance.gpu_memory_usage_mb || instance.load_time_seconds) && (
              <div>
                <h3 className="font-semibold mb-3">Performance</h3>
                <div className="grid grid-cols-2 gap-4">
                  {instance.memory_usage_mb && (
                    <div>
                      <div className="text-sm text-muted-foreground">Memory Usage</div>
                      <div className="font-medium">{formatBytes(instance.memory_usage_mb * 1024 * 1024)}</div>
                    </div>
                  )}
                  
                  {instance.gpu_memory_usage_mb && (
                    <div>
                      <div className="text-sm text-muted-foreground">GPU Memory</div>
                      <div className="font-medium">{formatBytes(instance.gpu_memory_usage_mb * 1024 * 1024)}</div>
                    </div>
                  )}
                  
                  {instance.load_time_seconds && (
                    <div>
                      <div className="text-sm text-muted-foreground">Load Time</div>
                      <div className="font-medium">{instance.load_time_seconds.toFixed(2)}s</div>
                    </div>
                  )}
                  
                  {health?.response_time_ms && (
                    <div>
                      <div className="text-sm text-muted-foreground">Response Time</div>
                      <div className="font-medium">{health.response_time_ms}ms</div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Serving Information */}
            {instance.serving_url && (
              <div>
                <h3 className="font-semibold mb-3">Serving</h3>
                <div className="space-y-2">
                  <div>
                    <div className="text-sm text-muted-foreground">Serving URL</div>
                    <div className="font-mono text-sm">{instance.serving_url}</div>
                  </div>
                  
                  {instance.api_endpoint && (
                    <div>
                      <div className="text-sm text-muted-foreground">API Endpoint</div>
                      <div className="font-mono text-sm">{instance.api_endpoint}</div>
                    </div>
                  )}
                  
                  <div>
                    <div className="text-sm text-muted-foreground">Port</div>
                    <div className="font-medium">{instance.serving_port}</div>
                  </div>
                </div>
              </div>
            )}

            {/* Configuration */}
            {Object.keys(instance.configuration).length > 0 && (
              <div>
                <h3 className="font-semibold mb-3">Configuration</h3>
                <div className="bg-muted rounded-lg p-4">
                  <pre className="text-sm overflow-x-auto">
                    {JSON.stringify(instance.configuration, null, 2)}
                  </pre>
                </div>
              </div>
            )}

            {/* Error Information */}
            {instance.error_message && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  {instance.error_message}
                </AlertDescription>
              </Alert>
            )}
          </div>
        </div>
      </motion.div>
    </div>
  );
}
