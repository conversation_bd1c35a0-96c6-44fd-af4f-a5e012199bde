"""
Enhanced MCP Service Tests

Comprehensive test suite for the enhanced MCP service with
actual model inference capabilities.
"""

import asyncio
import uuid
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
import httpx

from src.application.use_cases.mcp_service import MCPService
from src.domain.entities.mcp import (
    MCPContextCreate,
    MCPRequest,
    ContextType,
    ModelStatus,
    ModelType,
)


@pytest.fixture
def mcp_service():
    """Create MCP service instance for testing."""
    return MCPService()


@pytest.fixture
def sample_user_id():
    """Sample user ID for testing."""
    return uuid.uuid4()


@pytest.fixture
def sample_context_data():
    """Sample context creation data."""
    return MCPContextCreate(
        type=ContextType.CONVERSATION,
        title="Test Context",
        description="Test context for unit testing",
        model_id="gpt-4",
        max_length=4096,
        expires_in_hours=24,
        metadata={"test": True},
    )


@pytest.fixture
def sample_request():
    """Sample MCP request."""
    return MCPRequest(
        model_id="gpt-4",
        messages=[
            {"role": "user", "content": "Hello, how are you?"},
            {"role": "assistant", "content": "I'm doing well, thank you!"},
            {"role": "user", "content": "Can you help me with a coding problem?"},
        ],
        max_tokens=500,
        temperature=0.7,
        metadata={"test_request": True},
    )


class TestEnhancedMCPService:
    """Test suite for enhanced MCP service."""

    @pytest.mark.asyncio
    async def test_list_models_includes_sample_models(self, mcp_service):
        """Test that list_models returns predefined sample models."""
        models = await mcp_service.list_models()
        
        assert len(models) >= 3
        model_ids = [model.id for model in models]
        assert "gpt-4" in model_ids
        assert "claude-3" in model_ids
        assert "codellama" in model_ids
        
        # Check model types are correct
        gpt4_model = next(m for m in models if m.id == "gpt-4")
        assert gpt4_model.type == ModelType.TEXT_GENERATION
        assert gpt4_model.status == ModelStatus.AVAILABLE

    @pytest.mark.asyncio
    async def test_get_model_by_id(self, mcp_service):
        """Test retrieving specific model by ID."""
        model = await mcp_service.get_model("gpt-4")
        
        assert model is not None
        assert model.id == "gpt-4"
        assert model.name == "GPT-4"
        assert model.provider == "OpenAI"
        assert model.type == ModelType.TEXT_GENERATION

    @pytest.mark.asyncio
    async def test_get_nonexistent_model(self, mcp_service):
        """Test retrieving non-existent model returns None."""
        model = await mcp_service.get_model("nonexistent-model")
        assert model is None

    @pytest.mark.asyncio
    async def test_create_context_success(self, mcp_service, sample_user_id, sample_context_data):
        """Test successful context creation."""
        context = await mcp_service.create_context(sample_user_id, sample_context_data)
        
        assert context.user_id == sample_user_id
        assert context.title == sample_context_data.title
        assert context.model_id == sample_context_data.model_id
        assert context.max_length == sample_context_data.max_length
        assert not context.is_expired
        assert not context.is_full

    @pytest.mark.asyncio
    async def test_create_context_invalid_model(self, mcp_service, sample_user_id):
        """Test context creation with invalid model ID."""
        invalid_context_data = MCPContextCreate(
            type=ContextType.CONVERSATION,
            title="Invalid Context",
            model_id="invalid-model",
        )
        
        with pytest.raises(ValueError, match="Model not found"):
            await mcp_service.create_context(sample_user_id, invalid_context_data)

    @pytest.mark.asyncio
    async def test_process_request_mock_inference(self, mcp_service, sample_user_id, sample_request):
        """Test request processing with mock inference."""
        response = await mcp_service.process_request(sample_user_id, sample_request)
        
        assert response.model_id == sample_request.model_id
        assert response.content is not None
        assert len(response.content) > 0
        assert response.usage["prompt_tokens"] > 0
        assert response.usage["completion_tokens"] > 0
        assert response.usage["total_tokens"] > 0

    @pytest.mark.asyncio
    async def test_process_request_invalid_model(self, mcp_service, sample_user_id):
        """Test request processing with invalid model."""
        invalid_request = MCPRequest(
            model_id="invalid-model",
            messages=[{"role": "user", "content": "Test"}],
        )
        
        with pytest.raises(ValueError, match="Model not found"):
            await mcp_service.process_request(sample_user_id, invalid_request)

    @pytest.mark.asyncio
    @patch('httpx.AsyncClient')
    async def test_ollama_inference_success(self, mock_client, mcp_service, sample_user_id):
        """Test successful Ollama inference."""
        # Mock successful Ollama response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "message": {"content": "Hello! I'm an AI assistant."}
        }
        
        mock_client_instance = AsyncMock()
        mock_client_instance.post.return_value = mock_response
        mock_client.return_value.__aenter__.return_value = mock_client_instance
        
        # Create Ollama model
        mcp_service._models["ollama-test"] = type(mcp_service._models["gpt-4"])(
            id="ollama-test",
            name="Ollama Test",
            type=ModelType.TEXT_GENERATION,
            provider="ollama",
            version="test",
            status=ModelStatus.AVAILABLE,
            max_context_length=4096,
            capabilities=["text_generation"],
            metadata={},
        )
        
        request = MCPRequest(
            model_id="ollama-test",
            messages=[{"role": "user", "content": "Hello"}],
        )
        
        response = await mcp_service.process_request(sample_user_id, request)
        
        assert response.content == "Hello! I'm an AI assistant."
        assert response.model_id == "ollama-test"

    @pytest.mark.asyncio
    @patch('httpx.AsyncClient')
    async def test_ollama_inference_failure_fallback(self, mock_client, mcp_service, sample_user_id):
        """Test Ollama inference failure falls back to mock."""
        # Mock failed Ollama response
        mock_client_instance = AsyncMock()
        mock_client_instance.post.side_effect = Exception("Connection failed")
        mock_client.return_value.__aenter__.return_value = mock_client_instance
        
        # Create Ollama model
        mcp_service._models["ollama-fail"] = type(mcp_service._models["gpt-4"])(
            id="ollama-fail",
            name="Ollama Fail",
            type=ModelType.TEXT_GENERATION,
            provider="ollama",
            version="test",
            status=ModelStatus.AVAILABLE,
            max_context_length=4096,
            capabilities=["text_generation"],
            metadata={},
        )
        
        request = MCPRequest(
            model_id="ollama-fail",
            messages=[{"role": "user", "content": "Hello"}],
        )
        
        response = await mcp_service.process_request(sample_user_id, request)
        
        # Should fallback to mock response
        assert "mock response" in response.content.lower()
        assert response.model_id == "ollama-fail"

    @pytest.mark.asyncio
    async def test_code_generation_mock_response(self, mcp_service, sample_user_id):
        """Test code generation produces appropriate mock response."""
        request = MCPRequest(
            model_id="codellama",
            messages=[{"role": "user", "content": "Write a Python function"}],
        )
        
        response = await mcp_service.process_request(sample_user_id, request)
        
        assert "def example_function" in response.content
        assert "Generated by Code Llama" in response.content
        assert response.model_id == "codellama"

    @pytest.mark.asyncio
    async def test_context_workflow(self, mcp_service, sample_user_id, sample_context_data):
        """Test complete context workflow: create, use, update, delete."""
        # Create context
        context = await mcp_service.create_context(sample_user_id, sample_context_data)
        context_id = context.id
        
        # Use context in request
        request = MCPRequest(
            context_id=context_id,
            model_id="gpt-4",
            messages=[{"role": "user", "content": "Hello"}],
        )
        
        response = await mcp_service.process_request(sample_user_id, request)
        assert response.context_id == context_id
        
        # Update context
        from src.domain.entities.mcp import MCPContextUpdate
        update_data = MCPContextUpdate(
            title="Updated Context",
            description="Updated description",
        )
        
        updated_context = await mcp_service.update_context(context_id, sample_user_id, update_data)
        assert updated_context is not None
        assert updated_context.title == "Updated Context"
        
        # Delete context
        deleted = await mcp_service.delete_context(context_id, sample_user_id)
        assert deleted is True
        
        # Verify context is gone
        retrieved_context = await mcp_service.get_context(context_id, sample_user_id)
        assert retrieved_context is None

    @pytest.mark.asyncio
    async def test_performance_timing(self, mcp_service, sample_user_id, sample_request):
        """Test that inference timing is recorded."""
        import time
        
        start_time = time.time()
        response = await mcp_service.process_request(sample_user_id, sample_request)
        end_time = time.time()
        
        # Should complete within reasonable time (mock inference)
        assert end_time - start_time < 1.0  # Less than 1 second for mock
        assert response.content is not None
