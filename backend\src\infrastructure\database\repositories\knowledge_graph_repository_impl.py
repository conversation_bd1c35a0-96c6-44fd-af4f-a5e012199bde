"""
Knowledge Graph repository implementations.

This module contains the SQLAlchemy implementations of the knowledge graph repositories.
"""

import uuid

from sqlalchemy import and_, or_, text
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload

from src.domain.entities.knowledge_graph import (
    EntityType,
    KnowledgeEntity,
    KnowledgeGraph,
    KnowledgeRelationship,
    RelationType,
)
from src.domain.repositories.knowledge_graph_repository import (
    KnowledgeEntityRepository,
    KnowledgeGraphRepository,
    KnowledgeRelationshipRepository,
)
from src.infrastructure.database.models.knowledge_graph import (
    KnowledgeEntityModel,
    KnowledgeRelationshipModel,
)
from src.infrastructure.logging.setup import get_logger

logger = get_logger(__name__)


class KnowledgeEntityRepositoryImpl(KnowledgeEntityRepository):
    """SQLAlchemy implementation of knowledge entity repository."""

    def __init__(self, session: AsyncSession) -> None:
        """
        Initialize repository with database session.

        Args:
            session: SQLAlchemy async session
        """
        self.session = session

    async def create(self, entity: KnowledgeEntity) -> KnowledgeEntity:
        """Create a new knowledge entity."""
        try:
            entity_model = KnowledgeEntityModel.from_domain(entity)
            self.session.add(entity_model)
            await self.session.commit()
            await self.session.refresh(entity_model)

            logger.info(f"Created knowledge entity {entity_model.id}")
            return entity_model.to_domain()

        except Exception as e:
            await self.session.rollback()
            logger.error(f"Failed to create knowledge entity: {e}")
            raise

    async def get_by_id(self, entity_id: uuid.UUID) -> KnowledgeEntity | None:
        """Get a knowledge entity by ID."""
        try:
            stmt = (
                select(KnowledgeEntityModel)
                .options(selectinload(KnowledgeEntityModel.creator))
                .where(KnowledgeEntityModel.id == entity_id)
            )
            result = await self.session.execute(stmt)
            entity_model = result.scalar_one_or_none()

            return entity_model.to_domain() if entity_model else None

        except Exception as e:
            logger.error(f"Failed to get knowledge entity {entity_id}: {e}")
            raise

    async def get_by_name(
        self, name: str, user_id: uuid.UUID
    ) -> KnowledgeEntity | None:
        """Get a knowledge entity by name for a specific user."""
        try:
            stmt = (
                select(KnowledgeEntityModel)
                .options(selectinload(KnowledgeEntityModel.creator))
                .where(
                    and_(
                        KnowledgeEntityModel.name == name,
                        KnowledgeEntityModel.created_by == user_id,
                    )
                )
            )
            result = await self.session.execute(stmt)
            entity_model = result.scalar_one_or_none()

            return entity_model.to_domain() if entity_model else None

        except Exception as e:
            logger.error(f"Failed to get knowledge entity by name {name}: {e}")
            raise

    async def get_by_user(
        self,
        user_id: uuid.UUID,
        entity_type: EntityType | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[KnowledgeEntity]:
        """Get knowledge entities by user ID with optional filtering."""
        try:
            conditions = [KnowledgeEntityModel.created_by == user_id]

            if entity_type:
                conditions.append(KnowledgeEntityModel.entity_type == entity_type)

            stmt = (
                select(KnowledgeEntityModel)
                .options(selectinload(KnowledgeEntityModel.creator))
                .where(and_(*conditions))
                .order_by(KnowledgeEntityModel.updated_at.desc())
                .limit(limit)
                .offset(offset)
            )

            result = await self.session.execute(stmt)
            entity_models = result.scalars().all()

            return [model.to_domain() for model in entity_models]

        except Exception as e:
            logger.error(f"Failed to get knowledge entities for user {user_id}: {e}")
            raise

    async def search(
        self,
        query: str,
        user_id: uuid.UUID | None = None,
        entity_type: EntityType | None = None,
        include_public: bool = True,
        limit: int = 100,
        offset: int = 0,
    ) -> list[KnowledgeEntity]:
        """Search knowledge entities by name, description, or properties."""
        try:
            search_conditions = [
                KnowledgeEntityModel.name.ilike(f"%{query}%"),
                KnowledgeEntityModel.description.ilike(f"%{query}%"),
                KnowledgeEntityModel.tags.op("@>")([query]),
            ]

            conditions = [or_(*search_conditions)]

            if entity_type:
                conditions.append(KnowledgeEntityModel.entity_type == entity_type)

            if user_id and not include_public:
                conditions.append(KnowledgeEntityModel.created_by == user_id)
            elif user_id and include_public:
                conditions.append(
                    or_(
                        KnowledgeEntityModel.created_by == user_id,
                        KnowledgeEntityModel.is_public == True,
                    )
                )
            elif include_public:
                conditions.append(KnowledgeEntityModel.is_public == True)

            stmt = (
                select(KnowledgeEntityModel)
                .options(selectinload(KnowledgeEntityModel.creator))
                .where(and_(*conditions))
                .order_by(KnowledgeEntityModel.updated_at.desc())
                .limit(limit)
                .offset(offset)
            )

            result = await self.session.execute(stmt)
            entity_models = result.scalars().all()

            return [model.to_domain() for model in entity_models]

        except Exception as e:
            logger.error(
                f"Failed to search knowledge entities with query '{query}': {e}"
            )
            raise

    async def get_by_tags(
        self,
        tags: list[str],
        user_id: uuid.UUID | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[KnowledgeEntity]:
        """Get knowledge entities by tags."""
        try:
            conditions = []

            # Use PostgreSQL JSON operators for tag matching
            for tag in tags:
                conditions.append(KnowledgeEntityModel.tags.op("@>")([tag]))

            if user_id:
                conditions.append(
                    or_(
                        KnowledgeEntityModel.created_by == user_id,
                        KnowledgeEntityModel.is_public == True,
                    )
                )
            else:
                conditions.append(KnowledgeEntityModel.is_public == True)

            stmt = (
                select(KnowledgeEntityModel)
                .options(selectinload(KnowledgeEntityModel.creator))
                .where(and_(*conditions))
                .order_by(KnowledgeEntityModel.updated_at.desc())
                .limit(limit)
                .offset(offset)
            )

            result = await self.session.execute(stmt)
            entity_models = result.scalars().all()

            return [model.to_domain() for model in entity_models]

        except Exception as e:
            logger.error(f"Failed to get knowledge entities by tags {tags}: {e}")
            raise

    async def update(self, entity: KnowledgeEntity) -> KnowledgeEntity:
        """Update an existing knowledge entity."""
        try:
            stmt = select(KnowledgeEntityModel).where(
                KnowledgeEntityModel.id == entity.id
            )
            result = await self.session.execute(stmt)
            entity_model = result.scalar_one_or_none()

            if not entity_model:
                raise ValueError(f"Knowledge entity {entity.id} not found")

            # Update fields from domain entity
            updated_model = KnowledgeEntityModel.from_domain(entity)

            # Copy updated fields
            entity_model.name = updated_model.name
            entity_model.description = updated_model.description
            entity_model.entity_type = updated_model.entity_type
            entity_model.properties = updated_model.properties
            entity_model.attributes = updated_model.attributes
            entity_model.tags = updated_model.tags
            entity_model.categories = updated_model.categories
            entity_model.confidence_score = updated_model.confidence_score
            entity_model.source_id = updated_model.source_id
            entity_model.source_type = updated_model.source_type
            entity_model.source_metadata = updated_model.source_metadata
            entity_model.is_public = updated_model.is_public
            entity_model.updated_at = updated_model.updated_at

            await self.session.commit()
            await self.session.refresh(entity_model)

            logger.info(f"Updated knowledge entity {entity.id}")
            return entity_model.to_domain()

        except Exception as e:
            await self.session.rollback()
            logger.error(f"Failed to update knowledge entity {entity.id}: {e}")
            raise

    async def delete(self, entity_id: uuid.UUID) -> bool:
        """Delete a knowledge entity."""
        try:
            stmt = select(KnowledgeEntityModel).where(
                KnowledgeEntityModel.id == entity_id
            )
            result = await self.session.execute(stmt)
            entity_model = result.scalar_one_or_none()

            if not entity_model:
                return False

            await self.session.delete(entity_model)
            await self.session.commit()

            logger.info(f"Deleted knowledge entity {entity_id}")
            return True

        except Exception as e:
            await self.session.rollback()
            logger.error(f"Failed to delete knowledge entity {entity_id}: {e}")
            raise


class KnowledgeRelationshipRepositoryImpl(KnowledgeRelationshipRepository):
    """SQLAlchemy implementation of knowledge relationship repository."""

    def __init__(self, session: AsyncSession) -> None:
        """
        Initialize repository with database session.

        Args:
            session: SQLAlchemy async session
        """
        self.session = session

    async def create(
        self, relationship: KnowledgeRelationship
    ) -> KnowledgeRelationship:
        """Create a new knowledge relationship."""
        try:
            relationship_model = KnowledgeRelationshipModel.from_domain(relationship)
            self.session.add(relationship_model)
            await self.session.commit()
            await self.session.refresh(relationship_model)

            logger.info(f"Created knowledge relationship {relationship_model.id}")
            return relationship_model.to_domain()

        except Exception as e:
            await self.session.rollback()
            logger.error(f"Failed to create knowledge relationship: {e}")
            raise

    async def get_by_id(
        self, relationship_id: uuid.UUID
    ) -> KnowledgeRelationship | None:
        """Get a knowledge relationship by ID."""
        try:
            stmt = (
                select(KnowledgeRelationshipModel)
                .options(
                    selectinload(KnowledgeRelationshipModel.source_entity),
                    selectinload(KnowledgeRelationshipModel.target_entity),
                    selectinload(KnowledgeRelationshipModel.creator),
                )
                .where(KnowledgeRelationshipModel.id == relationship_id)
            )
            result = await self.session.execute(stmt)
            relationship_model = result.scalar_one_or_none()

            return relationship_model.to_domain() if relationship_model else None

        except Exception as e:
            logger.error(f"Failed to get knowledge relationship {relationship_id}: {e}")
            raise

    async def get_by_entities(
        self,
        source_entity_id: uuid.UUID,
        target_entity_id: uuid.UUID | None = None,
        relation_type: RelationType | None = None,
    ) -> list[KnowledgeRelationship]:
        """Get relationships by entity IDs."""
        try:
            conditions = [
                KnowledgeRelationshipModel.source_entity_id == source_entity_id
            ]

            if target_entity_id:
                conditions.append(
                    KnowledgeRelationshipModel.target_entity_id == target_entity_id
                )

            if relation_type:
                conditions.append(
                    KnowledgeRelationshipModel.relation_type == relation_type
                )

            stmt = (
                select(KnowledgeRelationshipModel)
                .options(
                    selectinload(KnowledgeRelationshipModel.source_entity),
                    selectinload(KnowledgeRelationshipModel.target_entity),
                )
                .where(and_(*conditions))
                .order_by(KnowledgeRelationshipModel.weight.desc())
            )

            result = await self.session.execute(stmt)
            relationship_models = result.scalars().all()

            return [model.to_domain() for model in relationship_models]

        except Exception as e:
            logger.error(f"Failed to get relationships by entities: {e}")
            raise

    async def get_entity_relationships(
        self,
        entity_id: uuid.UUID,
        relation_type: RelationType | None = None,
        direction: str | None = None,
    ) -> list[KnowledgeRelationship]:
        """Get all relationships for a specific entity."""
        try:
            conditions = []

            if direction == "outgoing":
                conditions.append(
                    KnowledgeRelationshipModel.source_entity_id == entity_id
                )
            elif direction == "incoming":
                conditions.append(
                    KnowledgeRelationshipModel.target_entity_id == entity_id
                )
            else:
                # Both directions
                conditions.append(
                    or_(
                        KnowledgeRelationshipModel.source_entity_id == entity_id,
                        KnowledgeRelationshipModel.target_entity_id == entity_id,
                    )
                )

            if relation_type:
                conditions.append(
                    KnowledgeRelationshipModel.relation_type == relation_type
                )

            stmt = (
                select(KnowledgeRelationshipModel)
                .options(
                    selectinload(KnowledgeRelationshipModel.source_entity),
                    selectinload(KnowledgeRelationshipModel.target_entity),
                )
                .where(and_(*conditions))
                .order_by(KnowledgeRelationshipModel.weight.desc())
            )

            result = await self.session.execute(stmt)
            relationship_models = result.scalars().all()

            return [model.to_domain() for model in relationship_models]

        except Exception as e:
            logger.error(f"Failed to get entity relationships for {entity_id}: {e}")
            raise

    async def update(
        self, relationship: KnowledgeRelationship
    ) -> KnowledgeRelationship:
        """Update an existing knowledge relationship."""
        try:
            stmt = select(KnowledgeRelationshipModel).where(
                KnowledgeRelationshipModel.id == relationship.id
            )
            result = await self.session.execute(stmt)
            relationship_model = result.scalar_one_or_none()

            if not relationship_model:
                raise ValueError(f"Knowledge relationship {relationship.id} not found")

            # Update fields from domain entity
            updated_model = KnowledgeRelationshipModel.from_domain(relationship)

            # Copy updated fields
            relationship_model.source_entity_id = updated_model.source_entity_id
            relationship_model.target_entity_id = updated_model.target_entity_id
            relationship_model.relation_type = updated_model.relation_type
            relationship_model.properties = updated_model.properties
            relationship_model.weight = updated_model.weight
            relationship_model.confidence_score = updated_model.confidence_score
            relationship_model.is_directed = updated_model.is_directed
            relationship_model.description = updated_model.description
            relationship_model.tags = updated_model.tags
            relationship_model.source_id = updated_model.source_id
            relationship_model.source_type = updated_model.source_type
            relationship_model.source_metadata = updated_model.source_metadata
            relationship_model.updated_at = updated_model.updated_at

            await self.session.commit()
            await self.session.refresh(relationship_model)

            logger.info(f"Updated knowledge relationship {relationship.id}")
            return relationship_model.to_domain()

        except Exception as e:
            await self.session.rollback()
            logger.error(
                f"Failed to update knowledge relationship {relationship.id}: {e}"
            )
            raise

    async def delete(self, relationship_id: uuid.UUID) -> bool:
        """Delete a knowledge relationship."""
        try:
            stmt = select(KnowledgeRelationshipModel).where(
                KnowledgeRelationshipModel.id == relationship_id
            )
            result = await self.session.execute(stmt)
            relationship_model = result.scalar_one_or_none()

            if not relationship_model:
                return False

            await self.session.delete(relationship_model)
            await self.session.commit()

            logger.info(f"Deleted knowledge relationship {relationship_id}")
            return True

        except Exception as e:
            await self.session.rollback()
            logger.error(
                f"Failed to delete knowledge relationship {relationship_id}: {e}"
            )
            raise


class KnowledgeGraphRepositoryImpl(KnowledgeGraphRepository):
    """SQLAlchemy implementation of knowledge graph repository."""

    def __init__(self, session: AsyncSession) -> None:
        """
        Initialize repository with database session.

        Args:
            session: SQLAlchemy async session
        """
        self.session = session

    async def create(self, graph: KnowledgeGraph) -> KnowledgeGraph:
        """Create a new knowledge graph."""
        # Note: This is a virtual operation since we don't store graphs as separate entities
        # Instead, we work with collections of entities and relationships
        return graph

    async def get_by_id(self, graph_id: uuid.UUID) -> KnowledgeGraph | None:
        """Get a knowledge graph by ID."""
        # Note: This is a virtual operation for the current implementation
        return None

    async def get_by_user(
        self,
        user_id: uuid.UUID,
        limit: int = 100,
        offset: int = 0,
    ) -> list[KnowledgeGraph]:
        """Get knowledge graphs by user ID."""
        # Note: This is a virtual operation for the current implementation
        return []

    async def get_subgraph(
        self,
        entity_ids: set[uuid.UUID],
        user_id: uuid.UUID,
        max_depth: int = 2,
    ) -> KnowledgeGraph:
        """Get a subgraph containing specified entities and their neighbors."""
        try:
            # Start with the specified entities
            all_entity_ids = set(entity_ids)

            # Expand to include neighbors up to max_depth
            for depth in range(max_depth):
                # Get relationships for current entities
                stmt = select(KnowledgeRelationshipModel).where(
                    or_(
                        KnowledgeRelationshipModel.source_entity_id.in_(all_entity_ids),
                        KnowledgeRelationshipModel.target_entity_id.in_(all_entity_ids),
                    )
                )
                result = await self.session.execute(stmt)
                relationships = result.scalars().all()

                # Add neighbor entities
                for rel in relationships:
                    all_entity_ids.add(rel.source_entity_id)
                    all_entity_ids.add(rel.target_entity_id)

            # Get all entities
            entity_stmt = (
                select(KnowledgeEntityModel)
                .options(selectinload(KnowledgeEntityModel.creator))
                .where(
                    and_(
                        KnowledgeEntityModel.id.in_(all_entity_ids),
                        or_(
                            KnowledgeEntityModel.created_by == user_id,
                            KnowledgeEntityModel.is_public == True,
                        ),
                    )
                )
            )
            entity_result = await self.session.execute(entity_stmt)
            entity_models = entity_result.scalars().all()

            # Get all relationships between these entities
            rel_stmt = (
                select(KnowledgeRelationshipModel)
                .options(
                    selectinload(KnowledgeRelationshipModel.source_entity),
                    selectinload(KnowledgeRelationshipModel.target_entity),
                )
                .where(
                    and_(
                        KnowledgeRelationshipModel.source_entity_id.in_(all_entity_ids),
                        KnowledgeRelationshipModel.target_entity_id.in_(all_entity_ids),
                    )
                )
            )
            rel_result = await self.session.execute(rel_stmt)
            relationship_models = rel_result.scalars().all()

            # Convert to domain entities
            entities = [model.to_domain() for model in entity_models]
            relationships = [model.to_domain() for model in relationship_models]

            # Create subgraph
            subgraph = KnowledgeGraph(
                name="Subgraph",
                description=f"Subgraph containing {len(entities)} entities",
                entities=entities,
                relationships=relationships,
                created_by=user_id,
            )

            return subgraph

        except Exception as e:
            logger.error(f"Failed to get subgraph: {e}")
            raise

    async def search_entities_in_context(
        self,
        query: str,
        context_entity_ids: set[uuid.UUID],
        user_id: uuid.UUID,
        limit: int = 100,
    ) -> list[KnowledgeEntity]:
        """Search for entities within the context of specified entities."""
        try:
            # Get entities connected to context entities
            connected_entity_ids = set(context_entity_ids)

            # Find entities connected to context entities
            stmt = select(
                KnowledgeRelationshipModel.source_entity_id,
                KnowledgeRelationshipModel.target_entity_id,
            ).where(
                or_(
                    KnowledgeRelationshipModel.source_entity_id.in_(context_entity_ids),
                    KnowledgeRelationshipModel.target_entity_id.in_(context_entity_ids),
                )
            )
            result = await self.session.execute(stmt)
            relationships = result.all()

            for source_id, target_id in relationships:
                connected_entity_ids.add(source_id)
                connected_entity_ids.add(target_id)

            # Search within connected entities
            search_conditions = [
                KnowledgeEntityModel.name.ilike(f"%{query}%"),
                KnowledgeEntityModel.description.ilike(f"%{query}%"),
                KnowledgeEntityModel.tags.op("@>")([query]),
            ]

            conditions = [
                or_(*search_conditions),
                KnowledgeEntityModel.id.in_(connected_entity_ids),
                or_(
                    KnowledgeEntityModel.created_by == user_id,
                    KnowledgeEntityModel.is_public == True,
                ),
            ]

            entity_stmt = (
                select(KnowledgeEntityModel)
                .options(selectinload(KnowledgeEntityModel.creator))
                .where(and_(*conditions))
                .order_by(KnowledgeEntityModel.updated_at.desc())
                .limit(limit)
            )

            entity_result = await self.session.execute(entity_stmt)
            entity_models = entity_result.scalars().all()

            return [model.to_domain() for model in entity_models]

        except Exception as e:
            logger.error(f"Failed to search entities in context: {e}")
            raise

    async def get_entity_paths(
        self,
        source_entity_id: uuid.UUID,
        target_entity_id: uuid.UUID,
        max_depth: int = 5,
    ) -> list[list[uuid.UUID]]:
        """Find paths between two entities using recursive CTE."""
        try:
            # Use recursive CTE to find paths
            cte_query = text("""
                WITH RECURSIVE entity_paths AS (
                    -- Base case: direct relationships
                    SELECT
                        source_entity_id,
                        target_entity_id,
                        ARRAY[source_entity_id, target_entity_id] as path,
                        1 as depth
                    FROM knowledge_relationships
                    WHERE source_entity_id = :source_id

                    UNION ALL

                    -- Recursive case: extend paths
                    SELECT
                        ep.source_entity_id,
                        kr.target_entity_id,
                        ep.path || kr.target_entity_id,
                        ep.depth + 1
                    FROM entity_paths ep
                    JOIN knowledge_relationships kr ON ep.target_entity_id = kr.source_entity_id
                    WHERE ep.depth < :max_depth
                    AND kr.target_entity_id != ALL(ep.path)  -- Avoid cycles
                )
                SELECT path
                FROM entity_paths
                WHERE target_entity_id = :target_id
                ORDER BY depth, path
                LIMIT 10
            """)

            result = await self.session.execute(
                cte_query,
                {
                    "source_id": source_entity_id,
                    "target_id": target_entity_id,
                    "max_depth": max_depth,
                },
            )

            paths = []
            for row in result:
                path = [uuid.UUID(str(entity_id)) for entity_id in row[0]]
                paths.append(path)

            return paths

        except Exception as e:
            logger.error(f"Failed to find entity paths: {e}")
            raise

    async def update(self, graph: KnowledgeGraph) -> KnowledgeGraph:
        """Update an existing knowledge graph."""
        # Note: This is a virtual operation for the current implementation
        return graph

    async def delete(self, graph_id: uuid.UUID) -> bool:
        """Delete a knowledge graph."""
        # Note: This is a virtual operation for the current implementation
        return True
