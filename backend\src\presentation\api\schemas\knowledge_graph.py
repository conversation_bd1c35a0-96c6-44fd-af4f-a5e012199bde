"""
Knowledge Graph API schemas.

This module contains the Pydantic schemas for knowledge graph API requests and responses.
"""

import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field

from src.domain.entities.knowledge_graph import (
    EntityType,
    KnowledgeEntity,
    KnowledgeGraph,
    KnowledgeRelationship,
    RelationType,
)


# Entity schemas
class KnowledgeEntityCreateRequest(BaseModel):
    """Request schema for creating a knowledge entity."""
    
    name: str = Field(..., min_length=1, max_length=255, description="Entity name")
    description: Optional[str] = Field(None, max_length=2000, description="Entity description")
    entity_type: EntityType = Field(..., description="Entity type")
    properties: Dict[str, Any] = Field(default_factory=dict, description="Entity properties")
    attributes: Dict[str, Any] = Field(default_factory=dict, description="Entity attributes")
    tags: List[str] = Field(default_factory=list, description="Entity tags")
    categories: List[str] = Field(default_factory=list, description="Entity categories")
    confidence_score: float = Field(default=1.0, ge=0.0, le=1.0, description="Confidence score")
    source_id: Optional[str] = Field(None, max_length=255, description="Source identifier")
    source_type: Optional[str] = Field(None, max_length=100, description="Source type")
    source_metadata: Dict[str, Any] = Field(default_factory=dict, description="Source metadata")
    is_public: bool = Field(default=False, description="Whether entity is publicly accessible")

    class Config:
        """Pydantic configuration."""
        use_enum_values = True
        schema_extra = {
            "example": {
                "name": "Artificial Intelligence",
                "description": "The simulation of human intelligence in machines",
                "entity_type": "concept",
                "properties": {
                    "field": "computer_science",
                    "complexity": "high"
                },
                "attributes": {
                    "importance": 9,
                    "maturity": "established"
                },
                "tags": ["technology", "machine_learning", "automation"],
                "categories": ["computer_science", "technology"],
                "confidence_score": 0.95,
                "source_id": "wikipedia_ai",
                "source_type": "encyclopedia",
                "source_metadata": {
                    "url": "https://en.wikipedia.org/wiki/Artificial_intelligence",
                    "last_updated": "2024-01-01"
                },
                "is_public": True
            }
        }


class KnowledgeEntityUpdateRequest(BaseModel):
    """Request schema for updating a knowledge entity."""
    
    name: Optional[str] = Field(None, min_length=1, max_length=255, description="Entity name")
    description: Optional[str] = Field(None, max_length=2000, description="Entity description")
    entity_type: Optional[EntityType] = Field(None, description="Entity type")
    properties: Optional[Dict[str, Any]] = Field(None, description="Entity properties")
    attributes: Optional[Dict[str, Any]] = Field(None, description="Entity attributes")
    tags: Optional[List[str]] = Field(None, description="Entity tags")
    categories: Optional[List[str]] = Field(None, description="Entity categories")
    confidence_score: Optional[float] = Field(None, ge=0.0, le=1.0, description="Confidence score")
    is_public: Optional[bool] = Field(None, description="Whether entity is publicly accessible")

    class Config:
        """Pydantic configuration."""
        use_enum_values = True


class KnowledgeEntityResponse(BaseModel):
    """Response schema for knowledge entity."""
    
    id: uuid.UUID = Field(..., description="Entity ID")
    name: str = Field(..., description="Entity name")
    description: Optional[str] = Field(None, description="Entity description")
    entity_type: EntityType = Field(..., description="Entity type")
    properties: Dict[str, Any] = Field(..., description="Entity properties")
    attributes: Dict[str, Any] = Field(..., description="Entity attributes")
    tags: List[str] = Field(..., description="Entity tags")
    categories: List[str] = Field(..., description="Entity categories")
    confidence_score: float = Field(..., description="Confidence score")
    source_id: Optional[str] = Field(None, description="Source identifier")
    source_type: Optional[str] = Field(None, description="Source type")
    source_metadata: Dict[str, Any] = Field(..., description="Source metadata")
    created_by: uuid.UUID = Field(..., description="Creator user ID")
    is_public: bool = Field(..., description="Whether entity is publicly accessible")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    class Config:
        """Pydantic configuration."""
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            uuid.UUID: lambda v: str(v),
        }

    @classmethod
    def from_domain(cls, entity: KnowledgeEntity) -> "KnowledgeEntityResponse":
        """
        Create response schema from domain entity.

        Args:
            entity: KnowledgeEntity domain entity

        Returns:
            KnowledgeEntityResponse instance
        """
        return cls(
            id=entity.id,
            name=entity.name,
            description=entity.description,
            entity_type=entity.entity_type,
            properties=entity.properties,
            attributes=entity.attributes,
            tags=entity.tags,
            categories=entity.categories,
            confidence_score=entity.confidence_score,
            source_id=entity.source_id,
            source_type=entity.source_type,
            source_metadata=entity.source_metadata,
            created_by=entity.created_by,
            is_public=entity.is_public,
            created_at=entity.created_at,
            updated_at=entity.updated_at,
        )


# Relationship schemas
class KnowledgeRelationshipCreateRequest(BaseModel):
    """Request schema for creating a knowledge relationship."""
    
    source_entity_id: uuid.UUID = Field(..., description="Source entity ID")
    target_entity_id: uuid.UUID = Field(..., description="Target entity ID")
    relation_type: RelationType = Field(..., description="Relationship type")
    properties: Dict[str, Any] = Field(default_factory=dict, description="Relationship properties")
    weight: float = Field(default=1.0, ge=0.0, description="Relationship weight/strength")
    confidence_score: float = Field(default=1.0, ge=0.0, le=1.0, description="Confidence score")
    is_directed: bool = Field(default=True, description="Whether relationship is directed")
    description: Optional[str] = Field(None, max_length=1000, description="Relationship description")
    tags: List[str] = Field(default_factory=list, description="Relationship tags")
    source_id: Optional[str] = Field(None, max_length=255, description="Source identifier")
    source_type: Optional[str] = Field(None, max_length=100, description="Source type")
    source_metadata: Dict[str, Any] = Field(default_factory=dict, description="Source metadata")

    class Config:
        """Pydantic configuration."""
        use_enum_values = True
        schema_extra = {
            "example": {
                "source_entity_id": "123e4567-e89b-12d3-a456-426614174000",
                "target_entity_id": "123e4567-e89b-12d3-a456-426614174001",
                "relation_type": "related_to",
                "properties": {
                    "strength": "strong",
                    "context": "research"
                },
                "weight": 0.8,
                "confidence_score": 0.9,
                "is_directed": True,
                "description": "AI is related to machine learning through shared concepts",
                "tags": ["technology", "research"],
                "source_id": "research_paper_123",
                "source_type": "academic_paper",
                "source_metadata": {
                    "doi": "10.1000/182",
                    "publication_year": 2024
                }
            }
        }


class KnowledgeRelationshipUpdateRequest(BaseModel):
    """Request schema for updating a knowledge relationship."""
    
    relation_type: Optional[RelationType] = Field(None, description="Relationship type")
    properties: Optional[Dict[str, Any]] = Field(None, description="Relationship properties")
    weight: Optional[float] = Field(None, ge=0.0, description="Relationship weight/strength")
    confidence_score: Optional[float] = Field(None, ge=0.0, le=1.0, description="Confidence score")
    is_directed: Optional[bool] = Field(None, description="Whether relationship is directed")
    description: Optional[str] = Field(None, max_length=1000, description="Relationship description")
    tags: Optional[List[str]] = Field(None, description="Relationship tags")

    class Config:
        """Pydantic configuration."""
        use_enum_values = True


class KnowledgeRelationshipResponse(BaseModel):
    """Response schema for knowledge relationship."""
    
    id: uuid.UUID = Field(..., description="Relationship ID")
    source_entity_id: uuid.UUID = Field(..., description="Source entity ID")
    target_entity_id: uuid.UUID = Field(..., description="Target entity ID")
    relation_type: RelationType = Field(..., description="Relationship type")
    properties: Dict[str, Any] = Field(..., description="Relationship properties")
    weight: float = Field(..., description="Relationship weight/strength")
    confidence_score: float = Field(..., description="Confidence score")
    is_directed: bool = Field(..., description="Whether relationship is directed")
    description: Optional[str] = Field(None, description="Relationship description")
    tags: List[str] = Field(..., description="Relationship tags")
    source_id: Optional[str] = Field(None, description="Source identifier")
    source_type: Optional[str] = Field(None, description="Source type")
    source_metadata: Dict[str, Any] = Field(..., description="Source metadata")
    created_by: uuid.UUID = Field(..., description="Creator user ID")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    class Config:
        """Pydantic configuration."""
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            uuid.UUID: lambda v: str(v),
        }

    @classmethod
    def from_domain(cls, relationship: KnowledgeRelationship) -> "KnowledgeRelationshipResponse":
        """
        Create response schema from domain entity.

        Args:
            relationship: KnowledgeRelationship domain entity

        Returns:
            KnowledgeRelationshipResponse instance
        """
        return cls(
            id=relationship.id,
            source_entity_id=relationship.source_entity_id,
            target_entity_id=relationship.target_entity_id,
            relation_type=relationship.relation_type,
            properties=relationship.properties,
            weight=relationship.weight,
            confidence_score=relationship.confidence_score,
            is_directed=relationship.is_directed,
            description=relationship.description,
            tags=relationship.tags,
            source_id=relationship.source_id,
            source_type=relationship.source_type,
            source_metadata=relationship.source_metadata,
            created_by=relationship.created_by,
            created_at=relationship.created_at,
            updated_at=relationship.updated_at,
        )


# Graph schemas
class KnowledgeGraphResponse(BaseModel):
    """Response schema for knowledge graph."""
    
    id: uuid.UUID = Field(..., description="Graph ID")
    name: str = Field(..., description="Graph name")
    description: Optional[str] = Field(None, description="Graph description")
    entities: List[KnowledgeEntityResponse] = Field(..., description="Graph entities")
    relationships: List[KnowledgeRelationshipResponse] = Field(..., description="Graph relationships")
    tags: List[str] = Field(..., description="Graph tags")
    categories: List[str] = Field(..., description="Graph categories")
    created_by: uuid.UUID = Field(..., description="Creator user ID")
    is_public: bool = Field(..., description="Whether graph is publicly accessible")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    class Config:
        """Pydantic configuration."""
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            uuid.UUID: lambda v: str(v),
        }

    @classmethod
    def from_domain(cls, graph: KnowledgeGraph) -> "KnowledgeGraphResponse":
        """
        Create response schema from domain entity.

        Args:
            graph: KnowledgeGraph domain entity

        Returns:
            KnowledgeGraphResponse instance
        """
        return cls(
            id=graph.id,
            name=graph.name,
            description=graph.description,
            entities=[KnowledgeEntityResponse.from_domain(entity) for entity in graph.entities],
            relationships=[KnowledgeRelationshipResponse.from_domain(rel) for rel in graph.relationships],
            tags=graph.tags,
            categories=graph.categories,
            created_by=graph.created_by,
            is_public=graph.is_public,
            created_at=graph.created_at,
            updated_at=graph.updated_at,
        )
