"""
Model Management WebSocket endpoints.

This module contains WebSocket endpoints for real-time model management updates.
"""

import asyncio
import json
import uuid
from typing import Dict, Set

from fastapi import WebSocket, WebSocketDisconnect
from pydantic import BaseModel

from src.infrastructure.logging.setup import get_logger

logger = get_logger(__name__)


class WebSocketMessage(BaseModel):
    """WebSocket message structure."""
    
    type: str
    data: dict
    timestamp: str


class ModelManagementWebSocketManager:
    """Manager for model management WebSocket connections."""

    def __init__(self):
        # Store active connections by user ID
        self.active_connections: Dict[str, Set[WebSocket]] = {}
        # Store download task subscriptions
        self.task_subscriptions: Dict[str, Set[WebSocket]] = {}
        # Store model instance subscriptions
        self.instance_subscriptions: Dict[str, Set[WebSocket]] = {}

    async def connect(self, websocket: WebSocket, user_id: str):
        """Accept a new WebSocket connection."""
        await websocket.accept()
        
        if user_id not in self.active_connections:
            self.active_connections[user_id] = set()
        
        self.active_connections[user_id].add(websocket)
        logger.info(f"WebSocket connected for user {user_id}")

    def disconnect(self, websocket: WebSocket, user_id: str):
        """Remove a WebSocket connection."""
        if user_id in self.active_connections:
            self.active_connections[user_id].discard(websocket)
            
            if not self.active_connections[user_id]:
                del self.active_connections[user_id]
        
        # Remove from all subscriptions
        for task_id, connections in self.task_subscriptions.items():
            connections.discard(websocket)
        
        for instance_id, connections in self.instance_subscriptions.items():
            connections.discard(websocket)
        
        logger.info(f"WebSocket disconnected for user {user_id}")

    async def subscribe_to_download_task(self, websocket: WebSocket, task_id: str):
        """Subscribe to download task updates."""
        if task_id not in self.task_subscriptions:
            self.task_subscriptions[task_id] = set()
        
        self.task_subscriptions[task_id].add(websocket)
        logger.info(f"WebSocket subscribed to download task {task_id}")

    async def subscribe_to_model_instance(self, websocket: WebSocket, instance_id: str):
        """Subscribe to model instance updates."""
        if instance_id not in self.instance_subscriptions:
            self.instance_subscriptions[instance_id] = set()
        
        self.instance_subscriptions[instance_id].add(websocket)
        logger.info(f"WebSocket subscribed to model instance {instance_id}")

    async def unsubscribe_from_download_task(self, websocket: WebSocket, task_id: str):
        """Unsubscribe from download task updates."""
        if task_id in self.task_subscriptions:
            self.task_subscriptions[task_id].discard(websocket)
            
            if not self.task_subscriptions[task_id]:
                del self.task_subscriptions[task_id]

    async def unsubscribe_from_model_instance(self, websocket: WebSocket, instance_id: str):
        """Unsubscribe from model instance updates."""
        if instance_id in self.instance_subscriptions:
            self.instance_subscriptions[instance_id].discard(websocket)
            
            if not self.instance_subscriptions[instance_id]:
                del self.instance_subscriptions[instance_id]

    async def send_personal_message(self, message: WebSocketMessage, user_id: str):
        """Send a message to all connections for a specific user."""
        if user_id in self.active_connections:
            disconnected = set()
            
            for websocket in self.active_connections[user_id]:
                try:
                    await websocket.send_text(message.model_dump_json())
                except Exception as e:
                    logger.warning(f"Failed to send message to user {user_id}: {e}")
                    disconnected.add(websocket)
            
            # Remove disconnected websockets
            for websocket in disconnected:
                self.active_connections[user_id].discard(websocket)

    async def broadcast_download_progress(self, task_id: str, progress_data: dict):
        """Broadcast download progress to subscribed connections."""
        if task_id in self.task_subscriptions:
            message = WebSocketMessage(
                type="download_progress",
                data={
                    "task_id": task_id,
                    **progress_data
                },
                timestamp=asyncio.get_event_loop().time()
            )
            
            disconnected = set()
            
            for websocket in self.task_subscriptions[task_id]:
                try:
                    await websocket.send_text(message.model_dump_json())
                except Exception as e:
                    logger.warning(f"Failed to send download progress for task {task_id}: {e}")
                    disconnected.add(websocket)
            
            # Remove disconnected websockets
            for websocket in disconnected:
                self.task_subscriptions[task_id].discard(websocket)

    async def broadcast_instance_status(self, instance_id: str, status_data: dict):
        """Broadcast model instance status to subscribed connections."""
        if instance_id in self.instance_subscriptions:
            message = WebSocketMessage(
                type="instance_status",
                data={
                    "instance_id": instance_id,
                    **status_data
                },
                timestamp=asyncio.get_event_loop().time()
            )
            
            disconnected = set()
            
            for websocket in self.instance_subscriptions[instance_id]:
                try:
                    await websocket.send_text(message.model_dump_json())
                except Exception as e:
                    logger.warning(f"Failed to send instance status for {instance_id}: {e}")
                    disconnected.add(websocket)
            
            # Remove disconnected websockets
            for websocket in disconnected:
                self.instance_subscriptions[instance_id].discard(websocket)

    async def broadcast_model_health(self, instance_id: str, health_data: dict):
        """Broadcast model health status to subscribed connections."""
        if instance_id in self.instance_subscriptions:
            message = WebSocketMessage(
                type="model_health",
                data={
                    "instance_id": instance_id,
                    **health_data
                },
                timestamp=asyncio.get_event_loop().time()
            )
            
            disconnected = set()
            
            for websocket in self.instance_subscriptions[instance_id]:
                try:
                    await websocket.send_text(message.model_dump_json())
                except Exception as e:
                    logger.warning(f"Failed to send health status for {instance_id}: {e}")
                    disconnected.add(websocket)
            
            # Remove disconnected websockets
            for websocket in disconnected:
                self.instance_subscriptions[instance_id].discard(websocket)


# Global WebSocket manager instance
websocket_manager = ModelManagementWebSocketManager()


async def model_management_websocket_endpoint(websocket: WebSocket, user_id: str):
    """
    WebSocket endpoint for model management real-time updates.
    
    Args:
        websocket: WebSocket connection
        user_id: User ID for the connection
    """
    await websocket_manager.connect(websocket, user_id)
    
    try:
        while True:
            # Receive messages from client
            data = await websocket.receive_text()
            
            try:
                message = json.loads(data)
                message_type = message.get("type")
                message_data = message.get("data", {})
                
                # Handle different message types
                if message_type == "subscribe_download_task":
                    task_id = message_data.get("task_id")
                    if task_id:
                        await websocket_manager.subscribe_to_download_task(websocket, task_id)
                        
                        # Send confirmation
                        response = WebSocketMessage(
                            type="subscription_confirmed",
                            data={"subscription": "download_task", "task_id": task_id},
                            timestamp=asyncio.get_event_loop().time()
                        )
                        await websocket.send_text(response.model_dump_json())
                
                elif message_type == "subscribe_model_instance":
                    instance_id = message_data.get("instance_id")
                    if instance_id:
                        await websocket_manager.subscribe_to_model_instance(websocket, instance_id)
                        
                        # Send confirmation
                        response = WebSocketMessage(
                            type="subscription_confirmed",
                            data={"subscription": "model_instance", "instance_id": instance_id},
                            timestamp=asyncio.get_event_loop().time()
                        )
                        await websocket.send_text(response.model_dump_json())
                
                elif message_type == "unsubscribe_download_task":
                    task_id = message_data.get("task_id")
                    if task_id:
                        await websocket_manager.unsubscribe_from_download_task(websocket, task_id)
                
                elif message_type == "unsubscribe_model_instance":
                    instance_id = message_data.get("instance_id")
                    if instance_id:
                        await websocket_manager.unsubscribe_from_model_instance(websocket, instance_id)
                
                elif message_type == "ping":
                    # Respond to ping with pong
                    response = WebSocketMessage(
                        type="pong",
                        data={"timestamp": asyncio.get_event_loop().time()},
                        timestamp=asyncio.get_event_loop().time()
                    )
                    await websocket.send_text(response.model_dump_json())
                
                else:
                    logger.warning(f"Unknown WebSocket message type: {message_type}")
                    
            except json.JSONDecodeError:
                logger.warning(f"Invalid JSON received from WebSocket: {data}")
            except Exception as e:
                logger.error(f"Error processing WebSocket message: {e}")
                
    except WebSocketDisconnect:
        websocket_manager.disconnect(websocket, user_id)
    except Exception as e:
        logger.error(f"WebSocket error for user {user_id}: {e}")
        websocket_manager.disconnect(websocket, user_id)


# Background task for periodic health checks
async def periodic_health_check():
    """Periodic health check for serving model instances."""
    while True:
        try:
            # This would typically query the database for serving instances
            # and check their health, then broadcast updates
            
            # For now, we'll just sleep
            await asyncio.sleep(30)  # Check every 30 seconds
            
        except Exception as e:
            logger.error(f"Error in periodic health check: {e}")
            await asyncio.sleep(60)  # Wait longer on error


# Utility functions for broadcasting updates
async def broadcast_download_progress_update(task_id: str, progress_data: dict):
    """Utility function to broadcast download progress updates."""
    await websocket_manager.broadcast_download_progress(task_id, progress_data)


async def broadcast_instance_status_update(instance_id: str, status_data: dict):
    """Utility function to broadcast instance status updates."""
    await websocket_manager.broadcast_instance_status(instance_id, status_data)


async def broadcast_model_health_update(instance_id: str, health_data: dict):
    """Utility function to broadcast model health updates."""
    await websocket_manager.broadcast_model_health(instance_id, health_data)
