"""
Agent service.

This module contains use cases for agent operations including
creation, management, and orchestration.
"""

import uuid
from datetime import UTC, datetime
from typing import Any, Dict, List, Optional

from src.application.use_cases.mcp_service import MCPService
from src.domain.entities.agent import Agent, AgentStatus, AgentType
from src.domain.entities.user import Permission, User
from src.domain.repositories.agent_repository import AgentRepository
from src.infrastructure.logging.setup import get_logger

logger = get_logger(__name__)


class AgentService:
    """Service for agent operations."""

    def __init__(
        self,
        agent_repository: AgentRepository,
        mcp_service: MCPService,
    ) -> None:
        """
        Initialize the agent service.

        Args:
            agent_repository: Repository for agent operations
            mcp_service: MCP service for AI model operations
        """
        self.agent_repository = agent_repository
        self.mcp_service = mcp_service

    async def create_agent(self, agent: Agent, user: User) -> Agent:
        """
        Create a new agent.

        Args:
            agent: The agent to create
            user: The user creating the agent

        Returns:
            The created agent

        Raises:
            PermissionError: If user lacks permission
            ValueError: If agent validation fails
        """
        # Check permissions
        if not user.has_permission(Permission.AGENT_CREATE):
            raise PermissionError("User lacks permission to create agents")

        # Set creator
        agent.created_by = uuid.UUID(user.id)
        agent.updated_at = datetime.now(UTC)

        logger.info(f"Creating agent '{agent.name}' for user {user.id}")

        # Validate agent configuration
        await self._validate_agent_configuration(agent)

        # Create agent
        created_agent = await self.agent_repository.create(agent)

        logger.info(f"Successfully created agent {created_agent.id}")
        return created_agent

    async def get_agent(self, agent_id: uuid.UUID, user: User) -> Optional[Agent]:
        """
        Get an agent by ID.

        Args:
            agent_id: The agent ID
            user: The requesting user

        Returns:
            The agent if found and accessible, None otherwise

        Raises:
            PermissionError: If user lacks permission
        """
        # Check permissions
        if not user.has_permission(Permission.AGENT_READ):
            raise PermissionError("User lacks permission to read agents")

        agent = await self.agent_repository.get_by_id(agent_id)

        if not agent:
            return None

        # Check if user can access this agent
        if str(agent.created_by) != user.id and not self._is_agent_public(agent):
            raise PermissionError("User cannot access this agent")

        return agent

    async def list_agents(
        self,
        user: User,
        status: Optional[AgentStatus] = None,
        agent_type: Optional[AgentType] = None,
        include_public: bool = True,
        limit: int = 100,
        offset: int = 0,
    ) -> List[Agent]:
        """
        List agents for a user.

        Args:
            user: The requesting user
            status: Optional status filter
            agent_type: Optional type filter
            include_public: Whether to include public agents
            limit: Maximum number of agents to return
            offset: Number of agents to skip

        Returns:
            List of agents

        Raises:
            PermissionError: If user lacks permission
        """
        # Check permissions
        if not user.has_permission(Permission.AGENT_READ):
            raise PermissionError("User lacks permission to read agents")

        # Get user's own agents
        user_agents = await self.agent_repository.get_by_user(
            uuid.UUID(user.id), status, agent_type, limit, offset
        )

        if not include_public:
            return user_agents

        # Get public agents if requested
        public_agents = await self.agent_repository.get_public_agents(
            agent_type, limit, offset
        )

        # Combine and deduplicate
        all_agents = user_agents + [
            a for a in public_agents if str(a.created_by) != user.id
        ]

        # Sort by updated_at descending and apply limit
        all_agents.sort(key=lambda a: a.updated_at, reverse=True)
        return all_agents[:limit]

    async def update_agent(self, agent: Agent, user: User) -> Agent:
        """
        Update an agent.

        Args:
            agent: The agent to update
            user: The user updating the agent

        Returns:
            The updated agent

        Raises:
            PermissionError: If user lacks permission
            ValueError: If agent validation fails or not found
        """
        # Check permissions
        if not user.has_permission(Permission.AGENT_UPDATE):
            raise PermissionError("User lacks permission to update agents")

        # Get existing agent to check ownership
        existing_agent = await self.agent_repository.get_by_id(agent.id)
        if not existing_agent:
            raise ValueError(f"Agent {agent.id} not found")

        if str(existing_agent.created_by) != user.id:
            raise PermissionError("User can only update their own agents")

        # Validate agent configuration
        await self._validate_agent_configuration(agent)

        # Update timestamp
        agent.updated_at = datetime.now(UTC)

        logger.info(f"Updating agent {agent.id} for user {user.id}")

        # Update agent
        updated_agent = await self.agent_repository.update(agent)

        logger.info(f"Successfully updated agent {agent.id}")
        return updated_agent

    async def delete_agent(self, agent_id: uuid.UUID, user: User) -> bool:
        """
        Delete an agent.

        Args:
            agent_id: The agent ID to delete
            user: The user deleting the agent

        Returns:
            True if deleted, False if not found

        Raises:
            PermissionError: If user lacks permission
        """
        # Check permissions
        if not user.has_permission(Permission.AGENT_DELETE):
            raise PermissionError("User lacks permission to delete agents")

        # Get agent to check ownership
        agent = await self.agent_repository.get_by_id(agent_id)
        if not agent:
            return False

        if str(agent.created_by) != user.id:
            raise PermissionError("User can only delete their own agents")

        logger.info(f"Deleting agent {agent_id} for user {user.id}")

        # Delete agent
        deleted = await self.agent_repository.delete(agent_id)

        if deleted:
            logger.info(f"Successfully deleted agent {agent_id}")
        else:
            logger.warning(f"Failed to delete agent {agent_id}")

        return deleted

    async def activate_agent(self, agent_id: uuid.UUID, user: User) -> Agent:
        """
        Activate an agent.

        Args:
            agent_id: The agent ID to activate
            user: The user activating the agent

        Returns:
            The activated agent

        Raises:
            PermissionError: If user lacks permission
            ValueError: If agent not found
        """
        agent = await self.get_agent(agent_id, user)
        if not agent:
            raise ValueError(f"Agent {agent_id} not found")

        if str(agent.created_by) != user.id:
            raise PermissionError("User can only activate their own agents")

        # Update status
        agent.update_status(AgentStatus.IDLE)

        # Update agent
        updated_agent = await self.agent_repository.update(agent)

        logger.info(f"Activated agent {agent_id}")
        return updated_agent

    async def deactivate_agent(self, agent_id: uuid.UUID, user: User) -> Agent:
        """
        Deactivate an agent.

        Args:
            agent_id: The agent ID to deactivate
            user: The user deactivating the agent

        Returns:
            The deactivated agent

        Raises:
            PermissionError: If user lacks permission
            ValueError: If agent not found
        """
        agent = await self.get_agent(agent_id, user)
        if not agent:
            raise ValueError(f"Agent {agent_id} not found")

        if str(agent.created_by) != user.id:
            raise PermissionError("User can only deactivate their own agents")

        # Update status
        agent.update_status(AgentStatus.CANCELLED)

        # Update agent
        updated_agent = await self.agent_repository.update(agent)

        logger.info(f"Deactivated agent {agent_id}")
        return updated_agent

    async def search_agents(
        self,
        query: str,
        user: User,
        include_public: bool = True,
        limit: int = 100,
        offset: int = 0,
    ) -> List[Agent]:
        """
        Search agents by name, description, or tags.

        Args:
            query: Search query string
            user: The requesting user
            include_public: Whether to include public agents
            limit: Maximum number of agents to return
            offset: Number of agents to skip

        Returns:
            List of matching agents

        Raises:
            PermissionError: If user lacks permission
        """
        # Check permissions
        if not user.has_permission(Permission.AGENT_READ):
            raise PermissionError("User lacks permission to read agents")

        user_id = uuid.UUID(user.id) if include_public else uuid.UUID(user.id)
        
        return await self.agent_repository.search(
            query, user_id, include_public, limit, offset
        )

    async def get_agent_metrics(self, user: User) -> Dict[str, Any]:
        """
        Get aggregated metrics for user's agents.

        Args:
            user: The requesting user

        Returns:
            Dictionary with aggregated metrics

        Raises:
            PermissionError: If user lacks permission
        """
        # Check permissions
        if not user.has_permission(Permission.AGENT_READ):
            raise PermissionError("User lacks permission to read agent metrics")

        return await self.agent_repository.get_agent_metrics_summary(uuid.UUID(user.id))

    async def _validate_agent_configuration(self, agent: Agent) -> None:
        """
        Validate agent configuration.

        Args:
            agent: The agent to validate

        Raises:
            ValueError: If configuration is invalid
        """
        # Validate model exists
        if not agent.configuration.model_name:
            raise ValueError("Agent must have a model configured")

        # Validate temperature range
        if not 0.0 <= agent.configuration.temperature <= 2.0:
            raise ValueError("Temperature must be between 0.0 and 2.0")

        # Validate max tokens
        if agent.configuration.max_tokens <= 0:
            raise ValueError("Max tokens must be positive")

        # Validate timeout
        if agent.configuration.timeout_seconds <= 0:
            raise ValueError("Timeout must be positive")

        # Validate capabilities
        for capability in agent.capabilities:
            if not capability.name:
                raise ValueError("Capability must have a name")

    def _is_agent_public(self, agent: Agent) -> bool:
        """
        Check if agent is publicly accessible.

        Args:
            agent: The agent to check

        Returns:
            True if agent is public, False otherwise
        """
        # For now, check if agent has public metadata
        return agent.metadata.get("is_public", False)
