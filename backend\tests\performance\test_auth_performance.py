"""
Authentication Performance Tests

Performance benchmarks for the authentication and authorization system
to ensure <200ms response times as required.
"""

import asyncio
import time
import uuid
from statistics import mean, median
from unittest.mock import AsyncMock, patch

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from src.domain.entities.user import Permission, User, UserRole, UserStatus
from src.domain.value_objects.email import Email
from src.domain.value_objects.username import Username
from src.presentation.dependencies.auth import require_permissions, require_roles


@pytest.fixture
def sample_user_id():
    """Sample user ID for testing."""
    return uuid.uuid4()


@pytest.fixture
def admin_user(sample_user_id):
    """Create admin user entity."""
    return User(
        id=str(sample_user_id),
        email=Email("<EMAIL>"),
        username=Username("admin"),
        full_name="Admin User",
        hashed_password="hashed_password",
        role=UserRole.ADMIN,
        status=UserStatus.ACTIVE,
        is_active=True,
        is_verified=True,
    )


@pytest.fixture
def regular_user(sample_user_id):
    """Create regular user entity."""
    return User(
        id=str(sample_user_id),
        email=Email("<EMAIL>"),
        username=Username("user"),
        full_name="Regular User",
        hashed_password="hashed_password",
        role=UserRole.USER,
        status=UserStatus.ACTIVE,
        is_active=True,
        is_verified=True,
    )


@pytest.fixture
def sample_current_user(sample_user_id):
    """Sample current user dict from JWT."""
    return {
        "user_id": str(sample_user_id),
        "subject": "<EMAIL>",
        "token_id": "test-token-id",
    }


@pytest.fixture
def mock_db_session():
    """Mock database session."""
    return AsyncMock(spec=AsyncSession)


class TestPermissionCheckPerformance:
    """Performance tests for permission checking."""

    @pytest.mark.asyncio
    @patch("src.infrastructure.database.repositories.user_repository.UserRepository")
    async def test_single_permission_check_performance(
        self, mock_user_repo_class, mock_db_session, sample_current_user, admin_user
    ):
        """Test single permission check performance."""
        # Setup mock
        mock_user_repo = AsyncMock()
        mock_user_repo.get_by_id.return_value = admin_user
        mock_user_repo_class.return_value = mock_user_repo

        # Create permission dependency
        permission_dep = require_permissions(Permission.WORKFLOW_CREATE.value)

        # Measure execution time
        start_time = time.time()
        await permission_dep(sample_current_user, mock_db_session)
        end_time = time.time()

        execution_time = end_time - start_time
        
        # Verify performance requirement (<200ms)
        assert execution_time < 0.2, f"Permission check took {execution_time:.3f}s, should be <0.2s"
        
        # Log performance for monitoring
        print(f"Single permission check: {execution_time:.3f}s")

    @pytest.mark.asyncio
    @patch("src.infrastructure.database.repositories.user_repository.UserRepository")
    async def test_multiple_permission_check_performance(
        self, mock_user_repo_class, mock_db_session, sample_current_user, admin_user
    ):
        """Test multiple permission check performance."""
        # Setup mock
        mock_user_repo = AsyncMock()
        mock_user_repo.get_by_id.return_value = admin_user
        mock_user_repo_class.return_value = mock_user_repo

        # Create permission dependency with multiple permissions
        permission_dep = require_permissions(
            Permission.WORKFLOW_CREATE.value,
            Permission.WORKFLOW_DELETE.value,
            Permission.USER_MANAGE.value,
            Permission.SYSTEM_ADMIN.value,
        )

        # Measure execution time
        start_time = time.time()
        await permission_dep(sample_current_user, mock_db_session)
        end_time = time.time()

        execution_time = end_time - start_time
        
        # Verify performance requirement (<200ms)
        assert execution_time < 0.2, f"Multiple permission check took {execution_time:.3f}s, should be <0.2s"
        
        # Log performance for monitoring
        print(f"Multiple permission check: {execution_time:.3f}s")

    @pytest.mark.asyncio
    @patch("src.infrastructure.database.repositories.user_repository.UserRepository")
    async def test_permission_check_load_test(
        self, mock_user_repo_class, mock_db_session, sample_current_user, admin_user
    ):
        """Test permission check under load (100 concurrent requests)."""
        # Setup mock
        mock_user_repo = AsyncMock()
        mock_user_repo.get_by_id.return_value = admin_user
        mock_user_repo_class.return_value = mock_user_repo

        # Create permission dependency
        permission_dep = require_permissions(Permission.WORKFLOW_CREATE.value)

        async def single_check():
            """Single permission check."""
            start_time = time.time()
            await permission_dep(sample_current_user, mock_db_session)
            return time.time() - start_time

        # Run 100 concurrent permission checks
        tasks = [single_check() for _ in range(100)]
        
        start_time = time.time()
        execution_times = await asyncio.gather(*tasks)
        total_time = time.time() - start_time

        # Analyze results
        avg_time = mean(execution_times)
        median_time = median(execution_times)
        max_time = max(execution_times)
        
        # Verify performance requirements
        assert avg_time < 0.2, f"Average permission check took {avg_time:.3f}s, should be <0.2s"
        assert median_time < 0.2, f"Median permission check took {median_time:.3f}s, should be <0.2s"
        assert max_time < 0.5, f"Max permission check took {max_time:.3f}s, should be <0.5s"
        
        # Log performance metrics
        print(f"Load test results (100 concurrent checks):")
        print(f"  Total time: {total_time:.3f}s")
        print(f"  Average: {avg_time:.3f}s")
        print(f"  Median: {median_time:.3f}s")
        print(f"  Max: {max_time:.3f}s")


class TestRoleCheckPerformance:
    """Performance tests for role checking."""

    @pytest.mark.asyncio
    @patch("src.infrastructure.database.repositories.user_repository.UserRepository")
    async def test_single_role_check_performance(
        self, mock_user_repo_class, mock_db_session, sample_current_user, admin_user
    ):
        """Test single role check performance."""
        # Setup mock
        mock_user_repo = AsyncMock()
        mock_user_repo.get_by_id.return_value = admin_user
        mock_user_repo_class.return_value = mock_user_repo

        # Create role dependency
        role_dep = require_roles(UserRole.ADMIN.value)

        # Measure execution time
        start_time = time.time()
        await role_dep(sample_current_user, mock_db_session)
        end_time = time.time()

        execution_time = end_time - start_time
        
        # Verify performance requirement (<200ms)
        assert execution_time < 0.2, f"Role check took {execution_time:.3f}s, should be <0.2s"
        
        # Log performance for monitoring
        print(f"Single role check: {execution_time:.3f}s")

    @pytest.mark.asyncio
    @patch("src.infrastructure.database.repositories.user_repository.UserRepository")
    async def test_multiple_role_check_performance(
        self, mock_user_repo_class, mock_db_session, sample_current_user, admin_user
    ):
        """Test multiple role check performance."""
        # Setup mock
        mock_user_repo = AsyncMock()
        mock_user_repo.get_by_id.return_value = admin_user
        mock_user_repo_class.return_value = mock_user_repo

        # Create role dependency with multiple roles
        role_dep = require_roles(
            UserRole.ADMIN.value,
            UserRole.MODERATOR.value,
            UserRole.USER.value,
        )

        # Measure execution time
        start_time = time.time()
        await role_dep(sample_current_user, mock_db_session)
        end_time = time.time()

        execution_time = end_time - start_time
        
        # Verify performance requirement (<200ms)
        assert execution_time < 0.2, f"Multiple role check took {execution_time:.3f}s, should be <0.2s"
        
        # Log performance for monitoring
        print(f"Multiple role check: {execution_time:.3f}s")


class TestDatabasePerformance:
    """Performance tests for database operations."""

    @pytest.mark.asyncio
    @patch("src.infrastructure.database.repositories.user_repository.UserRepository")
    async def test_user_lookup_performance(
        self, mock_user_repo_class, mock_db_session, sample_current_user, regular_user
    ):
        """Test user database lookup performance."""
        # Setup mock with simulated database delay
        mock_user_repo = AsyncMock()
        
        async def mock_get_by_id(user_id):
            # Simulate database query time (should be fast)
            await asyncio.sleep(0.01)  # 10ms simulated DB time
            return regular_user
        
        mock_user_repo.get_by_id = mock_get_by_id
        mock_user_repo_class.return_value = mock_user_repo

        # Create permission dependency
        permission_dep = require_permissions(Permission.WORKFLOW_READ.value)

        # Measure execution time including DB lookup
        start_time = time.time()
        await permission_dep(sample_current_user, mock_db_session)
        end_time = time.time()

        execution_time = end_time - start_time
        
        # Verify performance requirement (<200ms even with DB lookup)
        assert execution_time < 0.2, f"Permission check with DB lookup took {execution_time:.3f}s, should be <0.2s"
        
        # Log performance for monitoring
        print(f"Permission check with DB lookup: {execution_time:.3f}s")

    @pytest.mark.asyncio
    @patch("src.infrastructure.database.repositories.user_repository.UserRepository")
    async def test_concurrent_database_access(
        self, mock_user_repo_class, mock_db_session, sample_current_user, regular_user
    ):
        """Test concurrent database access performance."""
        # Setup mock with simulated database delay
        mock_user_repo = AsyncMock()
        
        async def mock_get_by_id(user_id):
            # Simulate database query time
            await asyncio.sleep(0.005)  # 5ms simulated DB time
            return regular_user
        
        mock_user_repo.get_by_id = mock_get_by_id
        mock_user_repo_class.return_value = mock_user_repo

        # Create permission dependency
        permission_dep = require_permissions(Permission.WORKFLOW_READ.value)

        async def single_check():
            """Single permission check with DB access."""
            start_time = time.time()
            await permission_dep(sample_current_user, mock_db_session)
            return time.time() - start_time

        # Run 50 concurrent checks
        tasks = [single_check() for _ in range(50)]
        
        start_time = time.time()
        execution_times = await asyncio.gather(*tasks)
        total_time = time.time() - start_time

        # Analyze results
        avg_time = mean(execution_times)
        max_time = max(execution_times)
        
        # Verify performance requirements
        assert avg_time < 0.2, f"Average DB check took {avg_time:.3f}s, should be <0.2s"
        assert max_time < 0.3, f"Max DB check took {max_time:.3f}s, should be <0.3s"
        
        # Log performance metrics
        print(f"Concurrent DB access results (50 concurrent checks):")
        print(f"  Total time: {total_time:.3f}s")
        print(f"  Average: {avg_time:.3f}s")
        print(f"  Max: {max_time:.3f}s")


class TestMemoryPerformance:
    """Performance tests for memory usage."""

    def test_permission_enum_performance(self):
        """Test permission enum lookup performance."""
        # Test permission enum conversion performance
        permission_strings = [
            "workflow:create",
            "workflow:read",
            "workflow:update",
            "workflow:delete",
            "agent:create",
            "agent:read",
            "user:manage",
            "system:admin",
        ]

        start_time = time.time()
        
        # Convert strings to enums 1000 times
        for _ in range(1000):
            for perm_str in permission_strings:
                try:
                    Permission(perm_str)
                except ValueError:
                    pass  # Invalid permission
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Should be very fast (enum lookups)
        assert execution_time < 0.1, f"Permission enum lookups took {execution_time:.3f}s, should be <0.1s"
        
        print(f"Permission enum performance (8000 lookups): {execution_time:.3f}s")

    def test_user_permission_calculation_performance(self, admin_user):
        """Test user permission calculation performance."""
        start_time = time.time()
        
        # Calculate permissions 1000 times
        for _ in range(1000):
            permissions = admin_user.get_permissions()
            # Check a few permissions
            admin_user.has_permission(Permission.WORKFLOW_CREATE)
            admin_user.has_permission(Permission.USER_MANAGE)
            admin_user.has_permission(Permission.SYSTEM_ADMIN)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Should be very fast (in-memory operations)
        assert execution_time < 0.1, f"Permission calculations took {execution_time:.3f}s, should be <0.1s"
        
        print(f"User permission calculation performance (1000 iterations): {execution_time:.3f}s")
