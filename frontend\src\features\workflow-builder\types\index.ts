/**
 * Workflow Builder Types
 * 
 * Type definitions for the drag-and-drop workflow builder system
 * following the established FSD architecture patterns.
 */

import { AgentModel } from '@/entities/agent/model';
import { AgentStatus } from '@/shared/types';

// Core workflow types
export interface WorkflowNode {
  id: string;
  type: NodeType;
  position: Position;
  data: NodeData;
  selected?: boolean;
  dragging?: boolean;
  connectable?: boolean;
  deletable?: boolean;
  selectable?: boolean;
  focusable?: boolean;
  zIndex?: number;
  extent?: 'parent' | CoordinateExtent;
  parentNode?: string;
  expandParent?: boolean;
  positionAbsolute?: Position;
  ariaLabel?: string;
  style?: React.CSSProperties;
  className?: string;
  sourcePosition?: HandlePosition;
  targetPosition?: HandlePosition;
  hidden?: boolean;
  width?: number;
  height?: number;
}

export interface WorkflowEdge {
  id: string;
  source: string;
  target: string;
  sourceHandle?: string;
  targetHandle?: string;
  type?: EdgeType;
  animated?: boolean;
  hidden?: boolean;
  deletable?: boolean;
  focusable?: boolean;
  selected?: boolean;
  markerStart?: EdgeMarker;
  markerEnd?: EdgeMarker;
  zIndex?: number;
  ariaLabel?: string;
  interactionWidth?: number;
  style?: React.CSSProperties;
  className?: string;
  data?: EdgeData;
  label?: string | React.ReactNode;
  labelStyle?: React.CSSProperties;
  labelShowBg?: boolean;
  labelBgStyle?: React.CSSProperties;
  labelBgPadding?: [number, number];
  labelBgBorderRadius?: number;
}

export interface Workflow {
  id: string;
  name: string;
  description?: string;
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
  viewport: Viewport;
  metadata: WorkflowMetadata;
  version: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  tags: string[];
  isPublic: boolean;
  status: WorkflowStatus;
}

// Node types
export enum NodeType {
  AGENT = 'agent',
  TOOL = 'tool',
  CONDITION = 'condition',
  LOOP = 'loop',
  INPUT = 'input',
  OUTPUT = 'output',
  TRIGGER = 'trigger',
  ACTION = 'action',
  TRANSFORM = 'transform',
  DELAY = 'delay',
  PARALLEL = 'parallel',
  MERGE = 'merge',
  CUSTOM = 'custom',
}

export enum EdgeType {
  DEFAULT = 'default',
  STRAIGHT = 'straight',
  STEP = 'step',
  SMOOTHSTEP = 'smoothstep',
  BEZIER = 'bezier',
  CUSTOM = 'custom',
}

export enum HandlePosition {
  TOP = 'top',
  RIGHT = 'right',
  BOTTOM = 'bottom',
  LEFT = 'left',
}

export enum WorkflowStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  FAILED = 'failed',
  ARCHIVED = 'archived',
}

// Position and geometry types
export interface Position {
  x: number;
  y: number;
}

export interface Dimensions {
  width: number;
  height: number;
}

export interface Viewport {
  x: number;
  y: number;
  zoom: number;
}

export type CoordinateExtent = [[number, number], [number, number]];

// Node data types
export interface NodeData {
  label?: string;
  description?: string;
  config?: Record<string, any>;
  inputs?: NodePort[];
  outputs?: NodePort[];
  agent?: AgentModel;
  status?: AgentStatus;
  executionTime?: number;
  lastExecuted?: string;
  errorMessage?: string;
  metadata?: Record<string, any>;
}

export interface EdgeData {
  label?: string;
  condition?: string;
  weight?: number;
  metadata?: Record<string, any>;
}

export interface NodePort {
  id: string;
  name: string;
  type: PortType;
  required?: boolean;
  description?: string;
  defaultValue?: any;
  validation?: PortValidation;
}

export enum PortType {
  STRING = 'string',
  NUMBER = 'number',
  BOOLEAN = 'boolean',
  OBJECT = 'object',
  ARRAY = 'array',
  ANY = 'any',
  AGENT = 'agent',
  FILE = 'file',
  IMAGE = 'image',
  AUDIO = 'audio',
  VIDEO = 'video',
}

export interface PortValidation {
  required?: boolean;
  min?: number;
  max?: number;
  pattern?: string;
  enum?: any[];
  custom?: (value: any) => boolean | string;
}

// Edge marker types
export interface EdgeMarker {
  type: MarkerType;
  color?: string;
  width?: number;
  height?: number;
  markerUnits?: string;
  orient?: string;
}

export enum MarkerType {
  ARROW = 'arrow',
  ARROW_CLOSED = 'arrowclosed',
}

// Workflow metadata
export interface WorkflowMetadata {
  category?: string;
  complexity?: WorkflowComplexity;
  estimatedDuration?: number;
  requiredCapabilities?: string[];
  performance?: WorkflowPerformance;
  validation?: WorkflowValidation;
}

export enum WorkflowComplexity {
  SIMPLE = 'simple',
  MODERATE = 'moderate',
  COMPLEX = 'complex',
  ADVANCED = 'advanced',
}

export interface WorkflowPerformance {
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  averageExecutionTime: number;
  lastExecutionTime?: string;
  totalCost: number;
}

export interface WorkflowValidation {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

export interface ValidationError {
  id: string;
  type: ValidationErrorType;
  message: string;
  nodeId?: string;
  edgeId?: string;
  severity: 'error' | 'warning';
}

export interface ValidationWarning {
  id: string;
  message: string;
  nodeId?: string;
  edgeId?: string;
}

export enum ValidationErrorType {
  MISSING_CONNECTION = 'missing_connection',
  INVALID_CONNECTION = 'invalid_connection',
  CIRCULAR_DEPENDENCY = 'circular_dependency',
  MISSING_REQUIRED_INPUT = 'missing_required_input',
  INVALID_CONFIGURATION = 'invalid_configuration',
  UNREACHABLE_NODE = 'unreachable_node',
  MULTIPLE_START_NODES = 'multiple_start_nodes',
  NO_START_NODE = 'no_start_node',
  NO_END_NODE = 'no_end_node',
}

// Canvas and interaction types
export interface CanvasState {
  viewport: Viewport;
  selectedNodes: string[];
  selectedEdges: string[];
  draggedNode?: string;
  connectionMode: boolean;
  connectionStart?: {
    nodeId: string;
    handleId: string;
    handleType: 'source' | 'target';
  };
  gridSize: number;
  snapToGrid: boolean;
  showGrid: boolean;
  showMinimap: boolean;
  readonly: boolean;
}

export interface DragItem {
  type: string;
  nodeType: NodeType;
  data?: Partial<NodeData>;
}

// Event types
export interface NodeChangeEvent {
  type: 'position' | 'dimensions' | 'selection' | 'remove' | 'add' | 'reset';
  id: string;
  position?: Position;
  dimensions?: Dimensions;
  selected?: boolean;
  dragging?: boolean;
}

export interface EdgeChangeEvent {
  type: 'select' | 'remove' | 'add' | 'reset';
  id: string;
  selected?: boolean;
}

export interface ConnectionEvent {
  source: string;
  target: string;
  sourceHandle?: string;
  targetHandle?: string;
}

// Node library types
export interface NodeTemplate {
  id: string;
  type: NodeType;
  name: string;
  description: string;
  category: NodeCategory;
  icon: React.ComponentType;
  defaultData: NodeData;
  inputs: NodePort[];
  outputs: NodePort[];
  configSchema?: Record<string, any>;
  examples?: NodeExample[];
  documentation?: string;
  tags: string[];
  complexity: WorkflowComplexity;
  deprecated?: boolean;
}

export enum NodeCategory {
  AGENTS = 'agents',
  TOOLS = 'tools',
  LOGIC = 'logic',
  DATA = 'data',
  TRIGGERS = 'triggers',
  ACTIONS = 'actions',
  INTEGRATIONS = 'integrations',
  UTILITIES = 'utilities',
  CUSTOM = 'custom',
}

export interface NodeExample {
  name: string;
  description: string;
  config: Record<string, any>;
  inputs: Record<string, any>;
  expectedOutputs: Record<string, any>;
}

// Execution types
export interface WorkflowExecution {
  id: string;
  workflowId: string;
  status: ExecutionStatus;
  startTime: string;
  endTime?: string;
  duration?: number;
  nodeExecutions: NodeExecution[];
  inputs: Record<string, any>;
  outputs: Record<string, any>;
  error?: ExecutionError;
  metadata: Record<string, any>;
}

export enum ExecutionStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  TIMEOUT = 'timeout',
}

export interface NodeExecution {
  nodeId: string;
  status: ExecutionStatus;
  startTime: string;
  endTime?: string;
  duration?: number;
  inputs: Record<string, any>;
  outputs: Record<string, any>;
  error?: ExecutionError;
  logs: ExecutionLog[];
}

export interface ExecutionError {
  code: string;
  message: string;
  details?: Record<string, any>;
  stack?: string;
}

export interface ExecutionLog {
  timestamp: string;
  level: LogLevel;
  message: string;
  data?: Record<string, any>;
}

export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error',
}
