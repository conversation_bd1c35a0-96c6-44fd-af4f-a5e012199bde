"""
Model Management API routes.

This module contains the FastAPI routes for model management operations.
"""

import uuid
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from pydantic import BaseModel, Field

from src.application.use_cases.model_management_service import ModelManagementService
from src.domain.entities.model_management import <PERSON><PERSON>rovider, ModelStatus, ModelType
from src.domain.entities.user import User
from src.infrastructure.logging.setup import get_logger
from src.infrastructure.security.jwt.jwt_manager import get_current_user
from src.presentation.api.schemas.model_management import (
    ModelDiscoveryResponse,
    ModelDownloadRequest,
    ModelDownloadTaskResponse,
    ModelHealthResponse,
    ModelInfoResponse,
    ModelInstanceCreateRequest,
    ModelInstanceResponse,
    ModelSearchRequest,
    ModelServingRequest,
)

logger = get_logger(__name__)

router = APIRouter(prefix="/models", tags=["Model Management"])


async def get_model_management_service() -> ModelManagementService:
    """
    Dependency to get model management service.

    Returns:
        ModelManagementService instance
    """
    return ModelManagementService()


# Model discovery endpoints
@router.get(
    "/discover/huggingface",
    response_model=List[ModelInfoResponse],
    summary="Discover HuggingFace models",
    description="Discover available models from HuggingFace Hub"
)
async def discover_huggingface_models(
    query: Optional[str] = Query(None, description="Search query"),
    model_type: Optional[ModelType] = Query(None, description="Filter by model type"),
    limit: int = Query(20, ge=1, le=100, description="Maximum number of models to return"),
    current_user: User = Depends(get_current_user),
    service: ModelManagementService = Depends(get_model_management_service),
) -> List[ModelInfoResponse]:
    """Discover models from HuggingFace Hub."""
    try:
        models = await service.discover_huggingface_models(query, model_type, limit)
        
        return [ModelInfoResponse.from_domain(model) for model in models]

    except Exception as e:
        logger.error(f"Failed to discover HuggingFace models: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to discover HuggingFace models"
        )


@router.get(
    "/discover/ollama",
    response_model=List[ModelInfoResponse],
    summary="Discover Ollama models",
    description="Discover available models from Ollama"
)
async def discover_ollama_models(
    current_user: User = Depends(get_current_user),
    service: ModelManagementService = Depends(get_model_management_service),
) -> List[ModelInfoResponse]:
    """Discover models from Ollama."""
    try:
        models = await service.discover_ollama_models()
        
        return [ModelInfoResponse.from_domain(model) for model in models]

    except Exception as e:
        logger.error(f"Failed to discover Ollama models: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to discover Ollama models"
        )


@router.post(
    "/search",
    response_model=List[ModelInfoResponse],
    summary="Search models",
    description="Search for models across all providers"
)
async def search_models(
    request: ModelSearchRequest,
    current_user: User = Depends(get_current_user),
    service: ModelManagementService = Depends(get_model_management_service),
) -> List[ModelInfoResponse]:
    """Search for models across all providers."""
    try:
        models = await service.search_models(
            request.query,
            request.model_type,
            request.provider,
            request.limit
        )
        
        return [ModelInfoResponse.from_domain(model) for model in models]

    except Exception as e:
        logger.error(f"Failed to search models: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to search models"
        )


# Model instance endpoints
@router.post(
    "/instances",
    response_model=ModelInstanceResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create model instance",
    description="Create a new model instance for local management"
)
async def create_model_instance(
    request: ModelInstanceCreateRequest,
    current_user: User = Depends(get_current_user),
    service: ModelManagementService = Depends(get_model_management_service),
) -> ModelInstanceResponse:
    """Create a new model instance."""
    try:
        instance = await service.create_model_instance(
            request.model_id,
            current_user,
            request.configuration
        )

        logger.info(f"Created model instance {instance.id} for user {current_user.id}")
        
        return ModelInstanceResponse.from_domain(instance)

    except PermissionError as e:
        logger.warning(f"Permission denied creating model instance for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to create model instances"
        )
    except Exception as e:
        logger.error(f"Failed to create model instance: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create model instance"
        )


@router.post(
    "/instances/{instance_id}/download",
    response_model=ModelDownloadTaskResponse,
    summary="Download model",
    description="Start downloading a model to local storage"
)
async def download_model(
    instance_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    service: ModelManagementService = Depends(get_model_management_service),
) -> ModelDownloadTaskResponse:
    """Start downloading a model."""
    try:
        # TODO: Get instance from database
        # For now, create a mock instance
        from src.domain.entities.model_management import ModelInstance
        instance = ModelInstance(
            id=instance_id,
            model_id="test-model",
            status=ModelStatus.AVAILABLE,
            created_by=uuid.UUID(current_user.id)
        )

        task = await service.download_model(instance, current_user)

        logger.info(f"Started download for instance {instance_id}")
        
        return ModelDownloadTaskResponse.from_domain(task)

    except PermissionError as e:
        logger.warning(f"Permission denied downloading model for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to download models"
        )
    except ValueError as e:
        logger.warning(f"Invalid download request: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Failed to start model download: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start model download"
        )


@router.get(
    "/downloads/{task_id}",
    response_model=ModelDownloadTaskResponse,
    summary="Get download progress",
    description="Get the progress of a model download task"
)
async def get_download_progress(
    task_id: str,
    current_user: User = Depends(get_current_user),
    service: ModelManagementService = Depends(get_model_management_service),
) -> ModelDownloadTaskResponse:
    """Get download progress for a task."""
    try:
        task = await service.get_download_progress(task_id)
        
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Download task not found"
            )
        
        return ModelDownloadTaskResponse.from_domain(task)

    except Exception as e:
        logger.error(f"Failed to get download progress: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get download progress"
        )


@router.delete(
    "/downloads/{task_id}",
    summary="Cancel download",
    description="Cancel a model download task"
)
async def cancel_download(
    task_id: str,
    current_user: User = Depends(get_current_user),
    service: ModelManagementService = Depends(get_model_management_service),
) -> dict:
    """Cancel a download task."""
    try:
        cancelled = await service.cancel_download(task_id, current_user)
        
        if not cancelled:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Download task not found"
            )

        logger.info(f"Cancelled download task {task_id}")
        
        return {
            "success": True,
            "message": f"Download task {task_id} cancelled successfully"
        }

    except PermissionError as e:
        logger.warning(f"Permission denied cancelling download for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to cancel this download"
        )
    except Exception as e:
        logger.error(f"Failed to cancel download: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to cancel download"
        )


# Model serving endpoints
@router.post(
    "/instances/{instance_id}/serve",
    summary="Start model serving",
    description="Start serving a model for inference"
)
async def start_model_serving(
    instance_id: uuid.UUID,
    request: ModelServingRequest,
    current_user: User = Depends(get_current_user),
    service: ModelManagementService = Depends(get_model_management_service),
) -> dict:
    """Start serving a model."""
    try:
        # TODO: Get instance from database
        from src.domain.entities.model_management import ModelInstance
        instance = ModelInstance(
            id=instance_id,
            model_id="test-model",
            status=ModelStatus.DOWNLOADED,
            created_by=uuid.UUID(current_user.id)
        )

        success = await service.start_model_serving(instance, current_user, request.port)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to start model serving"
            )

        logger.info(f"Started serving model instance {instance_id}")
        
        return {
            "success": True,
            "message": f"Model instance {instance_id} is now serving",
            "serving_url": instance.serving_url,
            "api_endpoint": instance.api_endpoint
        }

    except PermissionError as e:
        logger.warning(f"Permission denied starting model serving for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to serve models"
        )
    except ValueError as e:
        logger.warning(f"Invalid serving request: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Failed to start model serving: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start model serving"
        )


@router.post(
    "/instances/{instance_id}/stop",
    summary="Stop model serving",
    description="Stop serving a model"
)
async def stop_model_serving(
    instance_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    service: ModelManagementService = Depends(get_model_management_service),
) -> dict:
    """Stop serving a model."""
    try:
        # TODO: Get instance from database
        from src.domain.entities.model_management import ModelInstance
        instance = ModelInstance(
            id=instance_id,
            model_id="test-model",
            status=ModelStatus.SERVING,
            created_by=uuid.UUID(current_user.id)
        )

        success = await service.stop_model_serving(instance, current_user)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to stop model serving"
            )

        logger.info(f"Stopped serving model instance {instance_id}")
        
        return {
            "success": True,
            "message": f"Model instance {instance_id} serving stopped"
        }

    except PermissionError as e:
        logger.warning(f"Permission denied stopping model serving for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to stop model serving"
        )
    except Exception as e:
        logger.error(f"Failed to stop model serving: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to stop model serving"
        )


@router.get(
    "/instances/{instance_id}/health",
    response_model=ModelHealthResponse,
    summary="Check model health",
    description="Check the health status of a serving model"
)
async def check_model_health(
    instance_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    service: ModelManagementService = Depends(get_model_management_service),
) -> ModelHealthResponse:
    """Check health status of a serving model."""
    try:
        # TODO: Get instance from database
        from src.domain.entities.model_management import ModelInstance
        instance = ModelInstance(
            id=instance_id,
            model_id="test-model",
            status=ModelStatus.SERVING,
            serving_url="http://localhost:8000",
            api_endpoint="http://localhost:8000/v1/completions",
            created_by=uuid.UUID(current_user.id)
        )

        health_info = await service.check_model_health(instance)
        
        return ModelHealthResponse(**health_info)

    except Exception as e:
        logger.error(f"Failed to check model health: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to check model health"
        )
