"""
Enhanced exception handling system.

This module provides comprehensive exception classes and error handling patterns
for the Lonors AI Agent Platform.
"""

import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional, Union


class LonorsBaseException(Exception):
    """Base exception for all Lonors platform exceptions."""

    def __init__(
        self,
        message: str,
        error_code: str,
        details: Optional[Dict[str, Any]] = None,
        correlation_id: Optional[str] = None,
    ):
        """
        Initialize base exception.

        Args:
            message: Human-readable error message
            error_code: Machine-readable error code
            details: Additional error details
            correlation_id: Request correlation ID for tracing
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        self.correlation_id = correlation_id or str(uuid.uuid4())
        self.timestamp = datetime.utcnow()

    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary."""
        return {
            "error_code": self.error_code,
            "message": self.message,
            "details": self.details,
            "correlation_id": self.correlation_id,
            "timestamp": self.timestamp.isoformat(),
            "exception_type": self.__class__.__name__,
        }


# Domain-specific exceptions
class ValidationError(LonorsBaseException):
    """Raised when validation fails."""

    def __init__(
        self,
        message: str,
        field_errors: Optional[Dict[str, List[str]]] = None,
        correlation_id: Optional[str] = None,
    ):
        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR",
            details={"field_errors": field_errors or {}},
            correlation_id=correlation_id,
        )
        self.field_errors = field_errors or {}


class NotFoundError(LonorsBaseException):
    """Raised when a resource is not found."""

    def __init__(
        self,
        resource_type: str,
        resource_id: Union[str, uuid.UUID],
        correlation_id: Optional[str] = None,
    ):
        super().__init__(
            message=f"{resource_type} with ID {resource_id} not found",
            error_code="RESOURCE_NOT_FOUND",
            details={"resource_type": resource_type, "resource_id": str(resource_id)},
            correlation_id=correlation_id,
        )
        self.resource_type = resource_type
        self.resource_id = str(resource_id)


class PermissionDeniedError(LonorsBaseException):
    """Raised when user lacks required permissions."""

    def __init__(
        self,
        user_id: str,
        required_permission: str,
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
    ):
        super().__init__(
            message=f"User {user_id} lacks permission {required_permission}",
            error_code="PERMISSION_DENIED",
            details={
                "user_id": user_id,
                "required_permission": required_permission,
                "resource_type": resource_type,
                "resource_id": resource_id,
            },
            correlation_id=correlation_id,
        )
        self.user_id = user_id
        self.required_permission = required_permission


class ConflictError(LonorsBaseException):
    """Raised when a resource conflict occurs."""

    def __init__(
        self,
        message: str,
        conflicting_resource: Optional[str] = None,
        correlation_id: Optional[str] = None,
    ):
        super().__init__(
            message=message,
            error_code="RESOURCE_CONFLICT",
            details={"conflicting_resource": conflicting_resource},
            correlation_id=correlation_id,
        )


class RateLimitError(LonorsBaseException):
    """Raised when rate limit is exceeded."""

    def __init__(
        self,
        limit: int,
        window_seconds: int,
        retry_after: int,
        correlation_id: Optional[str] = None,
    ):
        super().__init__(
            message=f"Rate limit exceeded: {limit} requests per {window_seconds} seconds",
            error_code="RATE_LIMIT_EXCEEDED",
            details={
                "limit": limit,
                "window_seconds": window_seconds,
                "retry_after": retry_after,
            },
            correlation_id=correlation_id,
        )
        self.retry_after = retry_after


# Model Management specific exceptions
class ModelManagementError(LonorsBaseException):
    """Base exception for model management operations."""

    def __init__(
        self,
        message: str,
        error_code: str,
        model_id: Optional[str] = None,
        instance_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
    ):
        super().__init__(
            message=message,
            error_code=error_code,
            details={"model_id": model_id, "instance_id": instance_id},
            correlation_id=correlation_id,
        )
        self.model_id = model_id
        self.instance_id = instance_id


class ModelNotFoundError(ModelManagementError):
    """Raised when a model is not found."""

    def __init__(
        self,
        model_id: str,
        correlation_id: Optional[str] = None,
    ):
        super().__init__(
            message=f"Model {model_id} not found",
            error_code="MODEL_NOT_FOUND",
            model_id=model_id,
            correlation_id=correlation_id,
        )


class ModelDownloadError(ModelManagementError):
    """Raised when model download fails."""

    def __init__(
        self,
        model_id: str,
        reason: str,
        correlation_id: Optional[str] = None,
    ):
        super().__init__(
            message=f"Failed to download model {model_id}: {reason}",
            error_code="MODEL_DOWNLOAD_FAILED",
            model_id=model_id,
            correlation_id=correlation_id,
        )


class ModelServingError(ModelManagementError):
    """Raised when model serving fails."""

    def __init__(
        self,
        instance_id: str,
        reason: str,
        correlation_id: Optional[str] = None,
    ):
        super().__init__(
            message=f"Failed to serve model instance {instance_id}: {reason}",
            error_code="MODEL_SERVING_FAILED",
            instance_id=instance_id,
            correlation_id=correlation_id,
        )


# Workflow specific exceptions
class WorkflowError(LonorsBaseException):
    """Base exception for workflow operations."""

    def __init__(
        self,
        message: str,
        error_code: str,
        workflow_id: Optional[str] = None,
        execution_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
    ):
        super().__init__(
            message=message,
            error_code=error_code,
            details={"workflow_id": workflow_id, "execution_id": execution_id},
            correlation_id=correlation_id,
        )
        self.workflow_id = workflow_id
        self.execution_id = execution_id


class WorkflowValidationError(WorkflowError):
    """Raised when workflow validation fails."""

    def __init__(
        self,
        workflow_id: str,
        validation_errors: List[str],
        correlation_id: Optional[str] = None,
    ):
        super().__init__(
            message=f"Workflow {workflow_id} validation failed",
            error_code="WORKFLOW_VALIDATION_FAILED",
            workflow_id=workflow_id,
            correlation_id=correlation_id,
        )
        self.validation_errors = validation_errors
        self.details["validation_errors"] = validation_errors


class WorkflowExecutionError(WorkflowError):
    """Raised when workflow execution fails."""

    def __init__(
        self,
        execution_id: str,
        workflow_id: str,
        reason: str,
        failed_node_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
    ):
        super().__init__(
            message=f"Workflow execution {execution_id} failed: {reason}",
            error_code="WORKFLOW_EXECUTION_FAILED",
            workflow_id=workflow_id,
            execution_id=execution_id,
            correlation_id=correlation_id,
        )
        self.failed_node_id = failed_node_id
        self.details["failed_node_id"] = failed_node_id


# Knowledge Graph specific exceptions
class KnowledgeGraphError(LonorsBaseException):
    """Base exception for knowledge graph operations."""

    def __init__(
        self,
        message: str,
        error_code: str,
        entity_id: Optional[str] = None,
        relationship_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
    ):
        super().__init__(
            message=message,
            error_code=error_code,
            details={"entity_id": entity_id, "relationship_id": relationship_id},
            correlation_id=correlation_id,
        )
        self.entity_id = entity_id
        self.relationship_id = relationship_id


class EntityNotFoundError(KnowledgeGraphError):
    """Raised when a knowledge entity is not found."""

    def __init__(
        self,
        entity_id: str,
        correlation_id: Optional[str] = None,
    ):
        super().__init__(
            message=f"Knowledge entity {entity_id} not found",
            error_code="ENTITY_NOT_FOUND",
            entity_id=entity_id,
            correlation_id=correlation_id,
        )


class GraphQueryError(KnowledgeGraphError):
    """Raised when graph query fails."""

    def __init__(
        self,
        query: str,
        reason: str,
        correlation_id: Optional[str] = None,
    ):
        super().__init__(
            message=f"Graph query failed: {reason}",
            error_code="GRAPH_QUERY_FAILED",
            correlation_id=correlation_id,
        )
        self.query = query
        self.details["query"] = query


# WebSocket specific exceptions
class WebSocketError(LonorsBaseException):
    """Base exception for WebSocket operations."""

    def __init__(
        self,
        message: str,
        error_code: str,
        connection_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
    ):
        super().__init__(
            message=message,
            error_code=error_code,
            details={"connection_id": connection_id},
            correlation_id=correlation_id,
        )
        self.connection_id = connection_id


class WebSocketConnectionError(WebSocketError):
    """Raised when WebSocket connection fails."""

    def __init__(
        self,
        reason: str,
        connection_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
    ):
        super().__init__(
            message=f"WebSocket connection failed: {reason}",
            error_code="WEBSOCKET_CONNECTION_FAILED",
            connection_id=connection_id,
            correlation_id=correlation_id,
        )


class WebSocketMessageError(WebSocketError):
    """Raised when WebSocket message processing fails."""

    def __init__(
        self,
        message_type: str,
        reason: str,
        connection_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
    ):
        super().__init__(
            message=f"WebSocket message processing failed for {message_type}: {reason}",
            error_code="WEBSOCKET_MESSAGE_FAILED",
            connection_id=connection_id,
            correlation_id=correlation_id,
        )
        self.message_type = message_type
        self.details["message_type"] = message_type


# External service exceptions
class ExternalServiceError(LonorsBaseException):
    """Base exception for external service operations."""

    def __init__(
        self,
        service_name: str,
        message: str,
        error_code: str,
        status_code: Optional[int] = None,
        correlation_id: Optional[str] = None,
    ):
        super().__init__(
            message=f"{service_name}: {message}",
            error_code=error_code,
            details={"service_name": service_name, "status_code": status_code},
            correlation_id=correlation_id,
        )
        self.service_name = service_name
        self.status_code = status_code


class HuggingFaceAPIError(ExternalServiceError):
    """Raised when HuggingFace API calls fail."""

    def __init__(
        self,
        message: str,
        status_code: Optional[int] = None,
        correlation_id: Optional[str] = None,
    ):
        super().__init__(
            service_name="HuggingFace",
            message=message,
            error_code="HUGGINGFACE_API_ERROR",
            status_code=status_code,
            correlation_id=correlation_id,
        )


class OllamaAPIError(ExternalServiceError):
    """Raised when Ollama API calls fail."""

    def __init__(
        self,
        message: str,
        status_code: Optional[int] = None,
        correlation_id: Optional[str] = None,
    ):
        super().__init__(
            service_name="Ollama",
            message=message,
            error_code="OLLAMA_API_ERROR",
            status_code=status_code,
            correlation_id=correlation_id,
        )
