'use client';

import React, { useState, useMemo } from 'react';
import { cn } from '@/shared/lib/utils';
import { Button } from '@/shared/ui/button';
import { Input } from '@/shared/ui/input';
import { Badge } from '@/shared/ui/badge';
import { ScrollArea } from '@/shared/ui/scroll-area';
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/shared/ui/tabs';
import { 
  NodeTemplate, 
  NodeCategory, 
  NodeType, 
  PortType,
  WorkflowComplexity 
} from '../../types';
import { AgentType } from '@/shared/types';
import {
  Search,
  Bot,
  Tool,
  GitBranch,
  ArrowRight,
  ArrowLeft,
  Zap,
  Database,
  Globe,
  FileText,
  Image,
  MessageSquare,
  Calculator,
  Brain,
  Sparkles,
  Workflow,
  Play,
  Settings,
  Filter,
  Star,
} from 'lucide-react';

interface NodeLibraryProps {
  onNodeDragStart: (event: React.DragEvent, nodeTemplate: NodeTemplate) => void;
  className?: string;
  collapsed?: boolean;
  onToggleCollapse?: () => void;
}

// Predefined node templates
const nodeTemplates: NodeTemplate[] = [
  // Agent Nodes
  {
    id: 'chat-agent',
    type: NodeType.AGENT,
    name: 'Chat Agent',
    description: 'AI agent for conversational interactions and customer support',
    category: NodeCategory.AGENTS,
    icon: MessageSquare,
    defaultData: {
      agent: {
        agent_type: AgentType.CHAT,
        name: 'Chat Agent',
        description: 'Conversational AI agent',
      }
    },
    inputs: [
      { id: 'message', name: 'Message', type: PortType.STRING, required: true, description: 'Input message' }
    ],
    outputs: [
      { id: 'response', name: 'Response', type: PortType.STRING, required: true, description: 'Agent response' }
    ],
    tags: ['chat', 'conversation', 'ai'],
    complexity: WorkflowComplexity.SIMPLE,
  },
  {
    id: 'reasoning-agent',
    type: NodeType.AGENT,
    name: 'Reasoning Agent',
    description: 'Advanced AI agent for complex reasoning and analysis',
    category: NodeCategory.AGENTS,
    icon: Brain,
    defaultData: {
      agent: {
        agent_type: AgentType.REASONING,
        name: 'Reasoning Agent',
        description: 'Advanced reasoning AI agent',
      }
    },
    inputs: [
      { id: 'problem', name: 'Problem', type: PortType.STRING, required: true, description: 'Problem to analyze' },
      { id: 'context', name: 'Context', type: PortType.OBJECT, required: false, description: 'Additional context' }
    ],
    outputs: [
      { id: 'analysis', name: 'Analysis', type: PortType.OBJECT, required: true, description: 'Reasoning analysis' },
      { id: 'conclusion', name: 'Conclusion', type: PortType.STRING, required: true, description: 'Final conclusion' }
    ],
    tags: ['reasoning', 'analysis', 'ai'],
    complexity: WorkflowComplexity.COMPLEX,
  },
  
  // Tool Nodes
  {
    id: 'api-call',
    type: NodeType.TOOL,
    name: 'API Call',
    description: 'Make HTTP requests to external APIs',
    category: NodeCategory.TOOLS,
    icon: Zap,
    defaultData: {
      toolType: 'api',
      toolName: 'API Call',
      description: 'HTTP API request tool',
    },
    inputs: [
      { id: 'url', name: 'URL', type: PortType.STRING, required: true, description: 'API endpoint URL' },
      { id: 'method', name: 'Method', type: PortType.STRING, required: true, description: 'HTTP method' },
      { id: 'headers', name: 'Headers', type: PortType.OBJECT, required: false, description: 'Request headers' },
      { id: 'body', name: 'Body', type: PortType.OBJECT, required: false, description: 'Request body' }
    ],
    outputs: [
      { id: 'response', name: 'Response', type: PortType.OBJECT, required: true, description: 'API response' },
      { id: 'status', name: 'Status', type: PortType.NUMBER, required: true, description: 'HTTP status code' }
    ],
    tags: ['api', 'http', 'request'],
    complexity: WorkflowComplexity.MODERATE,
  },
  {
    id: 'database-query',
    type: NodeType.TOOL,
    name: 'Database Query',
    description: 'Execute database queries and retrieve data',
    category: NodeCategory.TOOLS,
    icon: Database,
    defaultData: {
      toolType: 'database',
      toolName: 'Database Query',
      description: 'Database query execution tool',
    },
    inputs: [
      { id: 'query', name: 'Query', type: PortType.STRING, required: true, description: 'SQL query' },
      { id: 'parameters', name: 'Parameters', type: PortType.OBJECT, required: false, description: 'Query parameters' }
    ],
    outputs: [
      { id: 'results', name: 'Results', type: PortType.ARRAY, required: true, description: 'Query results' },
      { id: 'count', name: 'Count', type: PortType.NUMBER, required: true, description: 'Number of results' }
    ],
    tags: ['database', 'sql', 'query'],
    complexity: WorkflowComplexity.MODERATE,
  },
  
  // Logic Nodes
  {
    id: 'condition',
    type: NodeType.CONDITION,
    name: 'Condition',
    description: 'Conditional branching based on input values',
    category: NodeCategory.LOGIC,
    icon: GitBranch,
    defaultData: {
      conditionType: 'simple',
      condition: '',
      description: 'Conditional logic node',
    },
    inputs: [
      { id: 'input', name: 'Input', type: PortType.ANY, required: true, description: 'Value to evaluate' }
    ],
    outputs: [
      { id: 'true', name: 'True', type: PortType.ANY, required: true, description: 'True branch output' },
      { id: 'false', name: 'False', type: PortType.ANY, required: true, description: 'False branch output' }
    ],
    tags: ['condition', 'logic', 'branch'],
    complexity: WorkflowComplexity.SIMPLE,
  },
  
  // Input/Output Nodes
  {
    id: 'text-input',
    type: NodeType.INPUT,
    name: 'Text Input',
    description: 'Text input for workflow data',
    category: NodeCategory.DATA,
    icon: ArrowRight,
    defaultData: {
      inputType: PortType.STRING,
      inputName: 'Text Input',
      description: 'Text input node',
    },
    inputs: [],
    outputs: [
      { id: 'value', name: 'Value', type: PortType.STRING, required: true, description: 'Input text value' }
    ],
    tags: ['input', 'text', 'data'],
    complexity: WorkflowComplexity.SIMPLE,
  },
  {
    id: 'file-output',
    type: NodeType.OUTPUT,
    name: 'File Output',
    description: 'Save workflow results to file',
    category: NodeCategory.DATA,
    icon: ArrowLeft,
    defaultData: {
      outputType: PortType.FILE,
      outputName: 'File Output',
      description: 'File output node',
    },
    inputs: [
      { id: 'data', name: 'Data', type: PortType.ANY, required: true, description: 'Data to save' },
      { id: 'filename', name: 'Filename', type: PortType.STRING, required: false, description: 'Output filename' }
    ],
    outputs: [],
    tags: ['output', 'file', 'save'],
    complexity: WorkflowComplexity.SIMPLE,
  },
];

const NodeLibrary: React.FC<NodeLibraryProps> = ({
  onNodeDragStart,
  className,
  collapsed = false,
  onToggleCollapse,
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<NodeCategory | 'all'>('all');
  const [complexityFilter, setComplexityFilter] = useState<WorkflowComplexity | 'all'>('all');

  // Filter templates based on search and filters
  const filteredTemplates = useMemo(() => {
    return nodeTemplates.filter(template => {
      const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           template.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           template.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
      
      const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
      const matchesComplexity = complexityFilter === 'all' || template.complexity === complexityFilter;
      
      return matchesSearch && matchesCategory && matchesComplexity;
    });
  }, [searchTerm, selectedCategory, complexityFilter]);

  // Group templates by category
  const templatesByCategory = useMemo(() => {
    const grouped: Record<NodeCategory, NodeTemplate[]> = {} as Record<NodeCategory, NodeTemplate[]>;
    
    Object.values(NodeCategory).forEach(category => {
      grouped[category] = filteredTemplates.filter(template => template.category === category);
    });
    
    return grouped;
  }, [filteredTemplates]);

  const handleDragStart = (event: React.DragEvent, template: NodeTemplate) => {
    event.dataTransfer.effectAllowed = 'move';
    event.dataTransfer.setData('application/reactflow', JSON.stringify({
      type: 'nodeTemplate',
      template,
    }));
    onNodeDragStart(event, template);
  };

  const getComplexityColor = (complexity: WorkflowComplexity) => {
    switch (complexity) {
      case WorkflowComplexity.SIMPLE:
        return 'bg-green-100 text-green-800';
      case WorkflowComplexity.MODERATE:
        return 'bg-yellow-100 text-yellow-800';
      case WorkflowComplexity.COMPLEX:
        return 'bg-orange-100 text-orange-800';
      case WorkflowComplexity.ADVANCED:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (collapsed) {
    return (
      <div className={cn('w-12 bg-background border-r border-border', className)}>
        <div className="p-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleCollapse}
            className="w-full"
          >
            <Bot className="h-4 w-4" />
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('w-80 bg-background border-r border-border flex flex-col', className)}>
      {/* Header */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-lg font-semibold">Node Library</h2>
          {onToggleCollapse && (
            <Button variant="ghost" size="sm" onClick={onToggleCollapse}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
          )}
        </div>
        
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search nodes..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-9"
          />
        </div>
      </div>

      {/* Filters */}
      <div className="p-4 border-b border-border space-y-3">
        <div>
          <label className="text-sm font-medium mb-2 block">Category</label>
          <Tabs value={selectedCategory} onValueChange={(value) => setSelectedCategory(value as NodeCategory | 'all')}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="all" className="text-xs">All</TabsTrigger>
              <TabsTrigger value={NodeCategory.AGENTS} className="text-xs">Agents</TabsTrigger>
              <TabsTrigger value={NodeCategory.TOOLS} className="text-xs">Tools</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
        
        <div>
          <label className="text-sm font-medium mb-2 block">Complexity</label>
          <select
            value={complexityFilter}
            onChange={(e) => setComplexityFilter(e.target.value as WorkflowComplexity | 'all')}
            className="w-full p-2 border border-border rounded-md text-sm"
          >
            <option value="all">All Levels</option>
            <option value={WorkflowComplexity.SIMPLE}>Simple</option>
            <option value={WorkflowComplexity.MODERATE}>Moderate</option>
            <option value={WorkflowComplexity.COMPLEX}>Complex</option>
            <option value={WorkflowComplexity.ADVANCED}>Advanced</option>
          </select>
        </div>
      </div>

      {/* Node Templates */}
      <ScrollArea className="flex-1">
        <div className="p-4 space-y-4">
          {selectedCategory === 'all' ? (
            // Show all categories
            Object.entries(templatesByCategory).map(([category, templates]) => (
              templates.length > 0 && (
                <div key={category}>
                  <h3 className="text-sm font-medium text-muted-foreground mb-2 capitalize">
                    {category}
                  </h3>
                  <div className="space-y-2">
                    {templates.map((template) => (
                      <div
                        key={template.id}
                        draggable
                        onDragStart={(e) => handleDragStart(e, template)}
                        className="p-3 border border-border rounded-lg cursor-grab hover:shadow-md transition-shadow bg-card"
                      >
                        <div className="flex items-start gap-3">
                          <div className="p-2 rounded-md bg-primary/10 text-primary">
                            <template.icon className="h-4 w-4" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <h4 className="font-medium text-sm truncate">{template.name}</h4>
                            <p className="text-xs text-muted-foreground line-clamp-2 mt-1">
                              {template.description}
                            </p>
                            <div className="flex items-center gap-1 mt-2">
                              <Badge variant="outline" className="text-xs">
                                {template.type}
                              </Badge>
                              <Badge className={cn('text-xs', getComplexityColor(template.complexity))}>
                                {template.complexity}
                              </Badge>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )
            ))
          ) : (
            // Show selected category only
            <div className="space-y-2">
              {filteredTemplates.map((template) => (
                <div
                  key={template.id}
                  draggable
                  onDragStart={(e) => handleDragStart(e, template)}
                  className="p-3 border border-border rounded-lg cursor-grab hover:shadow-md transition-shadow bg-card"
                >
                  <div className="flex items-start gap-3">
                    <div className="p-2 rounded-md bg-primary/10 text-primary">
                      <template.icon className="h-4 w-4" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium text-sm truncate">{template.name}</h4>
                      <p className="text-xs text-muted-foreground line-clamp-2 mt-1">
                        {template.description}
                      </p>
                      <div className="flex items-center gap-1 mt-2">
                        <Badge variant="outline" className="text-xs">
                          {template.type}
                        </Badge>
                        <Badge className={cn('text-xs', getComplexityColor(template.complexity))}>
                          {template.complexity}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2 mt-2 text-xs text-muted-foreground">
                        <span>In: {template.inputs.length}</span>
                        <span>Out: {template.outputs.length}</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
          
          {filteredTemplates.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <Bot className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No nodes found</p>
              <p className="text-xs">Try adjusting your search or filters</p>
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  );
};

export default NodeLibrary;
