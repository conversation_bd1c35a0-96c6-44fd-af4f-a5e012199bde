"""
Authentication Integration Tests

Integration tests for the complete authentication and authorization system.
"""

import uuid
from unittest.mock import AsyncMock, patch

import pytest
from fastapi import FastAPI, Depends
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession

from src.domain.entities.user import Permission, User, UserRole, UserStatus
from src.domain.value_objects.email import Email
from src.domain.value_objects.username import Username
from src.presentation.dependencies.auth import require_permissions, require_roles


# Create test FastAPI app
app = FastAPI()


@app.get("/admin-only")
async def admin_only_endpoint(
    current_user: dict = Depends(require_roles(UserRole.ADMIN.value))
):
    """Endpoint that requires admin role."""
    return {"message": "Admin access granted", "user_id": current_user["user_id"]}


@app.get("/workflow-create")
async def workflow_create_endpoint(
    current_user: dict = Depends(require_permissions(Permission.WORKFLOW_CREATE.value))
):
    """Endpoint that requires workflow creation permission."""
    return {"message": "Workflow creation allowed", "user_id": current_user["user_id"]}


@app.get("/user-or-admin")
async def user_or_admin_endpoint(
    current_user: dict = Depends(require_roles(UserRole.USER.value, UserRole.ADMIN.value))
):
    """Endpoint that requires user or admin role."""
    return {"message": "User or admin access", "user_id": current_user["user_id"]}


@app.get("/multiple-permissions")
async def multiple_permissions_endpoint(
    current_user: dict = Depends(
        require_permissions(
            Permission.WORKFLOW_CREATE.value,
            Permission.AGENT_READ.value
        )
    )
):
    """Endpoint that requires multiple permissions."""
    return {"message": "Multiple permissions granted", "user_id": current_user["user_id"]}


@pytest.fixture
def client():
    """Create test client."""
    return TestClient(app)


@pytest.fixture
def sample_user_id():
    """Sample user ID for testing."""
    return uuid.uuid4()


@pytest.fixture
def admin_user(sample_user_id):
    """Create admin user entity."""
    return User(
        id=str(sample_user_id),
        email=Email("<EMAIL>"),
        username=Username("admin"),
        full_name="Admin User",
        hashed_password="hashed_password",
        role=UserRole.ADMIN,
        status=UserStatus.ACTIVE,
        is_active=True,
        is_verified=True,
    )


@pytest.fixture
def regular_user(sample_user_id):
    """Create regular user entity."""
    return User(
        id=str(sample_user_id),
        email=Email("<EMAIL>"),
        username=Username("user"),
        full_name="Regular User",
        hashed_password="hashed_password",
        role=UserRole.USER,
        status=UserStatus.ACTIVE,
        is_active=True,
        is_verified=True,
    )


@pytest.fixture
def viewer_user(sample_user_id):
    """Create viewer user entity."""
    return User(
        id=str(sample_user_id),
        email=Email("<EMAIL>"),
        username=Username("viewer"),
        full_name="Viewer User",
        hashed_password="hashed_password",
        role=UserRole.VIEWER,
        status=UserStatus.ACTIVE,
        is_active=True,
        is_verified=True,
    )


@pytest.fixture
def mock_jwt_token():
    """Mock JWT token for testing."""
    return "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.test.token"


class TestRoleBasedEndpoints:
    """Test role-based endpoint access."""

    @patch("src.infrastructure.database.repositories.user_repository.UserRepository")
    @patch("src.infrastructure.security.jwt.jwt_manager.verify_token")
    @patch("src.infrastructure.database.connection.get_db_session")
    def test_admin_endpoint_success(
        self, mock_get_db, mock_verify_token, mock_user_repo_class, 
        client, admin_user, sample_user_id, mock_jwt_token
    ):
        """Test admin endpoint access with admin user."""
        # Setup mocks
        mock_verify_token.return_value = {
            "user_id": str(sample_user_id),
            "sub": "<EMAIL>",
            "jti": "token-id"
        }
        
        mock_db = AsyncMock(spec=AsyncSession)
        mock_get_db.return_value.__aenter__.return_value = mock_db
        
        mock_user_repo = AsyncMock()
        mock_user_repo.get_by_id.return_value = admin_user
        mock_user_repo_class.return_value = mock_user_repo

        # Make request
        response = client.get(
            "/admin-only",
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        # Verify
        assert response.status_code == 200
        assert response.json()["message"] == "Admin access granted"
        assert response.json()["user_id"] == str(sample_user_id)

    @patch("src.infrastructure.database.repositories.user_repository.UserRepository")
    @patch("src.infrastructure.security.jwt.jwt_manager.verify_token")
    @patch("src.infrastructure.database.connection.get_db_session")
    def test_admin_endpoint_failure_regular_user(
        self, mock_get_db, mock_verify_token, mock_user_repo_class,
        client, regular_user, sample_user_id, mock_jwt_token
    ):
        """Test admin endpoint access denied for regular user."""
        # Setup mocks
        mock_verify_token.return_value = {
            "user_id": str(sample_user_id),
            "sub": "<EMAIL>",
            "jti": "token-id"
        }
        
        mock_db = AsyncMock(spec=AsyncSession)
        mock_get_db.return_value.__aenter__.return_value = mock_db
        
        mock_user_repo = AsyncMock()
        mock_user_repo.get_by_id.return_value = regular_user
        mock_user_repo_class.return_value = mock_user_repo

        # Make request
        response = client.get(
            "/admin-only",
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        # Verify
        assert response.status_code == 403
        assert "Insufficient role" in response.json()["detail"]

    @patch("src.infrastructure.database.repositories.user_repository.UserRepository")
    @patch("src.infrastructure.security.jwt.jwt_manager.verify_token")
    @patch("src.infrastructure.database.connection.get_db_session")
    def test_user_or_admin_endpoint_success_regular_user(
        self, mock_get_db, mock_verify_token, mock_user_repo_class,
        client, regular_user, sample_user_id, mock_jwt_token
    ):
        """Test user-or-admin endpoint access with regular user."""
        # Setup mocks
        mock_verify_token.return_value = {
            "user_id": str(sample_user_id),
            "sub": "<EMAIL>",
            "jti": "token-id"
        }
        
        mock_db = AsyncMock(spec=AsyncSession)
        mock_get_db.return_value.__aenter__.return_value = mock_db
        
        mock_user_repo = AsyncMock()
        mock_user_repo.get_by_id.return_value = regular_user
        mock_user_repo_class.return_value = mock_user_repo

        # Make request
        response = client.get(
            "/user-or-admin",
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        # Verify
        assert response.status_code == 200
        assert response.json()["message"] == "User or admin access"


class TestPermissionBasedEndpoints:
    """Test permission-based endpoint access."""

    @patch("src.infrastructure.database.repositories.user_repository.UserRepository")
    @patch("src.infrastructure.security.jwt.jwt_manager.verify_token")
    @patch("src.infrastructure.database.connection.get_db_session")
    def test_workflow_create_success_regular_user(
        self, mock_get_db, mock_verify_token, mock_user_repo_class,
        client, regular_user, sample_user_id, mock_jwt_token
    ):
        """Test workflow creation endpoint with regular user (has permission)."""
        # Setup mocks
        mock_verify_token.return_value = {
            "user_id": str(sample_user_id),
            "sub": "<EMAIL>",
            "jti": "token-id"
        }
        
        mock_db = AsyncMock(spec=AsyncSession)
        mock_get_db.return_value.__aenter__.return_value = mock_db
        
        mock_user_repo = AsyncMock()
        mock_user_repo.get_by_id.return_value = regular_user
        mock_user_repo_class.return_value = mock_user_repo

        # Make request
        response = client.get(
            "/workflow-create",
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        # Verify
        assert response.status_code == 200
        assert response.json()["message"] == "Workflow creation allowed"

    @patch("src.infrastructure.database.repositories.user_repository.UserRepository")
    @patch("src.infrastructure.security.jwt.jwt_manager.verify_token")
    @patch("src.infrastructure.database.connection.get_db_session")
    def test_workflow_create_failure_viewer(
        self, mock_get_db, mock_verify_token, mock_user_repo_class,
        client, viewer_user, sample_user_id, mock_jwt_token
    ):
        """Test workflow creation endpoint denied for viewer (no permission)."""
        # Setup mocks
        mock_verify_token.return_value = {
            "user_id": str(sample_user_id),
            "sub": "<EMAIL>",
            "jti": "token-id"
        }
        
        mock_db = AsyncMock(spec=AsyncSession)
        mock_get_db.return_value.__aenter__.return_value = mock_db
        
        mock_user_repo = AsyncMock()
        mock_user_repo.get_by_id.return_value = viewer_user
        mock_user_repo_class.return_value = mock_user_repo

        # Make request
        response = client.get(
            "/workflow-create",
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        # Verify
        assert response.status_code == 403
        assert "Insufficient permissions" in response.json()["detail"]

    @patch("src.infrastructure.database.repositories.user_repository.UserRepository")
    @patch("src.infrastructure.security.jwt.jwt_manager.verify_token")
    @patch("src.infrastructure.database.connection.get_db_session")
    def test_multiple_permissions_success_admin(
        self, mock_get_db, mock_verify_token, mock_user_repo_class,
        client, admin_user, sample_user_id, mock_jwt_token
    ):
        """Test multiple permissions endpoint with admin user."""
        # Setup mocks
        mock_verify_token.return_value = {
            "user_id": str(sample_user_id),
            "sub": "<EMAIL>",
            "jti": "token-id"
        }
        
        mock_db = AsyncMock(spec=AsyncSession)
        mock_get_db.return_value.__aenter__.return_value = mock_db
        
        mock_user_repo = AsyncMock()
        mock_user_repo.get_by_id.return_value = admin_user
        mock_user_repo_class.return_value = mock_user_repo

        # Make request
        response = client.get(
            "/multiple-permissions",
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        # Verify
        assert response.status_code == 200
        assert response.json()["message"] == "Multiple permissions granted"


class TestAuthenticationFailures:
    """Test authentication failure scenarios."""

    def test_no_token_provided(self, client):
        """Test endpoint access without token."""
        response = client.get("/admin-only")
        assert response.status_code == 401
        assert "Missing authentication token" in response.json()["detail"]

    @patch("src.infrastructure.security.jwt.jwt_manager.verify_token")
    def test_invalid_token(self, mock_verify_token, client):
        """Test endpoint access with invalid token."""
        from jose import JWTError
        
        mock_verify_token.side_effect = JWTError("Invalid token")
        
        response = client.get(
            "/admin-only",
            headers={"Authorization": "Bearer invalid.token.here"}
        )
        
        assert response.status_code == 401
        assert "Invalid or expired token" in response.json()["detail"]
