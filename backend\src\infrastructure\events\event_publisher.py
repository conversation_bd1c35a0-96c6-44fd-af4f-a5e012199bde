"""
Event Publisher Implementation.

This module provides the event publishing infrastructure for domain events.
"""

import asyncio
import json
import uuid
from typing import Any, Callable, Dict, List, Optional, Type

from src.domain.events.base import DomainEvent
from src.infrastructure.logging.setup import get_logger

logger = get_logger(__name__)


class EventHandler:
    """Event handler wrapper."""

    def __init__(self, handler: Callable, event_type: str, handler_id: str):
        self.handler = handler
        self.event_type = event_type
        self.handler_id = handler_id


class EventPublisher:
    """
    Event publisher for domain events.
    
    Provides in-memory event publishing with handler registration and
    asynchronous event processing.
    """

    def __init__(self):
        """Initialize event publisher."""
        self._handlers: Dict[str, List[EventHandler]] = {}
        self._global_handlers: List[EventHandler] = []
        self._event_queue: asyncio.Queue = asyncio.Queue()
        self._processing_task: Optional[asyncio.Task] = None
        self._is_running = False

    async def start(self) -> None:
        """Start the event processing loop."""
        if self._is_running:
            return

        self._is_running = True
        self._processing_task = asyncio.create_task(self._process_events())
        logger.info("Event publisher started")

    async def stop(self) -> None:
        """Stop the event processing loop."""
        if not self._is_running:
            return

        self._is_running = False
        
        if self._processing_task:
            self._processing_task.cancel()
            try:
                await self._processing_task
            except asyncio.CancelledError:
                pass

        logger.info("Event publisher stopped")

    def subscribe(
        self,
        event_type: str,
        handler: Callable[[DomainEvent], None],
        handler_id: Optional[str] = None,
    ) -> str:
        """
        Subscribe to events of a specific type.

        Args:
            event_type: Type of event to subscribe to
            handler: Event handler function
            handler_id: Optional handler ID (auto-generated if not provided)

        Returns:
            Handler ID for unsubscribing
        """
        if handler_id is None:
            handler_id = str(uuid.uuid4())

        event_handler = EventHandler(handler, event_type, handler_id)

        if event_type not in self._handlers:
            self._handlers[event_type] = []

        self._handlers[event_type].append(event_handler)
        
        logger.info(f"Subscribed handler {handler_id} to event type {event_type}")
        return handler_id

    def subscribe_global(
        self,
        handler: Callable[[DomainEvent], None],
        handler_id: Optional[str] = None,
    ) -> str:
        """
        Subscribe to all events.

        Args:
            handler: Event handler function
            handler_id: Optional handler ID (auto-generated if not provided)

        Returns:
            Handler ID for unsubscribing
        """
        if handler_id is None:
            handler_id = str(uuid.uuid4())

        event_handler = EventHandler(handler, "*", handler_id)
        self._global_handlers.append(event_handler)
        
        logger.info(f"Subscribed global handler {handler_id}")
        return handler_id

    def unsubscribe(self, handler_id: str) -> bool:
        """
        Unsubscribe a handler.

        Args:
            handler_id: Handler ID to unsubscribe

        Returns:
            True if handler was found and removed, False otherwise
        """
        # Check global handlers
        for i, handler in enumerate(self._global_handlers):
            if handler.handler_id == handler_id:
                self._global_handlers.pop(i)
                logger.info(f"Unsubscribed global handler {handler_id}")
                return True

        # Check event-specific handlers
        for event_type, handlers in self._handlers.items():
            for i, handler in enumerate(handlers):
                if handler.handler_id == handler_id:
                    handlers.pop(i)
                    logger.info(f"Unsubscribed handler {handler_id} from event type {event_type}")
                    return True

        logger.warning(f"Handler {handler_id} not found for unsubscription")
        return False

    async def publish(self, event: DomainEvent) -> None:
        """
        Publish an event.

        Args:
            event: Domain event to publish
        """
        if not self._is_running:
            logger.warning("Event publisher not running, starting automatically")
            await self.start()

        await self._event_queue.put(event)
        logger.debug(f"Published event {event.event_type} with ID {event.event_id}")

    async def publish_sync(self, event: DomainEvent) -> None:
        """
        Publish an event synchronously (process immediately).

        Args:
            event: Domain event to publish
        """
        await self._handle_event(event)

    async def _process_events(self) -> None:
        """Process events from the queue."""
        logger.info("Started event processing loop")
        
        try:
            while self._is_running:
                try:
                    # Wait for event with timeout to allow checking _is_running
                    event = await asyncio.wait_for(
                        self._event_queue.get(),
                        timeout=1.0
                    )
                    await self._handle_event(event)
                    
                except asyncio.TimeoutError:
                    # Timeout is expected, continue loop
                    continue
                except Exception as e:
                    logger.error(f"Error processing event: {e}")
                    
        except asyncio.CancelledError:
            logger.info("Event processing loop cancelled")
            raise
        except Exception as e:
            logger.error(f"Event processing loop error: {e}")

    async def _handle_event(self, event: DomainEvent) -> None:
        """
        Handle a single event by calling all registered handlers.

        Args:
            event: Domain event to handle
        """
        event_type = event.event_type
        handlers_called = 0

        try:
            # Call global handlers
            for handler in self._global_handlers:
                try:
                    if asyncio.iscoroutinefunction(handler.handler):
                        await handler.handler(event)
                    else:
                        handler.handler(event)
                    handlers_called += 1
                except Exception as e:
                    logger.error(
                        f"Error in global handler {handler.handler_id} for event {event_type}: {e}"
                    )

            # Call event-specific handlers
            if event_type in self._handlers:
                for handler in self._handlers[event_type]:
                    try:
                        if asyncio.iscoroutinefunction(handler.handler):
                            await handler.handler(event)
                        else:
                            handler.handler(event)
                        handlers_called += 1
                    except Exception as e:
                        logger.error(
                            f"Error in handler {handler.handler_id} for event {event_type}: {e}"
                        )

            logger.debug(f"Handled event {event_type} with {handlers_called} handlers")

        except Exception as e:
            logger.error(f"Error handling event {event_type}: {e}")

    def get_handler_count(self, event_type: Optional[str] = None) -> int:
        """
        Get the number of registered handlers.

        Args:
            event_type: Optional event type to count handlers for

        Returns:
            Number of handlers
        """
        if event_type is None:
            # Count all handlers
            total = len(self._global_handlers)
            for handlers in self._handlers.values():
                total += len(handlers)
            return total
        elif event_type == "*":
            return len(self._global_handlers)
        else:
            return len(self._handlers.get(event_type, []))

    def get_event_types(self) -> List[str]:
        """
        Get all registered event types.

        Returns:
            List of event types
        """
        return list(self._handlers.keys())


# Global event publisher instance
_event_publisher: Optional[EventPublisher] = None


def get_event_publisher() -> EventPublisher:
    """
    Get the global event publisher instance.

    Returns:
        EventPublisher instance
    """
    global _event_publisher
    if _event_publisher is None:
        _event_publisher = EventPublisher()
    return _event_publisher


async def initialize_event_publisher() -> EventPublisher:
    """
    Initialize and start the global event publisher.

    Returns:
        EventPublisher instance
    """
    publisher = get_event_publisher()
    await publisher.start()
    return publisher


async def shutdown_event_publisher() -> None:
    """Shutdown the global event publisher."""
    global _event_publisher
    if _event_publisher:
        await _event_publisher.stop()
        _event_publisher = None


# Convenience functions for common operations
async def publish_event(event: DomainEvent) -> None:
    """
    Publish an event using the global publisher.

    Args:
        event: Domain event to publish
    """
    publisher = get_event_publisher()
    await publisher.publish(event)


def subscribe_to_event(
    event_type: str,
    handler: Callable[[DomainEvent], None],
    handler_id: Optional[str] = None,
) -> str:
    """
    Subscribe to events using the global publisher.

    Args:
        event_type: Type of event to subscribe to
        handler: Event handler function
        handler_id: Optional handler ID

    Returns:
        Handler ID
    """
    publisher = get_event_publisher()
    return publisher.subscribe(event_type, handler, handler_id)


def subscribe_to_all_events(
    handler: Callable[[DomainEvent], None],
    handler_id: Optional[str] = None,
) -> str:
    """
    Subscribe to all events using the global publisher.

    Args:
        handler: Event handler function
        handler_id: Optional handler ID

    Returns:
        Handler ID
    """
    publisher = get_event_publisher()
    return publisher.subscribe_global(handler, handler_id)
