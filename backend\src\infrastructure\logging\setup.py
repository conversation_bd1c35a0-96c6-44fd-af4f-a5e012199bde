"""
Logging configuration and setup.

This module provides structured logging setup with support for both
JSON and text formats, suitable for development and production environments.
"""

import logging
import logging.config
import sys
from pathlib import Path
from typing import Any

import structlog


def setup_logging(log_level: str = "INFO", log_format: str = "json") -> None:
    """
    Set up structured logging configuration.

    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_format: Log format ("json" or "text")
    """
    # Ensure logs directory exists
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)

    # Configure structlog
    structlog.configure(
        processors=[
            structlog.contextvars.merge_contextvars,
            structlog.processors.add_log_level,
            structlog.processors.StackInfoRenderer(),
            structlog.dev.set_exc_info,
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.JSONRenderer()
            if log_format == "json"
            else structlog.dev.Console<PERSON><PERSON>er(),
        ],
        wrapper_class=structlog.make_filtering_bound_logger(
            getattr(logging, log_level.upper())
        ),
        logger_factory=structlog.WriteLoggerFactory(),
        cache_logger_on_first_use=True,
    )

    # Standard library logging configuration
    logging_config: dict[str, Any] = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "json": {
                "()": "pythonjsonlogger.jsonlogger.JsonFormatter",
                "format": "%(asctime)s %(name)s %(levelname)s %(message)s %(pathname)s %(lineno)d",
            },
            "text": {
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S",
            },
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "level": log_level,
                "formatter": log_format,
                "stream": sys.stdout,
            },
            "file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": log_level,
                "formatter": log_format,
                "filename": "logs/app.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
                "encoding": "utf8",
            },
        },
        "loggers": {
            "": {  # Root logger
                "level": log_level,
                "handlers": ["console", "file"],
                "propagate": False,
            },
            "uvicorn": {
                "level": log_level,
                "handlers": ["console", "file"],
                "propagate": False,
            },
            "uvicorn.access": {
                "level": log_level,
                "handlers": ["console", "file"],
                "propagate": False,
            },
            "sqlalchemy": {
                "level": "WARNING",  # Reduce SQLAlchemy verbosity
                "handlers": ["console", "file"],
                "propagate": False,
            },
            "alembic": {
                "level": "INFO",
                "handlers": ["console", "file"],
                "propagate": False,
            },
        },
    }

    # Apply logging configuration
    logging.config.dictConfig(logging_config)

    # Set up request ID context
    structlog.contextvars.clear_contextvars()


def get_logger(name: str) -> structlog.BoundLogger:
    """
    Get a structured logger instance.

    Args:
        name: Logger name (usually __name__)

    Returns:
        structlog.BoundLogger: Configured logger instance
    """
    return structlog.get_logger(name)


class LoggerMixin:
    """
    Mixin class to add logging capabilities to any class.

    Provides a logger property that returns a structured logger
    with the class name as the logger name.
    """

    @property
    def logger(self) -> structlog.BoundLogger:
        """Get logger for this class."""
        return get_logger(self.__class__.__name__)


def log_function_call(func_name: str, **kwargs) -> None:
    """
    Log function call with parameters.

    Args:
        func_name: Name of the function being called
        **kwargs: Function parameters to log
    """
    logger = get_logger("function_call")
    logger.info(f"Calling {func_name}", **kwargs)


def log_performance(operation: str, duration: float, **kwargs) -> None:
    """
    Log performance metrics.

    Args:
        operation: Name of the operation
        duration: Duration in seconds
        **kwargs: Additional metrics to log
    """
    logger = get_logger("performance")
    logger.info(f"Performance: {operation}", duration_seconds=duration, **kwargs)


def log_with_correlation(
    logger_name: str, level: str, message: str, correlation_id: str, **kwargs
) -> None:
    """
    Log message with correlation ID.

    Args:
        logger_name: Name of the logger
        level: Log level (info, warning, error, etc.)
        message: Log message
        correlation_id: Request correlation ID
        **kwargs: Additional context
    """
    logger = get_logger(logger_name)
    with structlog.contextvars.bound_contextvars(
        correlation_id=correlation_id, **kwargs
    ):
        getattr(logger, level.lower())(message)


def log_user_action(
    user_id: str,
    action: str,
    resource_type: str | None = None,
    resource_id: str | None = None,
    **kwargs,
) -> None:
    """
    Log user action for audit trail.

    Args:
        user_id: User ID performing the action
        action: Action being performed
        resource_type: Type of resource being acted upon
        resource_id: ID of the resource
        **kwargs: Additional context
    """
    logger = get_logger("audit")
    logger.info(
        f"User action: {action}",
        user_id=user_id,
        action=action,
        resource_type=resource_type,
        resource_id=resource_id,
        **kwargs,
    )


def log_api_request(
    method: str,
    path: str,
    status_code: int,
    duration_ms: float,
    user_id: str = None,
    **kwargs,
) -> None:
    """
    Log API request for monitoring.

    Args:
        method: HTTP method
        path: Request path
        status_code: Response status code
        duration_ms: Request duration in milliseconds
        user_id: Optional user ID
        **kwargs: Additional context
    """
    logger = get_logger("api")
    logger.info(
        f"API {method} {path}",
        method=method,
        path=path,
        status_code=status_code,
        duration_ms=duration_ms,
        user_id=user_id,
        **kwargs,
    )


def log_websocket_event(
    event_type: str, connection_id: str, user_id: str = None, **kwargs
) -> None:
    """
    Log WebSocket event.

    Args:
        event_type: Type of WebSocket event
        connection_id: WebSocket connection ID
        user_id: Optional user ID
        **kwargs: Additional context
    """
    logger = get_logger("websocket")
    logger.info(
        f"WebSocket {event_type}",
        event_type=event_type,
        connection_id=connection_id,
        user_id=user_id,
        **kwargs,
    )


def log_model_operation(
    operation: str,
    model_id: str,
    instance_id: str = None,
    user_id: str = None,
    **kwargs,
) -> None:
    """
    Log model management operation.

    Args:
        operation: Operation being performed
        model_id: Model ID
        instance_id: Optional instance ID
        user_id: Optional user ID
        **kwargs: Additional context
    """
    logger = get_logger("model_management")
    logger.info(
        f"Model {operation}",
        operation=operation,
        model_id=model_id,
        instance_id=instance_id,
        user_id=user_id,
        **kwargs,
    )


def log_workflow_operation(
    operation: str,
    workflow_id: str,
    execution_id: str = None,
    user_id: str = None,
    **kwargs,
) -> None:
    """
    Log workflow operation.

    Args:
        operation: Operation being performed
        workflow_id: Workflow ID
        execution_id: Optional execution ID
        user_id: Optional user ID
        **kwargs: Additional context
    """
    logger = get_logger("workflow")
    logger.info(
        f"Workflow {operation}",
        operation=operation,
        workflow_id=workflow_id,
        execution_id=execution_id,
        user_id=user_id,
        **kwargs,
    )


class PerformanceTimer:
    """Context manager for timing operations."""

    def __init__(self, operation: str, logger_name: str = "performance", **context):
        self.operation = operation
        self.logger_name = logger_name
        self.context = context
        self.start_time = None

    def __enter__(self):
        import time

        self.start_time = time.time()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        import time

        duration = time.time() - self.start_time

        if exc_type is None:
            log_performance(self.operation, duration, **self.context)
        else:
            logger = get_logger(self.logger_name)
            logger.error(
                f"Operation failed: {self.operation}",
                duration_seconds=duration,
                error_type=exc_type.__name__ if exc_type else None,
                error_message=str(exc_val) if exc_val else None,
                **self.context,
            )
