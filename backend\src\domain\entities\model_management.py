"""
Model Management domain entities.

This module contains the core model management entities for the
Local Model Management Interface.
"""

import uuid
from datetime import UTC, datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class ModelType(str, Enum):
    """Model type enumeration."""
    
    LANGUAGE_MODEL = "language_model"
    EMBEDDING_MODEL = "embedding_model"
    VISION_MODEL = "vision_model"
    AUDIO_MODEL = "audio_model"
    MULTIMODAL_MODEL = "multimodal_model"
    CODE_MODEL = "code_model"
    CUSTOM = "custom"


class ModelStatus(str, Enum):
    """Model status enumeration."""
    
    AVAILABLE = "available"
    DOWNLOADING = "downloading"
    DOWNLOADED = "downloaded"
    LOADING = "loading"
    LOADED = "loaded"
    SERVING = "serving"
    ERROR = "error"
    STOPPED = "stopped"


class ModelProvider(str, Enum):
    """Model provider enumeration."""
    
    HUGGINGFACE = "huggingface"
    OLLAMA = "ollama"
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"
    CUSTOM = "custom"


class ModelInfo(BaseModel):
    """
    Model information domain entity.
    
    Represents metadata about an AI model including its capabilities,
    requirements, and current status.
    """
    
    id: str = Field(..., description="Unique model identifier")
    name: str = Field(..., min_length=1, max_length=255, description="Model name")
    display_name: str = Field(..., min_length=1, max_length=255, description="Human-readable model name")
    description: Optional[str] = Field(None, max_length=2000, description="Model description")
    
    # Model classification
    model_type: ModelType = Field(..., description="Model type")
    provider: ModelProvider = Field(..., description="Model provider")
    version: str = Field(..., description="Model version")
    
    # Model specifications
    size_bytes: Optional[int] = Field(None, ge=0, description="Model size in bytes")
    parameter_count: Optional[int] = Field(None, ge=0, description="Number of parameters")
    context_length: Optional[int] = Field(None, ge=0, description="Maximum context length")
    
    # Requirements
    min_ram_gb: Optional[float] = Field(None, ge=0, description="Minimum RAM required in GB")
    min_vram_gb: Optional[float] = Field(None, ge=0, description="Minimum VRAM required in GB")
    gpu_required: bool = Field(default=False, description="Whether GPU is required")
    
    # Capabilities
    capabilities: List[str] = Field(default_factory=list, description="Model capabilities")
    supported_formats: List[str] = Field(default_factory=list, description="Supported input/output formats")
    languages: List[str] = Field(default_factory=list, description="Supported languages")
    
    # Metadata
    tags: List[str] = Field(default_factory=list, description="Model tags")
    license: Optional[str] = Field(None, description="Model license")
    homepage_url: Optional[str] = Field(None, description="Model homepage URL")
    repository_url: Optional[str] = Field(None, description="Model repository URL")
    paper_url: Optional[str] = Field(None, description="Research paper URL")
    
    # Provider-specific metadata
    provider_metadata: Dict[str, Any] = Field(default_factory=dict, description="Provider-specific metadata")
    
    # Timestamps
    created_at: datetime = Field(default_factory=lambda: datetime.now(UTC), description="Creation timestamp")
    updated_at: datetime = Field(default_factory=lambda: datetime.now(UTC), description="Last update timestamp")
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
        }

    def get_size_display(self) -> str:
        """Get human-readable size display."""
        if not self.size_bytes:
            return "Unknown"
        
        size = self.size_bytes
        units = ['B', 'KB', 'MB', 'GB', 'TB']
        unit_index = 0
        
        while size >= 1024 and unit_index < len(units) - 1:
            size /= 1024
            unit_index += 1
        
        return f"{size:.1f} {units[unit_index]}"

    def get_parameter_display(self) -> str:
        """Get human-readable parameter count display."""
        if not self.parameter_count:
            return "Unknown"
        
        count = self.parameter_count
        if count >= 1_000_000_000:
            return f"{count / 1_000_000_000:.1f}B"
        elif count >= 1_000_000:
            return f"{count / 1_000_000:.1f}M"
        elif count >= 1_000:
            return f"{count / 1_000:.1f}K"
        else:
            return str(count)

    def model_dump(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "id": self.id,
            "name": self.name,
            "display_name": self.display_name,
            "description": self.description,
            "model_type": self.model_type.value,
            "provider": self.provider.value,
            "version": self.version,
            "size_bytes": self.size_bytes,
            "parameter_count": self.parameter_count,
            "context_length": self.context_length,
            "min_ram_gb": self.min_ram_gb,
            "min_vram_gb": self.min_vram_gb,
            "gpu_required": self.gpu_required,
            "capabilities": self.capabilities,
            "supported_formats": self.supported_formats,
            "languages": self.languages,
            "tags": self.tags,
            "license": self.license,
            "homepage_url": self.homepage_url,
            "repository_url": self.repository_url,
            "paper_url": self.paper_url,
            "provider_metadata": self.provider_metadata,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
        }


class ModelInstance(BaseModel):
    """
    Model instance domain entity.
    
    Represents a specific instance of a model that can be downloaded,
    loaded, and served locally.
    """
    
    id: uuid.UUID = Field(default_factory=uuid.uuid4, description="Unique instance identifier")
    model_id: str = Field(..., description="Reference to ModelInfo")
    
    # Instance status
    status: ModelStatus = Field(default=ModelStatus.AVAILABLE, description="Current status")
    
    # Download information
    download_url: Optional[str] = Field(None, description="Download URL")
    local_path: Optional[str] = Field(None, description="Local file path")
    download_progress: float = Field(default=0.0, ge=0.0, le=100.0, description="Download progress percentage")
    download_speed: Optional[float] = Field(None, ge=0, description="Download speed in bytes/second")
    
    # Serving information
    serving_port: Optional[int] = Field(None, ge=1, le=65535, description="Serving port")
    serving_url: Optional[str] = Field(None, description="Serving URL")
    api_endpoint: Optional[str] = Field(None, description="API endpoint")
    
    # Performance metrics
    load_time_seconds: Optional[float] = Field(None, ge=0, description="Model load time in seconds")
    memory_usage_mb: Optional[float] = Field(None, ge=0, description="Memory usage in MB")
    gpu_memory_usage_mb: Optional[float] = Field(None, ge=0, description="GPU memory usage in MB")
    
    # Configuration
    configuration: Dict[str, Any] = Field(default_factory=dict, description="Model configuration")
    
    # Error information
    error_message: Optional[str] = Field(None, description="Error message if status is ERROR")
    error_details: Dict[str, Any] = Field(default_factory=dict, description="Detailed error information")
    
    # Ownership
    created_by: uuid.UUID = Field(..., description="Creator user ID")
    
    # Timestamps
    created_at: datetime = Field(default_factory=lambda: datetime.now(UTC), description="Creation timestamp")
    updated_at: datetime = Field(default_factory=lambda: datetime.now(UTC), description="Last update timestamp")
    last_used_at: Optional[datetime] = Field(None, description="Last usage timestamp")
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            uuid.UUID: lambda v: str(v),
        }

    def update_status(self, status: ModelStatus, error_message: Optional[str] = None) -> None:
        """Update instance status."""
        self.status = status
        self.updated_at = datetime.now(UTC)
        
        if status == ModelStatus.ERROR and error_message:
            self.error_message = error_message
        elif status != ModelStatus.ERROR:
            self.error_message = None
            self.error_details = {}

    def update_download_progress(self, progress: float, speed: Optional[float] = None) -> None:
        """Update download progress."""
        self.download_progress = max(0.0, min(100.0, progress))
        if speed is not None:
            self.download_speed = speed
        self.updated_at = datetime.now(UTC)

    def update_serving_info(self, port: int, url: str, endpoint: str) -> None:
        """Update serving information."""
        self.serving_port = port
        self.serving_url = url
        self.api_endpoint = endpoint
        self.updated_at = datetime.now(UTC)

    def update_performance_metrics(
        self, 
        load_time: Optional[float] = None,
        memory_usage: Optional[float] = None,
        gpu_memory_usage: Optional[float] = None
    ) -> None:
        """Update performance metrics."""
        if load_time is not None:
            self.load_time_seconds = load_time
        if memory_usage is not None:
            self.memory_usage_mb = memory_usage
        if gpu_memory_usage is not None:
            self.gpu_memory_usage_mb = gpu_memory_usage
        self.updated_at = datetime.now(UTC)

    def mark_used(self) -> None:
        """Mark instance as recently used."""
        self.last_used_at = datetime.now(UTC)
        self.updated_at = datetime.now(UTC)

    def is_available(self) -> bool:
        """Check if instance is available for use."""
        return self.status in [ModelStatus.LOADED, ModelStatus.SERVING]

    def is_downloading(self) -> bool:
        """Check if instance is currently downloading."""
        return self.status == ModelStatus.DOWNLOADING

    def has_error(self) -> bool:
        """Check if instance has an error."""
        return self.status == ModelStatus.ERROR

    def model_dump(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "id": str(self.id),
            "model_id": self.model_id,
            "status": self.status.value,
            "download_url": self.download_url,
            "local_path": self.local_path,
            "download_progress": self.download_progress,
            "download_speed": self.download_speed,
            "serving_port": self.serving_port,
            "serving_url": self.serving_url,
            "api_endpoint": self.api_endpoint,
            "load_time_seconds": self.load_time_seconds,
            "memory_usage_mb": self.memory_usage_mb,
            "gpu_memory_usage_mb": self.gpu_memory_usage_mb,
            "configuration": self.configuration,
            "error_message": self.error_message,
            "error_details": self.error_details,
            "created_by": str(self.created_by),
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "last_used_at": self.last_used_at.isoformat() if self.last_used_at else None,
        }


class ModelDownloadTask(BaseModel):
    """
    Model download task domain entity.
    
    Represents a background task for downloading a model.
    """
    
    id: uuid.UUID = Field(default_factory=uuid.uuid4, description="Unique task identifier")
    instance_id: uuid.UUID = Field(..., description="Model instance ID")
    model_id: str = Field(..., description="Model ID")
    
    # Task status
    status: str = Field(default="pending", description="Task status")
    progress: float = Field(default=0.0, ge=0.0, le=100.0, description="Progress percentage")
    
    # Download information
    download_url: str = Field(..., description="Download URL")
    destination_path: str = Field(..., description="Destination file path")
    total_size: Optional[int] = Field(None, ge=0, description="Total download size in bytes")
    downloaded_size: int = Field(default=0, ge=0, description="Downloaded size in bytes")
    download_speed: Optional[float] = Field(None, ge=0, description="Download speed in bytes/second")
    
    # Timing
    started_at: Optional[datetime] = Field(None, description="Task start timestamp")
    completed_at: Optional[datetime] = Field(None, description="Task completion timestamp")
    estimated_completion: Optional[datetime] = Field(None, description="Estimated completion timestamp")
    
    # Error handling
    error_message: Optional[str] = Field(None, description="Error message if failed")
    retry_count: int = Field(default=0, ge=0, description="Number of retry attempts")
    max_retries: int = Field(default=3, ge=0, description="Maximum retry attempts")
    
    # Ownership
    created_by: uuid.UUID = Field(..., description="Creator user ID")
    
    # Timestamps
    created_at: datetime = Field(default_factory=lambda: datetime.now(UTC), description="Creation timestamp")
    updated_at: datetime = Field(default_factory=lambda: datetime.now(UTC), description="Last update timestamp")
    
    class Config:
        """Pydantic configuration."""
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            uuid.UUID: lambda v: str(v),
        }

    def start_download(self) -> None:
        """Mark download as started."""
        self.status = "running"
        self.started_at = datetime.now(UTC)
        self.updated_at = datetime.now(UTC)

    def update_progress(self, downloaded: int, speed: Optional[float] = None) -> None:
        """Update download progress."""
        self.downloaded_size = downloaded
        if self.total_size and self.total_size > 0:
            self.progress = (downloaded / self.total_size) * 100
        
        if speed is not None:
            self.download_speed = speed
            
            # Estimate completion time
            if speed > 0 and self.total_size:
                remaining_bytes = self.total_size - downloaded
                remaining_seconds = remaining_bytes / speed
                self.estimated_completion = datetime.now(UTC).replace(
                    microsecond=0
                ) + datetime.timedelta(seconds=remaining_seconds)
        
        self.updated_at = datetime.now(UTC)

    def complete_download(self) -> None:
        """Mark download as completed."""
        self.status = "completed"
        self.progress = 100.0
        self.completed_at = datetime.now(UTC)
        self.updated_at = datetime.now(UTC)

    def fail_download(self, error_message: str) -> None:
        """Mark download as failed."""
        self.status = "failed"
        self.error_message = error_message
        self.updated_at = datetime.now(UTC)

    def can_retry(self) -> bool:
        """Check if download can be retried."""
        return self.retry_count < self.max_retries

    def retry_download(self) -> None:
        """Retry the download."""
        if self.can_retry():
            self.retry_count += 1
            self.status = "pending"
            self.error_message = None
            self.downloaded_size = 0
            self.progress = 0.0
            self.download_speed = None
            self.estimated_completion = None
            self.updated_at = datetime.now(UTC)

    def model_dump(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "id": str(self.id),
            "instance_id": str(self.instance_id),
            "model_id": self.model_id,
            "status": self.status,
            "progress": self.progress,
            "download_url": self.download_url,
            "destination_path": self.destination_path,
            "total_size": self.total_size,
            "downloaded_size": self.downloaded_size,
            "download_speed": self.download_speed,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "estimated_completion": self.estimated_completion.isoformat() if self.estimated_completion else None,
            "error_message": self.error_message,
            "retry_count": self.retry_count,
            "max_retries": self.max_retries,
            "created_by": str(self.created_by),
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
        }
