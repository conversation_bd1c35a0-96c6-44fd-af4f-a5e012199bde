'use client';

import { useAnime, UseAnimeConfig } from '@/shared/lib/hooks/useAnime';
import { ANIMATION_PRESETS } from '@/shared/lib/animations';
import { cn } from '@/shared/lib/utils';
import React, { useMemo, useRef, useEffect } from 'react';

// Enhanced animation preset types for better type safety
export type AgentAnimationPreset = 
  | 'agentThinking'
  | 'agentProcessing'
  | 'agentSuccess'
  | 'agentError'
  | 'agentWarning'
  | 'agentActivate'
  | 'agentDeactivate'
  | 'agentStatusChange';

export type FlowBuilderAnimationPreset =
  | 'nodeEnter'
  | 'nodeExit'
  | 'nodeHover'
  | 'nodeUnhover'
  | 'nodeSelect'
  | 'nodeDeselect'
  | 'connectionDraw'
  | 'connectionPulse';

export type WorkflowAnimationPreset =
  | 'workflowStart'
  | 'workflowComplete'
  | 'workflowError';

export type StatusAnimationPreset =
  | 'statusIndicatorPulse';

export type ModelAnimationPreset =
  | 'modelLoading';

export type CardAnimationPreset =
  | 'cardHoverAgent';

export type EnhancedAnimationPreset = 
  | AgentAnimationPreset
  | FlowBuilderAnimationPreset
  | WorkflowAnimationPreset
  | StatusAnimationPreset
  | ModelAnimationPreset
  | CardAnimationPreset;

interface EnhancedAnimationPresetsProps extends React.HTMLAttributes<HTMLDivElement> {
  animationPreset: EnhancedAnimationPreset;
  animationConfig?: Partial<UseAnimeConfig>;
  animationDeps?: React.DependencyList;
  trigger?: 'mount' | 'hover' | 'click' | 'focus' | 'manual';
  loop?: boolean;
  autoplay?: boolean;
  onAnimationStart?: () => void;
  onAnimationComplete?: () => void;
  disabled?: boolean;
}

/**
 * Enhanced Animation Presets Component
 * 
 * Provides a comprehensive interface for all AI agent and workflow-specific animations.
 * Built on top of the existing AnimatedBox component with enhanced functionality.
 * 
 * Features:
 * - Type-safe animation presets for different contexts
 * - Multiple trigger modes (mount, hover, click, focus, manual)
 * - Performance optimized with 60fps target
 * - Accessibility support with reduced motion detection
 * - Comprehensive callback system
 * 
 * @example
 * ```tsx
 * // Agent status animation
 * <EnhancedAnimationPresets
 *   animationPreset="agentThinking"
 *   trigger="mount"
 *   loop={true}
 * >
 *   <AgentStatusIndicator status="thinking" />
 * </EnhancedAnimationPresets>
 * 
 * // Flow builder node animation
 * <EnhancedAnimationPresets
 *   animationPreset="nodeEnter"
 *   trigger="mount"
 *   onAnimationComplete={() => console.log('Node entered')}
 * >
 *   <FlowNode type="agent" />
 * </EnhancedAnimationPresets>
 * ```
 */
const EnhancedAnimationPresets: React.FC<EnhancedAnimationPresetsProps> = ({
  children,
  className,
  animationPreset,
  animationConfig = {},
  animationDeps = [],
  trigger = 'mount',
  loop = false,
  autoplay = true,
  onAnimationStart,
  onAnimationComplete,
  disabled = false,
  ...props
}) => {
  const elementRef = useRef<HTMLDivElement>(null);
  const animationInstanceRef = useRef<any>(null);

  // Memoize the final animation configuration
  const finalAnimationConfig = useMemo(() => {
    const presetConfig = ANIMATION_PRESETS[animationPreset];
    
    if (!presetConfig) {
      console.warn(`Animation preset "${animationPreset}" not found`);
      return { opacity: [0, 1], duration: 300 };
    }

    return {
      ...presetConfig,
      ...animationConfig,
      loop: loop || presetConfig.loop || false,
      autoplay: autoplay && trigger === 'mount',
      begin: onAnimationStart,
      complete: onAnimationComplete,
    };
  }, [animationPreset, animationConfig, loop, autoplay, trigger, onAnimationStart, onAnimationComplete]);

  // Use the existing useAnime hook for mount trigger
  const mountAnimationRef = useAnime<HTMLDivElement>(
    trigger === 'mount' && !disabled ? finalAnimationConfig : {},
    [...animationDeps, disabled]
  );

  // Manual animation control
  const playAnimation = React.useCallback(() => {
    if (disabled || !elementRef.current) return;

    // Stop any existing animation
    if (animationInstanceRef.current) {
      animationInstanceRef.current.pause();
    }

    // Import anime dynamically to avoid SSR issues
    import('animejs').then(({ default: anime }) => {
      animationInstanceRef.current = anime({
        targets: elementRef.current,
        ...finalAnimationConfig,
      });
    });
  }, [finalAnimationConfig, disabled]);

  // Handle different trigger types
  const handleInteraction = React.useCallback((event: React.SyntheticEvent) => {
    if (trigger === 'manual' || disabled) return;
    
    const eventType = event.type;
    if (
      (trigger === 'hover' && (eventType === 'mouseenter' || eventType === 'mouseleave')) ||
      (trigger === 'click' && eventType === 'click') ||
      (trigger === 'focus' && (eventType === 'focus' || eventType === 'blur'))
    ) {
      playAnimation();
    }
  }, [trigger, playAnimation, disabled]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (animationInstanceRef.current) {
        animationInstanceRef.current.pause();
      }
    };
  }, []);

  // Determine which ref to use based on trigger type
  const ref = trigger === 'mount' ? mountAnimationRef : elementRef;

  return (
    <div
      ref={ref}
      className={cn(
        'transition-opacity',
        trigger === 'mount' && 'opacity-0', // Start hidden for mount animations
        className
      )}
      onMouseEnter={trigger === 'hover' ? handleInteraction : undefined}
      onMouseLeave={trigger === 'hover' ? handleInteraction : undefined}
      onClick={trigger === 'click' ? handleInteraction : undefined}
      onFocus={trigger === 'focus' ? handleInteraction : undefined}
      onBlur={trigger === 'focus' ? handleInteraction : undefined}
      {...props}
    >
      {children}
    </div>
  );
};

EnhancedAnimationPresets.displayName = 'EnhancedAnimationPresets';

export { EnhancedAnimationPresets };

// Export animation preset groups for easier consumption
export const AGENT_ANIMATION_PRESETS: AgentAnimationPreset[] = [
  'agentThinking',
  'agentProcessing',
  'agentSuccess',
  'agentError',
  'agentWarning',
  'agentActivate',
  'agentDeactivate',
  'agentStatusChange',
];

export const FLOW_BUILDER_ANIMATION_PRESETS: FlowBuilderAnimationPreset[] = [
  'nodeEnter',
  'nodeExit',
  'nodeHover',
  'nodeUnhover',
  'nodeSelect',
  'nodeDeselect',
  'connectionDraw',
  'connectionPulse',
];

export const WORKFLOW_ANIMATION_PRESETS: WorkflowAnimationPreset[] = [
  'workflowStart',
  'workflowComplete',
  'workflowError',
];

// Utility function to get animation preset by category
export function getAnimationPresetsByCategory(category: 'agent' | 'flow' | 'workflow' | 'status' | 'model' | 'card') {
  switch (category) {
    case 'agent':
      return AGENT_ANIMATION_PRESETS;
    case 'flow':
      return FLOW_BUILDER_ANIMATION_PRESETS;
    case 'workflow':
      return WORKFLOW_ANIMATION_PRESETS;
    case 'status':
      return ['statusIndicatorPulse'] as StatusAnimationPreset[];
    case 'model':
      return ['modelLoading'] as ModelAnimationPreset[];
    case 'card':
      return ['cardHoverAgent'] as CardAnimationPreset[];
    default:
      return [];
  }
}
