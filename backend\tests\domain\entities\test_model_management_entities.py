"""
Tests for model management domain entities.

This module contains comprehensive tests for ModelInstance and ModelDownloadTask entities.
"""

import uuid
from datetime import datetime, timedelta

import pytest

from src.domain.entities.model_management import (
    ModelDownloadTask,
    ModelInstance,
    ModelProvider,
    ModelStatus,
    ModelType,
)


class TestModelInstance:
    """Test cases for ModelInstance entity."""

    def test_model_instance_creation(self):
        """Test creating a valid model instance."""
        instance_id = uuid.uuid4()
        model_id = "microsoft/DialoGPT-medium"
        user_id = uuid.uuid4()
        
        instance = ModelInstance(
            id=instance_id,
            model_id=model_id,
            status=ModelStatus.AVAILABLE,
            download_progress=0.0,
            configuration={"max_length": 1000},
            error_details={},
            created_by=user_id,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )
        
        assert instance.id == instance_id
        assert instance.model_id == model_id
        assert instance.status == ModelStatus.AVAILABLE
        assert instance.download_progress == 0.0
        assert instance.configuration == {"max_length": 1000}
        assert instance.created_by == user_id

    def test_model_instance_status_transitions(self):
        """Test valid status transitions."""
        instance = self._create_test_instance()
        
        # Test valid transitions
        valid_transitions = [
            (ModelStatus.AVAILABLE, ModelStatus.DOWNLOADING),
            (ModelStatus.DOWNLOADING, ModelStatus.DOWNLOADED),
            (ModelStatus.DOWNLOADED, ModelStatus.LOADING),
            (ModelStatus.LOADING, ModelStatus.LOADED),
            (ModelStatus.LOADED, ModelStatus.SERVING),
            (ModelStatus.SERVING, ModelStatus.STOPPED),
            (ModelStatus.DOWNLOADING, ModelStatus.ERROR),
            (ModelStatus.LOADING, ModelStatus.ERROR),
        ]
        
        for from_status, to_status in valid_transitions:
            instance.status = from_status
            instance.status = to_status  # Should not raise exception
            assert instance.status == to_status

    def test_model_instance_download_progress_validation(self):
        """Test download progress validation."""
        instance = self._create_test_instance()
        
        # Valid progress values
        valid_progress = [0.0, 25.5, 50.0, 75.25, 100.0]
        for progress in valid_progress:
            instance.download_progress = progress
            assert instance.download_progress == progress
        
        # Invalid progress values should be handled by the application layer
        # The entity itself doesn't enforce validation

    def test_model_instance_serving_configuration(self):
        """Test serving configuration updates."""
        instance = self._create_test_instance()
        
        # Update serving configuration
        instance.serving_port = 8000
        instance.serving_url = "http://localhost:8000"
        instance.api_endpoint = "http://localhost:8000/v1/completions"
        instance.status = ModelStatus.SERVING
        
        assert instance.serving_port == 8000
        assert instance.serving_url == "http://localhost:8000"
        assert instance.api_endpoint == "http://localhost:8000/v1/completions"
        assert instance.status == ModelStatus.SERVING

    def test_model_instance_performance_metrics(self):
        """Test performance metrics tracking."""
        instance = self._create_test_instance()
        
        # Update performance metrics
        instance.load_time_seconds = 15.5
        instance.memory_usage_mb = 2048.0
        instance.gpu_memory_usage_mb = 1024.0
        
        assert instance.load_time_seconds == 15.5
        assert instance.memory_usage_mb == 2048.0
        assert instance.gpu_memory_usage_mb == 1024.0

    def test_model_instance_error_handling(self):
        """Test error information storage."""
        instance = self._create_test_instance()
        
        # Set error information
        instance.status = ModelStatus.ERROR
        instance.error_message = "Failed to load model"
        instance.error_details = {
            "error_type": "ModelLoadError",
            "stack_trace": "...",
            "timestamp": datetime.utcnow().isoformat(),
        }
        
        assert instance.status == ModelStatus.ERROR
        assert instance.error_message == "Failed to load model"
        assert "error_type" in instance.error_details
        assert instance.error_details["error_type"] == "ModelLoadError"

    def test_model_instance_last_used_tracking(self):
        """Test last used timestamp tracking."""
        instance = self._create_test_instance()
        
        # Initially no last used time
        assert instance.last_used_at is None
        
        # Update last used time
        now = datetime.utcnow()
        instance.last_used_at = now
        
        assert instance.last_used_at == now

    def _create_test_instance(self) -> ModelInstance:
        """Create a test model instance."""
        return ModelInstance(
            id=uuid.uuid4(),
            model_id="test/model",
            status=ModelStatus.AVAILABLE,
            download_progress=0.0,
            configuration={},
            error_details={},
            created_by=uuid.uuid4(),
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )


class TestModelDownloadTask:
    """Test cases for ModelDownloadTask entity."""

    def test_download_task_creation(self):
        """Test creating a valid download task."""
        task_id = uuid.uuid4()
        instance_id = uuid.uuid4()
        user_id = uuid.uuid4()
        
        task = ModelDownloadTask(
            id=task_id,
            instance_id=instance_id,
            model_id="microsoft/DialoGPT-medium",
            status="pending",
            progress=0.0,
            download_url="https://huggingface.co/microsoft/DialoGPT-medium/resolve/main/pytorch_model.bin",
            destination_path="./models/microsoft/DialoGPT-medium",
            downloaded_size=0,
            retry_count=0,
            max_retries=3,
            created_by=user_id,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )
        
        assert task.id == task_id
        assert task.instance_id == instance_id
        assert task.model_id == "microsoft/DialoGPT-medium"
        assert task.status == "pending"
        assert task.progress == 0.0
        assert task.downloaded_size == 0
        assert task.retry_count == 0
        assert task.max_retries == 3

    def test_download_task_progress_updates(self):
        """Test download progress updates."""
        task = self._create_test_task()
        
        # Simulate download progress
        progress_updates = [
            (10.0, 100_000_000, 5_000_000),  # 10%, 100MB, 5MB/s
            (25.0, 250_000_000, 8_000_000),  # 25%, 250MB, 8MB/s
            (50.0, 500_000_000, 10_000_000), # 50%, 500MB, 10MB/s
            (75.0, 750_000_000, 12_000_000), # 75%, 750MB, 12MB/s
            (100.0, 1_000_000_000, 15_000_000), # 100%, 1GB, 15MB/s
        ]
        
        for progress, downloaded, speed in progress_updates:
            task.progress = progress
            task.downloaded_size = downloaded
            task.download_speed = speed
            task.updated_at = datetime.utcnow()
            
            assert task.progress == progress
            assert task.downloaded_size == downloaded
            assert task.download_speed == speed

    def test_download_task_completion(self):
        """Test download task completion."""
        task = self._create_test_task()
        
        # Complete the download
        completion_time = datetime.utcnow()
        task.status = "completed"
        task.progress = 100.0
        task.completed_at = completion_time
        task.updated_at = completion_time
        
        assert task.status == "completed"
        assert task.progress == 100.0
        assert task.completed_at == completion_time

    def test_download_task_failure_handling(self):
        """Test download task failure scenarios."""
        task = self._create_test_task()
        
        # Simulate download failure
        task.status = "failed"
        task.error_message = "Network connection timeout"
        task.retry_count += 1
        task.updated_at = datetime.utcnow()
        
        assert task.status == "failed"
        assert task.error_message == "Network connection timeout"
        assert task.retry_count == 1

    def test_download_task_retry_logic(self):
        """Test download task retry logic."""
        task = self._create_test_task()
        
        # Simulate multiple retries
        for retry in range(1, task.max_retries + 1):
            task.status = "failed"
            task.retry_count = retry
            task.error_message = f"Retry {retry} failed"
            
            assert task.retry_count == retry
            assert task.retry_count <= task.max_retries

    def test_download_task_time_estimation(self):
        """Test download time estimation."""
        task = self._create_test_task()
        
        # Set up for time estimation
        task.total_size = 1_000_000_000  # 1GB
        task.downloaded_size = 250_000_000  # 250MB
        task.download_speed = 10_000_000  # 10MB/s
        task.started_at = datetime.utcnow() - timedelta(seconds=25)  # Started 25 seconds ago
        
        # Calculate estimated completion (should be in ~75 seconds from now)
        remaining_bytes = task.total_size - task.downloaded_size
        estimated_seconds = remaining_bytes / task.download_speed
        estimated_completion = datetime.utcnow() + timedelta(seconds=estimated_seconds)
        
        task.estimated_completion = estimated_completion
        
        assert task.total_size == 1_000_000_000
        assert task.downloaded_size == 250_000_000
        assert task.download_speed == 10_000_000
        assert task.estimated_completion is not None

    def test_download_task_cancellation(self):
        """Test download task cancellation."""
        task = self._create_test_task()
        
        # Start the download
        task.status = "running"
        task.started_at = datetime.utcnow()
        
        # Cancel the download
        task.status = "cancelled"
        task.completed_at = datetime.utcnow()
        task.updated_at = datetime.utcnow()
        
        assert task.status == "cancelled"
        assert task.completed_at is not None

    def _create_test_task(self) -> ModelDownloadTask:
        """Create a test download task."""
        return ModelDownloadTask(
            id=uuid.uuid4(),
            instance_id=uuid.uuid4(),
            model_id="test/model",
            status="pending",
            progress=0.0,
            download_url="https://example.com/model.bin",
            destination_path="./models/test/model",
            downloaded_size=0,
            retry_count=0,
            max_retries=3,
            created_by=uuid.uuid4(),
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )


class TestModelManagementEntityValidation:
    """Test validation logic for model management entities."""

    def test_model_instance_required_fields(self):
        """Test that required fields are properly set."""
        # This would typically be enforced by the application layer
        # The entity itself is a data container
        
        instance = ModelInstance(
            id=uuid.uuid4(),
            model_id="test/model",
            status=ModelStatus.AVAILABLE,
            download_progress=0.0,
            configuration={},
            error_details={},
            created_by=uuid.uuid4(),
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )
        
        # Verify required fields are set
        assert instance.id is not None
        assert instance.model_id is not None
        assert instance.status is not None
        assert instance.created_by is not None
        assert instance.created_at is not None
        assert instance.updated_at is not None

    def test_download_task_required_fields(self):
        """Test that required fields are properly set."""
        task = ModelDownloadTask(
            id=uuid.uuid4(),
            instance_id=uuid.uuid4(),
            model_id="test/model",
            status="pending",
            progress=0.0,
            download_url="https://example.com/model.bin",
            destination_path="./models/test/model",
            downloaded_size=0,
            retry_count=0,
            max_retries=3,
            created_by=uuid.uuid4(),
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )
        
        # Verify required fields are set
        assert task.id is not None
        assert task.instance_id is not None
        assert task.model_id is not None
        assert task.status is not None
        assert task.download_url is not None
        assert task.destination_path is not None
        assert task.created_by is not None
