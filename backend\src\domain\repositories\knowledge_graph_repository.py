"""
Knowledge Graph repository interface.

This module defines the repository interface for knowledge graph persistence operations.
"""

import uuid
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Set

from src.domain.entities.knowledge_graph import (
    EntityType,
    KnowledgeEntity,
    KnowledgeGraph,
    KnowledgeRelationship,
    RelationType,
)


class KnowledgeEntityRepository(ABC):
    """Abstract repository interface for knowledge entity operations."""

    @abstractmethod
    async def create(self, entity: KnowledgeEntity) -> KnowledgeEntity:
        """
        Create a new knowledge entity.

        Args:
            entity: The entity to create

        Returns:
            The created entity with assigned ID

        Raises:
            RepositoryError: If creation fails
        """
        pass

    @abstractmethod
    async def get_by_id(self, entity_id: uuid.UUID) -> Optional[KnowledgeEntity]:
        """
        Get a knowledge entity by ID.

        Args:
            entity_id: The entity ID

        Returns:
            The entity if found, None otherwise

        Raises:
            RepositoryError: If retrieval fails
        """
        pass

    @abstractmethod
    async def get_by_name(self, name: str, user_id: uuid.UUID) -> Optional[KnowledgeEntity]:
        """
        Get a knowledge entity by name for a specific user.

        Args:
            name: The entity name
            user_id: The user ID

        Returns:
            The entity if found, None otherwise

        Raises:
            RepositoryError: If retrieval fails
        """
        pass

    @abstractmethod
    async def get_by_user(
        self,
        user_id: uuid.UUID,
        entity_type: Optional[EntityType] = None,
        limit: int = 100,
        offset: int = 0,
    ) -> List[KnowledgeEntity]:
        """
        Get knowledge entities by user ID with optional filtering.

        Args:
            user_id: The user ID
            entity_type: Optional entity type filter
            limit: Maximum number of entities to return
            offset: Number of entities to skip

        Returns:
            List of entities

        Raises:
            RepositoryError: If retrieval fails
        """
        pass

    @abstractmethod
    async def search(
        self,
        query: str,
        user_id: Optional[uuid.UUID] = None,
        entity_type: Optional[EntityType] = None,
        include_public: bool = True,
        limit: int = 100,
        offset: int = 0,
    ) -> List[KnowledgeEntity]:
        """
        Search knowledge entities by name, description, or properties.

        Args:
            query: Search query string
            user_id: Optional user ID filter
            entity_type: Optional entity type filter
            include_public: Whether to include public entities
            limit: Maximum number of entities to return
            offset: Number of entities to skip

        Returns:
            List of matching entities

        Raises:
            RepositoryError: If search fails
        """
        pass

    @abstractmethod
    async def get_by_tags(
        self,
        tags: List[str],
        user_id: Optional[uuid.UUID] = None,
        limit: int = 100,
        offset: int = 0,
    ) -> List[KnowledgeEntity]:
        """
        Get knowledge entities by tags.

        Args:
            tags: List of tags to search for
            user_id: Optional user ID filter
            limit: Maximum number of entities to return
            offset: Number of entities to skip

        Returns:
            List of entities with matching tags

        Raises:
            RepositoryError: If retrieval fails
        """
        pass

    @abstractmethod
    async def update(self, entity: KnowledgeEntity) -> KnowledgeEntity:
        """
        Update an existing knowledge entity.

        Args:
            entity: The entity to update

        Returns:
            The updated entity

        Raises:
            RepositoryError: If update fails
            NotFoundError: If entity not found
        """
        pass

    @abstractmethod
    async def delete(self, entity_id: uuid.UUID) -> bool:
        """
        Delete a knowledge entity.

        Args:
            entity_id: The entity ID to delete

        Returns:
            True if deleted, False if not found

        Raises:
            RepositoryError: If deletion fails
        """
        pass


class KnowledgeRelationshipRepository(ABC):
    """Abstract repository interface for knowledge relationship operations."""

    @abstractmethod
    async def create(self, relationship: KnowledgeRelationship) -> KnowledgeRelationship:
        """
        Create a new knowledge relationship.

        Args:
            relationship: The relationship to create

        Returns:
            The created relationship with assigned ID

        Raises:
            RepositoryError: If creation fails
        """
        pass

    @abstractmethod
    async def get_by_id(self, relationship_id: uuid.UUID) -> Optional[KnowledgeRelationship]:
        """
        Get a knowledge relationship by ID.

        Args:
            relationship_id: The relationship ID

        Returns:
            The relationship if found, None otherwise

        Raises:
            RepositoryError: If retrieval fails
        """
        pass

    @abstractmethod
    async def get_by_entities(
        self,
        source_entity_id: uuid.UUID,
        target_entity_id: Optional[uuid.UUID] = None,
        relation_type: Optional[RelationType] = None,
    ) -> List[KnowledgeRelationship]:
        """
        Get relationships by entity IDs.

        Args:
            source_entity_id: The source entity ID
            target_entity_id: Optional target entity ID
            relation_type: Optional relationship type filter

        Returns:
            List of relationships

        Raises:
            RepositoryError: If retrieval fails
        """
        pass

    @abstractmethod
    async def get_entity_relationships(
        self,
        entity_id: uuid.UUID,
        relation_type: Optional[RelationType] = None,
        direction: Optional[str] = None,  # 'incoming', 'outgoing', or None for both
    ) -> List[KnowledgeRelationship]:
        """
        Get all relationships for a specific entity.

        Args:
            entity_id: The entity ID
            relation_type: Optional relationship type filter
            direction: Optional direction filter

        Returns:
            List of relationships

        Raises:
            RepositoryError: If retrieval fails
        """
        pass

    @abstractmethod
    async def update(self, relationship: KnowledgeRelationship) -> KnowledgeRelationship:
        """
        Update an existing knowledge relationship.

        Args:
            relationship: The relationship to update

        Returns:
            The updated relationship

        Raises:
            RepositoryError: If update fails
            NotFoundError: If relationship not found
        """
        pass

    @abstractmethod
    async def delete(self, relationship_id: uuid.UUID) -> bool:
        """
        Delete a knowledge relationship.

        Args:
            relationship_id: The relationship ID to delete

        Returns:
            True if deleted, False if not found

        Raises:
            RepositoryError: If deletion fails
        """
        pass


class KnowledgeGraphRepository(ABC):
    """Abstract repository interface for knowledge graph operations."""

    @abstractmethod
    async def create(self, graph: KnowledgeGraph) -> KnowledgeGraph:
        """
        Create a new knowledge graph.

        Args:
            graph: The graph to create

        Returns:
            The created graph with assigned ID

        Raises:
            RepositoryError: If creation fails
        """
        pass

    @abstractmethod
    async def get_by_id(self, graph_id: uuid.UUID) -> Optional[KnowledgeGraph]:
        """
        Get a knowledge graph by ID.

        Args:
            graph_id: The graph ID

        Returns:
            The graph if found, None otherwise

        Raises:
            RepositoryError: If retrieval fails
        """
        pass

    @abstractmethod
    async def get_by_user(
        self,
        user_id: uuid.UUID,
        limit: int = 100,
        offset: int = 0,
    ) -> List[KnowledgeGraph]:
        """
        Get knowledge graphs by user ID.

        Args:
            user_id: The user ID
            limit: Maximum number of graphs to return
            offset: Number of graphs to skip

        Returns:
            List of graphs

        Raises:
            RepositoryError: If retrieval fails
        """
        pass

    @abstractmethod
    async def get_subgraph(
        self,
        entity_ids: Set[uuid.UUID],
        user_id: uuid.UUID,
        max_depth: int = 2,
    ) -> KnowledgeGraph:
        """
        Get a subgraph containing specified entities and their neighbors.

        Args:
            entity_ids: Set of entity IDs to include
            user_id: The user ID for access control
            max_depth: Maximum depth for neighbor traversal

        Returns:
            The subgraph

        Raises:
            RepositoryError: If retrieval fails
        """
        pass

    @abstractmethod
    async def search_entities_in_context(
        self,
        query: str,
        context_entity_ids: Set[uuid.UUID],
        user_id: uuid.UUID,
        limit: int = 100,
    ) -> List[KnowledgeEntity]:
        """
        Search for entities within the context of specified entities.

        Args:
            query: Search query string
            context_entity_ids: Set of context entity IDs
            user_id: The user ID for access control
            limit: Maximum number of entities to return

        Returns:
            List of matching entities

        Raises:
            RepositoryError: If search fails
        """
        pass

    @abstractmethod
    async def get_entity_paths(
        self,
        source_entity_id: uuid.UUID,
        target_entity_id: uuid.UUID,
        max_depth: int = 5,
    ) -> List[List[uuid.UUID]]:
        """
        Find paths between two entities.

        Args:
            source_entity_id: The source entity ID
            target_entity_id: The target entity ID
            max_depth: Maximum path depth

        Returns:
            List of paths (each path is a list of entity IDs)

        Raises:
            RepositoryError: If search fails
        """
        pass

    @abstractmethod
    async def update(self, graph: KnowledgeGraph) -> KnowledgeGraph:
        """
        Update an existing knowledge graph.

        Args:
            graph: The graph to update

        Returns:
            The updated graph

        Raises:
            RepositoryError: If update fails
            NotFoundError: If graph not found
        """
        pass

    @abstractmethod
    async def delete(self, graph_id: uuid.UUID) -> bool:
        """
        Delete a knowledge graph.

        Args:
            graph_id: The graph ID to delete

        Returns:
            True if deleted, False if not found

        Raises:
            RepositoryError: If deletion fails
        """
        pass
