"""
Knowledge Graph API routes.

This module contains the FastAPI routes for knowledge graph operations.
"""

import uuid

from fastapi import APIRouter, Depends, HTTPException, Query, status
from pydantic import Field

from src.application.use_cases.knowledge_graph_service import KnowledgeGraphService
from src.domain.entities.knowledge_graph import (
    EntityType,
    KnowledgeEntity,
    RelationType,
)
from src.domain.entities.user import User
from src.infrastructure.database.connection import get_db_session
from src.infrastructure.database.repositories.knowledge_graph_repository_impl import (
    KnowledgeEntityRepositoryImpl,
    KnowledgeGraphRepositoryImpl,
    KnowledgeRelationshipRepositoryImpl,
)
from src.infrastructure.logging.setup import get_logger
from src.infrastructure.security.jwt.jwt_manager import get_current_user
from src.presentation.api.schemas.knowledge_graph import (
    KnowledgeEntityCreateRequest,
    KnowledgeEntityResponse,
    KnowledgeEntityUpdateRequest,
    KnowledgeGraphResponse,
    KnowledgeRelationshipCreateRequest,
    KnowledgeRelationshipResponse,
)

logger = get_logger(__name__)

router = APIRouter(prefix="/knowledge-graph", tags=["Knowledge Graph"])


async def get_knowledge_graph_service(
    db_session=Depends(get_db_session),
) -> KnowledgeGraphService:
    """
    Dependency to get knowledge graph service.

    Args:
        db_session: Database session

    Returns:
        KnowledgeGraphService instance
    """
    entity_repo = KnowledgeEntityRepositoryImpl(db_session)
    relationship_repo = KnowledgeRelationshipRepositoryImpl(db_session)
    graph_repo = KnowledgeGraphRepositoryImpl(db_session)

    return KnowledgeGraphService(entity_repo, relationship_repo, graph_repo)


# Entity endpoints
@router.post(
    "/entities",
    response_model=KnowledgeEntityResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create knowledge entity",
    description="Create a new knowledge entity in the graph",
)
async def create_entity(
    request: KnowledgeEntityCreateRequest,
    current_user: User = Depends(get_current_user),
    service: KnowledgeGraphService = Depends(get_knowledge_graph_service),
) -> KnowledgeEntityResponse:
    """Create a new knowledge entity."""
    try:
        # Convert request to domain entity
        entity = KnowledgeEntity(
            name=request.name,
            description=request.description,
            entity_type=request.entity_type,
            properties=request.properties,
            attributes=request.attributes,
            tags=request.tags,
            categories=request.categories,
            confidence_score=request.confidence_score,
            source_id=request.source_id,
            source_type=request.source_type,
            source_metadata=request.source_metadata,
            created_by=uuid.UUID(current_user.id),
            is_public=request.is_public,
        )

        # Create entity
        created_entity = await service.create_entity(entity, current_user)

        logger.info(
            f"Created knowledge entity {created_entity.id} for user {current_user.id}"
        )

        return KnowledgeEntityResponse.from_domain(created_entity)

    except PermissionError as e:
        logger.warning(
            f"Permission denied creating entity for user {current_user.id}: {e}"
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to create knowledge entities",
        )
    except Exception as e:
        logger.error(f"Failed to create knowledge entity: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create knowledge entity",
        )


@router.get(
    "/entities/{entity_id}",
    response_model=KnowledgeEntityResponse,
    summary="Get knowledge entity",
    description="Get a knowledge entity by ID",
)
async def get_entity(
    entity_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    service: KnowledgeGraphService = Depends(get_knowledge_graph_service),
) -> KnowledgeEntityResponse:
    """Get a knowledge entity by ID."""
    try:
        entity = await service.get_entity(entity_id, current_user)

        if not entity:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Knowledge entity not found",
            )

        return KnowledgeEntityResponse.from_domain(entity)

    except PermissionError as e:
        logger.warning(
            f"Permission denied accessing entity {entity_id} for user {current_user.id}: {e}"
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to access this entity",
        )
    except Exception as e:
        logger.error(f"Failed to get knowledge entity {entity_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get knowledge entity",
        )


@router.get(
    "/entities",
    response_model=list[KnowledgeEntityResponse],
    summary="List knowledge entities",
    description="List knowledge entities with optional filtering",
)
async def list_entities(
    entity_type: EntityType | None = Query(None, description="Filter by entity type"),
    limit: int = Query(
        100, ge=1, le=1000, description="Maximum number of entities to return"
    ),
    offset: int = Query(0, ge=0, description="Number of entities to skip"),
    current_user: User = Depends(get_current_user),
    service: KnowledgeGraphService = Depends(get_knowledge_graph_service),
) -> list[KnowledgeEntityResponse]:
    """List knowledge entities for the current user."""
    try:
        entities = await service.list_entities(current_user, entity_type, limit, offset)

        return [KnowledgeEntityResponse.from_domain(entity) for entity in entities]

    except PermissionError as e:
        logger.warning(
            f"Permission denied listing entities for user {current_user.id}: {e}"
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to list knowledge entities",
        )
    except Exception as e:
        logger.error(f"Failed to list knowledge entities: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list knowledge entities",
        )


@router.get(
    "/entities/search",
    response_model=list[KnowledgeEntityResponse],
    summary="Search knowledge entities",
    description="Search knowledge entities by name, description, or tags",
)
async def search_entities(
    query: str = Query(..., min_length=1, description="Search query"),
    entity_type: EntityType | None = Query(None, description="Filter by entity type"),
    include_public: bool = Query(True, description="Include public entities"),
    limit: int = Query(
        100, ge=1, le=1000, description="Maximum number of entities to return"
    ),
    offset: int = Query(0, ge=0, description="Number of entities to skip"),
    current_user: User = Depends(get_current_user),
    service: KnowledgeGraphService = Depends(get_knowledge_graph_service),
) -> list[KnowledgeEntityResponse]:
    """Search knowledge entities."""
    try:
        entities = await service.search_entities(
            query, current_user, entity_type, include_public, limit, offset
        )

        return [KnowledgeEntityResponse.from_domain(entity) for entity in entities]

    except PermissionError as e:
        logger.warning(
            f"Permission denied searching entities for user {current_user.id}: {e}"
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to search knowledge entities",
        )
    except Exception as e:
        logger.error(f"Failed to search knowledge entities: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to search knowledge entities",
        )


@router.put(
    "/entities/{entity_id}",
    response_model=KnowledgeEntityResponse,
    summary="Update knowledge entity",
    description="Update an existing knowledge entity",
)
async def update_entity(
    entity_id: uuid.UUID,
    request: KnowledgeEntityUpdateRequest,
    current_user: User = Depends(get_current_user),
    service: KnowledgeGraphService = Depends(get_knowledge_graph_service),
) -> KnowledgeEntityResponse:
    """Update a knowledge entity."""
    try:
        # Get existing entity
        existing_entity = await service.get_entity(entity_id, current_user)
        if not existing_entity:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Knowledge entity not found",
            )

        # Update entity with provided fields
        if request.name is not None:
            existing_entity.name = request.name
        if request.description is not None:
            existing_entity.description = request.description
        if request.entity_type is not None:
            existing_entity.entity_type = request.entity_type
        if request.properties is not None:
            existing_entity.properties = request.properties
        if request.attributes is not None:
            existing_entity.attributes = request.attributes
        if request.tags is not None:
            existing_entity.tags = request.tags
        if request.categories is not None:
            existing_entity.categories = request.categories
        if request.confidence_score is not None:
            existing_entity.confidence_score = request.confidence_score
        if request.is_public is not None:
            existing_entity.is_public = request.is_public

        # Update entity
        updated_entity = await service.update_entity(existing_entity, current_user)

        logger.info(f"Updated knowledge entity {entity_id} for user {current_user.id}")

        return KnowledgeEntityResponse.from_domain(updated_entity)

    except PermissionError as e:
        logger.warning(
            f"Permission denied updating entity {entity_id} for user {current_user.id}: {e}"
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to update this entity",
        )
    except Exception as e:
        logger.error(f"Failed to update knowledge entity {entity_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update knowledge entity",
        )


@router.delete(
    "/entities/{entity_id}",
    status_code=status.HTTP_200_OK,
    summary="Delete knowledge entity",
    description="Delete a knowledge entity and its relationships",
)
async def delete_entity(
    entity_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    service: KnowledgeGraphService = Depends(get_knowledge_graph_service),
) -> dict:
    """Delete a knowledge entity."""
    try:
        deleted = await service.delete_entity(entity_id, current_user)

        if not deleted:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Knowledge entity not found",
            )

        logger.info(f"Deleted knowledge entity {entity_id} for user {current_user.id}")

        return {
            "success": True,
            "message": f"Knowledge entity {entity_id} deleted successfully",
        }

    except PermissionError as e:
        logger.warning(
            f"Permission denied deleting entity {entity_id} for user {current_user.id}: {e}"
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to delete this entity",
        )
    except Exception as e:
        logger.error(f"Failed to delete knowledge entity {entity_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete knowledge entity",
        )


# Relationship endpoints
@router.post(
    "/relationships",
    response_model=KnowledgeRelationshipResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create knowledge relationship",
    description="Create a new relationship between knowledge entities",
)
async def create_relationship(
    request: KnowledgeRelationshipCreateRequest,
    current_user: User = Depends(get_current_user),
    service: KnowledgeGraphService = Depends(get_knowledge_graph_service),
) -> KnowledgeRelationshipResponse:
    """Create a new knowledge relationship."""
    try:
        from src.domain.entities.knowledge_graph import KnowledgeRelationship

        # Convert request to domain entity
        relationship = KnowledgeRelationship(
            source_entity_id=request.source_entity_id,
            target_entity_id=request.target_entity_id,
            relation_type=request.relation_type,
            properties=request.properties,
            weight=request.weight,
            confidence_score=request.confidence_score,
            is_directed=request.is_directed,
            description=request.description,
            tags=request.tags,
            source_id=request.source_id,
            source_type=request.source_type,
            source_metadata=request.source_metadata,
            created_by=uuid.UUID(current_user.id),
        )

        # Create relationship
        created_relationship = await service.create_relationship(
            relationship, current_user
        )

        logger.info(
            f"Created knowledge relationship {created_relationship.id} for user {current_user.id}"
        )

        return KnowledgeRelationshipResponse.from_domain(created_relationship)

    except PermissionError as e:
        logger.warning(
            f"Permission denied creating relationship for user {current_user.id}: {e}"
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to create knowledge relationships",
        )
    except ValueError as e:
        logger.warning(f"Invalid relationship data: {e}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to create knowledge relationship: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create knowledge relationship",
        )


@router.get(
    "/entities/{entity_id}/relationships",
    response_model=list[KnowledgeRelationshipResponse],
    summary="Get entity relationships",
    description="Get all relationships for a specific entity",
)
async def get_entity_relationships(
    entity_id: uuid.UUID,
    relation_type: RelationType | None = Query(
        None, description="Filter by relationship type"
    ),
    direction: str | None = Query(
        None, regex="^(incoming|outgoing)$", description="Filter by direction"
    ),
    current_user: User = Depends(get_current_user),
    service: KnowledgeGraphService = Depends(get_knowledge_graph_service),
) -> list[KnowledgeRelationshipResponse]:
    """Get relationships for an entity."""
    try:
        relationships = await service.get_entity_relationships(
            entity_id, current_user, relation_type, direction
        )

        return [KnowledgeRelationshipResponse.from_domain(rel) for rel in relationships]

    except PermissionError as e:
        logger.warning(
            f"Permission denied accessing relationships for entity {entity_id}: {e}"
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to access entity relationships",
        )
    except ValueError as e:
        logger.warning(f"Invalid entity ID {entity_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Entity not found or not accessible",
        )
    except Exception as e:
        logger.error(f"Failed to get entity relationships: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get entity relationships",
        )


# Graph endpoints
@router.post(
    "/subgraph",
    response_model=KnowledgeGraphResponse,
    summary="Get subgraph",
    description="Get a subgraph containing specified entities and their neighbors",
)
async def get_subgraph(
    entity_ids: list[uuid.UUID] = Field(
        ..., description="List of entity IDs to include"
    ),
    max_depth: int = Field(
        2, ge=1, le=5, description="Maximum depth for neighbor traversal"
    ),
    current_user: User = Depends(get_current_user),
    service: KnowledgeGraphService = Depends(get_knowledge_graph_service),
) -> KnowledgeGraphResponse:
    """Get a subgraph containing specified entities and their neighbors."""
    try:
        if not entity_ids:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="At least one entity ID must be provided",
            )

        subgraph = await service.get_subgraph(set(entity_ids), current_user, max_depth)

        return KnowledgeGraphResponse.from_domain(subgraph)

    except PermissionError as e:
        logger.warning(
            f"Permission denied accessing subgraph for user {current_user.id}: {e}"
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to access knowledge graph",
        )
    except ValueError as e:
        logger.warning(f"Invalid subgraph request: {e}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to get subgraph: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get subgraph",
        )


@router.get(
    "/entities/search-in-context",
    response_model=list[KnowledgeEntityResponse],
    summary="Search entities in context",
    description="Search for entities within the context of specified entities",
)
async def search_entities_in_context(
    query: str = Query(..., min_length=1, description="Search query"),
    context_entity_ids: list[uuid.UUID] = Query(..., description="Context entity IDs"),
    limit: int = Query(
        100, ge=1, le=1000, description="Maximum number of entities to return"
    ),
    current_user: User = Depends(get_current_user),
    service: KnowledgeGraphService = Depends(get_knowledge_graph_service),
) -> list[KnowledgeEntityResponse]:
    """Search for entities within the context of specified entities."""
    try:
        if not context_entity_ids:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="At least one context entity ID must be provided",
            )

        entities = await service.search_entities_in_context(
            query, set(context_entity_ids), current_user, limit
        )

        return [KnowledgeEntityResponse.from_domain(entity) for entity in entities]

    except PermissionError as e:
        logger.warning(
            f"Permission denied searching in context for user {current_user.id}: {e}"
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to search knowledge entities",
        )
    except Exception as e:
        logger.error(f"Failed to search entities in context: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to search entities in context",
        )


@router.get(
    "/paths/{source_entity_id}/{target_entity_id}",
    response_model=list[list[uuid.UUID]],
    summary="Find entity paths",
    description="Find paths between two entities in the knowledge graph",
)
async def find_entity_paths(
    source_entity_id: uuid.UUID,
    target_entity_id: uuid.UUID,
    max_depth: int = Query(5, ge=1, le=10, description="Maximum path depth"),
    current_user: User = Depends(get_current_user),
    service: KnowledgeGraphService = Depends(get_knowledge_graph_service),
) -> list[list[uuid.UUID]]:
    """Find paths between two entities."""
    try:
        paths = await service.find_entity_paths(
            source_entity_id, target_entity_id, current_user, max_depth
        )

        return paths

    except PermissionError as e:
        logger.warning(
            f"Permission denied finding paths for user {current_user.id}: {e}"
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to access knowledge graph",
        )
    except ValueError as e:
        logger.warning(f"Invalid path request: {e}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to find entity paths: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to find entity paths",
        )
