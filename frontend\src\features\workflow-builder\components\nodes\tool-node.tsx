'use client';

import React, { memo, useCallback } from 'react';
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow';
import { cn } from '@/shared/lib/utils';
import { Badge } from '@/shared/ui/badge';
import { Button } from '@/shared/ui/button';
import { NodeData } from '../../types';
import {
  Tool,
  Settings,
  Play,
  CheckCircle,
  AlertCircle,
  Clock,
  Zap,
  Database,
  Globe,
  FileText,
  Image,
  MessageSquare,
  Calculator,
} from 'lucide-react';

interface ToolNodeData extends NodeData {
  toolType: ToolType;
  toolName: string;
  version?: string;
  configuration?: Record<string, any>;
  status?: ToolStatus;
  lastExecuted?: string;
  executionTime?: number;
  errorMessage?: string;
}

interface ToolNodeProps extends NodeProps {
  data: ToolNodeData;
}

export enum ToolType {
  API = 'api',
  DATABASE = 'database',
  FILE = 'file',
  IMAGE = 'image',
  TEXT = 'text',
  MATH = 'math',
  WEB = 'web',
  CUSTOM = 'custom',
}

export enum ToolStatus {
  IDLE = 'idle',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

// Tool type icon mapping
const getToolTypeIcon = (type: ToolType) => {
  switch (type) {
    case ToolType.API:
      return Zap;
    case ToolType.DATABASE:
      return Database;
    case ToolType.WEB:
      return Globe;
    case ToolType.FILE:
      return FileText;
    case ToolType.IMAGE:
      return Image;
    case ToolType.TEXT:
      return MessageSquare;
    case ToolType.MATH:
      return Calculator;
    default:
      return Tool;
  }
};

// Status icon mapping
const getStatusIcon = (status: ToolStatus) => {
  switch (status) {
    case ToolStatus.RUNNING:
      return Clock;
    case ToolStatus.COMPLETED:
      return CheckCircle;
    case ToolStatus.FAILED:
      return AlertCircle;
    default:
      return null;
  }
};

// Tool type color mapping
const getToolTypeColor = (type: ToolType) => {
  switch (type) {
    case ToolType.API:
      return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    case ToolType.DATABASE:
      return 'text-purple-600 bg-purple-50 border-purple-200';
    case ToolType.WEB:
      return 'text-blue-600 bg-blue-50 border-blue-200';
    case ToolType.FILE:
      return 'text-green-600 bg-green-50 border-green-200';
    case ToolType.IMAGE:
      return 'text-pink-600 bg-pink-50 border-pink-200';
    case ToolType.TEXT:
      return 'text-indigo-600 bg-indigo-50 border-indigo-200';
    case ToolType.MATH:
      return 'text-orange-600 bg-orange-50 border-orange-200';
    default:
      return 'text-gray-600 bg-gray-50 border-gray-200';
  }
};

export const ToolNode = memo<ToolNodeProps>(({ 
  data, 
  selected, 
  dragging,
  id 
}) => {
  const { 
    toolType, 
    toolName, 
    version, 
    status = ToolStatus.IDLE,
    executionTime,
    errorMessage,
    inputs = [],
    outputs = []
  } = data;
  
  const ToolIcon = getToolTypeIcon(toolType);
  const StatusIcon = getStatusIcon(status);
  const toolColorClass = getToolTypeColor(toolType);

  const handleExecute = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    console.log('Execute tool:', toolName);
  }, [toolName]);

  const handleConfigure = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    console.log('Configure tool:', toolName);
  }, [toolName]);

  const isActive = status === ToolStatus.RUNNING;
  const isCompleted = status === ToolStatus.COMPLETED;
  const isFailed = status === ToolStatus.FAILED;

  return (
    <div
      className={cn(
        'relative bg-background border-2 rounded-lg shadow-sm transition-all duration-200',
        'min-w-[240px] max-w-[280px]',
        selected && 'border-primary ring-2 ring-primary/20',
        dragging && 'shadow-lg scale-105',
        isActive && 'border-yellow-400 shadow-yellow-100',
        isFailed && 'border-red-400 shadow-red-100',
        isCompleted && 'border-green-400 shadow-green-100'
      )}
    >
      {/* Input Handles */}
      {inputs.map((input, index) => (
        <Handle
          key={input.id}
          type="target"
          position={Position.Left}
          id={input.id}
          className="w-2 h-2 border border-background bg-muted-foreground hover:bg-primary transition-colors"
          style={{ 
            left: -4, 
            top: 40 + (index * 20)
          }}
        />
      ))}

      {/* Output Handles */}
      {outputs.map((output, index) => (
        <Handle
          key={output.id}
          type="source"
          position={Position.Right}
          id={output.id}
          className="w-2 h-2 border border-background bg-muted-foreground hover:bg-primary transition-colors"
          style={{ 
            right: -4, 
            top: 40 + (index * 20)
          }}
        />
      ))}

      {/* Default handles if no specific inputs/outputs */}
      {inputs.length === 0 && (
        <Handle
          type="target"
          position={Position.Left}
          className="w-3 h-3 border-2 border-background bg-muted-foreground hover:bg-primary transition-colors"
          style={{ left: -6 }}
        />
      )}

      {outputs.length === 0 && (
        <Handle
          type="source"
          position={Position.Right}
          className="w-3 h-3 border-2 border-background bg-muted-foreground hover:bg-primary transition-colors"
          style={{ right: -6 }}
        />
      )}

      {/* Status indicator overlay */}
      <div className={cn(
        'absolute top-0 left-0 w-full h-1 rounded-t-lg transition-all duration-300',
        isActive && 'bg-gradient-to-r from-yellow-400 to-yellow-600',
        isCompleted && 'bg-gradient-to-r from-green-400 to-green-600',
        isFailed && 'bg-gradient-to-r from-red-400 to-red-600',
        status === ToolStatus.IDLE && 'bg-gradient-to-r from-gray-300 to-gray-400'
      )} />

      {/* Header */}
      <div className="p-3">
        <div className="flex items-start justify-between mb-2">
          <div className="flex items-center space-x-2 min-w-0 flex-1">
            <div className={cn(
              'p-1.5 rounded-md border',
              toolColorClass
            )}>
              <ToolIcon className="h-4 w-4" />
            </div>
            <div className="min-w-0 flex-1">
              <h3 className="font-semibold text-sm truncate">
                {toolName}
              </h3>
              <div className="flex items-center gap-1 mt-1">
                <Badge variant="outline" className="text-xs">
                  {toolType}
                </Badge>
                {version && (
                  <Badge variant="secondary" className="text-xs">
                    v{version}
                  </Badge>
                )}
              </div>
            </div>
          </div>
          
          {StatusIcon && (
            <StatusIcon className={cn(
              'h-4 w-4',
              isActive && 'text-yellow-600 animate-pulse',
              isCompleted && 'text-green-600',
              isFailed && 'text-red-600'
            )} />
          )}
        </div>

        {data.description && (
          <p className="text-xs text-muted-foreground line-clamp-2 mb-2">
            {data.description}
          </p>
        )}

        {/* Input/Output Summary */}
        {(inputs.length > 0 || outputs.length > 0) && (
          <div className="flex justify-between text-xs text-muted-foreground mb-2">
            <span>Inputs: {inputs.length}</span>
            <span>Outputs: {outputs.length}</span>
          </div>
        )}

        {/* Controls */}
        <div className="flex gap-1">
          {status === ToolStatus.IDLE && (
            <Button size="sm" onClick={handleExecute} className="flex-1">
              <Play className="h-3 w-3 mr-1" />
              Run
            </Button>
          )}
          
          {(status === ToolStatus.COMPLETED || status === ToolStatus.FAILED) && (
            <Button size="sm" onClick={handleExecute} className="flex-1">
              <Play className="h-3 w-3 mr-1" />
              Rerun
            </Button>
          )}
          
          <Button size="sm" variant="ghost" onClick={handleConfigure}>
            <Settings className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Execution indicator */}
      {isActive && (
        <div className="absolute inset-0 bg-gradient-to-br from-yellow-500/5 to-yellow-600/10 rounded-lg pointer-events-none" />
      )}
      
      {/* Error indicator */}
      {isFailed && errorMessage && (
        <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full">
          Error
        </div>
      )}
      
      {/* Execution time indicator */}
      {executionTime && (
        <div className="absolute -top-2 -left-2 bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
          {executionTime < 1000 
            ? `${executionTime}ms`
            : `${(executionTime / 1000).toFixed(1)}s`
          }
        </div>
      )}
    </div>
  );
});

ToolNode.displayName = 'ToolNode';
