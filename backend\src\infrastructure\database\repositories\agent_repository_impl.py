"""
Agent repository implementation.

This module contains the SQLAlchemy implementation of the agent repository.
"""

import uuid
from typing import List, Optional

from sqlalchemy import and_, func, or_, text
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload

from src.domain.entities.agent import Agent, AgentStatus, AgentType
from src.domain.repositories.agent_repository import AgentRepository
from src.infrastructure.database.models.agent import AgentModel
from src.infrastructure.logging.setup import get_logger

logger = get_logger(__name__)


class AgentRepositoryImpl(AgentRepository):
    """SQLAlchemy implementation of agent repository."""

    def __init__(self, session: AsyncSession) -> None:
        """
        Initialize repository with database session.

        Args:
            session: SQLAlchemy async session
        """
        self.session = session

    async def create(self, agent: Agent) -> Agent:
        """Create a new agent."""
        try:
            agent_model = AgentModel.from_domain(agent)
            self.session.add(agent_model)
            await self.session.commit()
            await self.session.refresh(agent_model)
            
            logger.info(f"Created agent {agent_model.id}")
            return agent_model.to_domain()
            
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Failed to create agent: {e}")
            raise

    async def get_by_id(self, agent_id: uuid.UUID) -> Optional[Agent]:
        """Get an agent by ID."""
        try:
            stmt = (
                select(AgentModel)
                .options(selectinload(AgentModel.creator))
                .where(AgentModel.id == agent_id)
            )
            result = await self.session.execute(stmt)
            agent_model = result.scalar_one_or_none()
            
            return agent_model.to_domain() if agent_model else None
            
        except Exception as e:
            logger.error(f"Failed to get agent {agent_id}: {e}")
            raise

    async def get_by_name(self, name: str, user_id: uuid.UUID) -> Optional[Agent]:
        """Get an agent by name for a specific user."""
        try:
            stmt = (
                select(AgentModel)
                .options(selectinload(AgentModel.creator))
                .where(
                    and_(
                        AgentModel.name == name,
                        AgentModel.created_by == user_id
                    )
                )
            )
            result = await self.session.execute(stmt)
            agent_model = result.scalar_one_or_none()
            
            return agent_model.to_domain() if agent_model else None
            
        except Exception as e:
            logger.error(f"Failed to get agent by name {name}: {e}")
            raise

    async def get_by_user(
        self,
        user_id: uuid.UUID,
        status: Optional[AgentStatus] = None,
        agent_type: Optional[AgentType] = None,
        limit: int = 100,
        offset: int = 0,
    ) -> List[Agent]:
        """Get agents by user ID with optional filtering."""
        try:
            conditions = [AgentModel.created_by == user_id]
            
            if status:
                conditions.append(AgentModel.status == status)
            
            if agent_type:
                conditions.append(AgentModel.agent_type == agent_type)
            
            stmt = (
                select(AgentModel)
                .options(selectinload(AgentModel.creator))
                .where(and_(*conditions))
                .order_by(AgentModel.updated_at.desc())
                .limit(limit)
                .offset(offset)
            )
            
            result = await self.session.execute(stmt)
            agent_models = result.scalars().all()
            
            return [model.to_domain() for model in agent_models]
            
        except Exception as e:
            logger.error(f"Failed to get agents for user {user_id}: {e}")
            raise

    async def get_public_agents(
        self,
        agent_type: Optional[AgentType] = None,
        limit: int = 100,
        offset: int = 0,
    ) -> List[Agent]:
        """Get public agents with optional filtering."""
        try:
            conditions = [
                AgentModel.metadata.op('->>')('is_public').cast(text('boolean')) == True
            ]
            
            if agent_type:
                conditions.append(AgentModel.agent_type == agent_type)
            
            stmt = (
                select(AgentModel)
                .options(selectinload(AgentModel.creator))
                .where(and_(*conditions))
                .order_by(AgentModel.updated_at.desc())
                .limit(limit)
                .offset(offset)
            )
            
            result = await self.session.execute(stmt)
            agent_models = result.scalars().all()
            
            return [model.to_domain() for model in agent_models]
            
        except Exception as e:
            logger.error(f"Failed to get public agents: {e}")
            raise

    async def get_by_capability(
        self,
        capability_name: str,
        user_id: Optional[uuid.UUID] = None,
        limit: int = 100,
        offset: int = 0,
    ) -> List[Agent]:
        """Get agents by capability."""
        try:
            conditions = [
                AgentModel.capabilities.op('@>')([{"name": capability_name}])
            ]
            
            if user_id:
                conditions.append(AgentModel.created_by == user_id)
            
            stmt = (
                select(AgentModel)
                .options(selectinload(AgentModel.creator))
                .where(and_(*conditions))
                .order_by(AgentModel.updated_at.desc())
                .limit(limit)
                .offset(offset)
            )
            
            result = await self.session.execute(stmt)
            agent_models = result.scalars().all()
            
            return [model.to_domain() for model in agent_models]
            
        except Exception as e:
            logger.error(f"Failed to get agents by capability {capability_name}: {e}")
            raise

    async def search(
        self,
        query: str,
        user_id: Optional[uuid.UUID] = None,
        include_public: bool = True,
        limit: int = 100,
        offset: int = 0,
    ) -> List[Agent]:
        """Search agents by name, description, or tags."""
        try:
            search_conditions = [
                AgentModel.name.ilike(f"%{query}%"),
                AgentModel.description.ilike(f"%{query}%"),
                AgentModel.tags.op('@>')([query])
            ]
            
            conditions = [or_(*search_conditions)]
            
            if user_id and not include_public:
                conditions.append(AgentModel.created_by == user_id)
            elif user_id and include_public:
                conditions.append(
                    or_(
                        AgentModel.created_by == user_id,
                        AgentModel.metadata.op('->>')('is_public').cast(text('boolean')) == True
                    )
                )
            elif include_public:
                conditions.append(
                    AgentModel.metadata.op('->>')('is_public').cast(text('boolean')) == True
                )
            
            stmt = (
                select(AgentModel)
                .options(selectinload(AgentModel.creator))
                .where(and_(*conditions))
                .order_by(AgentModel.updated_at.desc())
                .limit(limit)
                .offset(offset)
            )
            
            result = await self.session.execute(stmt)
            agent_models = result.scalars().all()
            
            return [model.to_domain() for model in agent_models]
            
        except Exception as e:
            logger.error(f"Failed to search agents with query '{query}': {e}")
            raise

    async def update(self, agent: Agent) -> Agent:
        """Update an existing agent."""
        try:
            stmt = select(AgentModel).where(AgentModel.id == agent.id)
            result = await self.session.execute(stmt)
            agent_model = result.scalar_one_or_none()
            
            if not agent_model:
                raise ValueError(f"Agent {agent.id} not found")
            
            # Update fields from domain entity
            updated_model = AgentModel.from_domain(agent)
            
            # Copy updated fields
            agent_model.name = updated_model.name
            agent_model.description = updated_model.description
            agent_model.agent_type = updated_model.agent_type
            agent_model.version = updated_model.version
            agent_model.status = updated_model.status
            agent_model.current_task_id = updated_model.current_task_id
            agent_model.configuration = updated_model.configuration
            agent_model.capabilities = updated_model.capabilities
            agent_model.metrics = updated_model.metrics
            agent_model.parent_agent_id = updated_model.parent_agent_id
            agent_model.child_agent_ids = updated_model.child_agent_ids
            agent_model.tags = updated_model.tags
            agent_model.metadata = updated_model.metadata
            agent_model.updated_at = updated_model.updated_at
            
            await self.session.commit()
            await self.session.refresh(agent_model)
            
            logger.info(f"Updated agent {agent.id}")
            return agent_model.to_domain()
            
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Failed to update agent {agent.id}: {e}")
            raise

    async def delete(self, agent_id: uuid.UUID) -> bool:
        """Delete an agent."""
        try:
            stmt = select(AgentModel).where(AgentModel.id == agent_id)
            result = await self.session.execute(stmt)
            agent_model = result.scalar_one_or_none()
            
            if not agent_model:
                return False
            
            await self.session.delete(agent_model)
            await self.session.commit()
            
            logger.info(f"Deleted agent {agent_id}")
            return True
            
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Failed to delete agent {agent_id}: {e}")
            raise

    async def get_active_agents(
        self,
        user_id: Optional[uuid.UUID] = None,
        limit: int = 100,
        offset: int = 0,
    ) -> List[Agent]:
        """Get all active agents."""
        try:
            conditions = [AgentModel.status == AgentStatus.IDLE]
            
            if user_id:
                conditions.append(AgentModel.created_by == user_id)
            
            stmt = (
                select(AgentModel)
                .options(selectinload(AgentModel.creator))
                .where(and_(*conditions))
                .order_by(AgentModel.updated_at.desc())
                .limit(limit)
                .offset(offset)
            )
            
            result = await self.session.execute(stmt)
            agent_models = result.scalars().all()
            
            return [model.to_domain() for model in agent_models]
            
        except Exception as e:
            logger.error(f"Failed to get active agents: {e}")
            raise

    async def get_agents_by_parent(
        self,
        parent_agent_id: uuid.UUID,
        limit: int = 100,
        offset: int = 0,
    ) -> List[Agent]:
        """Get child agents by parent agent ID."""
        try:
            stmt = (
                select(AgentModel)
                .options(selectinload(AgentModel.creator))
                .where(AgentModel.parent_agent_id == parent_agent_id)
                .order_by(AgentModel.updated_at.desc())
                .limit(limit)
                .offset(offset)
            )
            
            result = await self.session.execute(stmt)
            agent_models = result.scalars().all()
            
            return [model.to_domain() for model in agent_models]
            
        except Exception as e:
            logger.error(f"Failed to get child agents for parent {parent_agent_id}: {e}")
            raise

    async def count_by_user(
        self,
        user_id: uuid.UUID,
        status: Optional[AgentStatus] = None,
        agent_type: Optional[AgentType] = None,
    ) -> int:
        """Count agents by user ID with optional filtering."""
        try:
            conditions = [AgentModel.created_by == user_id]
            
            if status:
                conditions.append(AgentModel.status == status)
            
            if agent_type:
                conditions.append(AgentModel.agent_type == agent_type)
            
            stmt = (
                select(func.count(AgentModel.id))
                .where(and_(*conditions))
            )
            
            result = await self.session.execute(stmt)
            count = result.scalar()
            
            return count or 0
            
        except Exception as e:
            logger.error(f"Failed to count agents for user {user_id}: {e}")
            raise

    async def get_agent_metrics_summary(self, user_id: uuid.UUID) -> dict:
        """Get aggregated metrics for all user agents."""
        try:
            stmt = (
                select(AgentModel)
                .where(AgentModel.created_by == user_id)
            )
            
            result = await self.session.execute(stmt)
            agent_models = result.scalars().all()
            
            total_agents = len(agent_models)
            active_agents = sum(1 for a in agent_models if a.status == AgentStatus.IDLE)
            total_executions = sum(a.metrics.get("total_executions", 0) for a in agent_models)
            total_cost = sum(a.metrics.get("total_cost", 0.0) for a in agent_models)
            total_tokens = sum(a.metrics.get("total_tokens_used", 0) for a in agent_models)
            
            avg_success_rate = 0.0
            if agent_models:
                success_rates = []
                for a in agent_models:
                    total_exec = a.metrics.get("total_executions", 0)
                    successful_exec = a.metrics.get("successful_executions", 0)
                    if total_exec > 0:
                        success_rates.append(successful_exec / total_exec)
                
                if success_rates:
                    avg_success_rate = sum(success_rates) / len(success_rates)
            
            return {
                "total_agents": total_agents,
                "active_agents": active_agents,
                "total_executions": total_executions,
                "total_cost": total_cost,
                "total_tokens_used": total_tokens,
                "average_success_rate": avg_success_rate * 100,
            }
            
        except Exception as e:
            logger.error(f"Failed to get agent metrics summary for user {user_id}: {e}")
            raise
