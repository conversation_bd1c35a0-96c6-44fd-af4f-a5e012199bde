"""
Workflow domain events.

This module defines domain events for workflow operations.
"""

import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

from src.domain.events.base import DomainEvent
from src.domain.entities.workflow import ExecutionStatus, WorkflowStatus


class WorkflowCreatedEvent(DomainEvent):
    """Event raised when a workflow is created."""

    def __init__(
        self,
        workflow_id: uuid.UUID,
        name: str,
        owner_id: uuid.UUID,
        is_public: bool,
        node_count: int,
        edge_count: int,
        occurred_at: Optional[datetime] = None,
    ):
        super().__init__(occurred_at)
        self.workflow_id = workflow_id
        self.name = name
        self.owner_id = owner_id
        self.is_public = is_public
        self.node_count = node_count
        self.edge_count = edge_count

    @property
    def event_type(self) -> str:
        return "workflow.created"

    def to_dict(self) -> Dict[str, Any]:
        return {
            **super().to_dict(),
            "workflow_id": str(self.workflow_id),
            "name": self.name,
            "owner_id": str(self.owner_id),
            "is_public": self.is_public,
            "node_count": self.node_count,
            "edge_count": self.edge_count,
        }


class WorkflowUpdatedEvent(DomainEvent):
    """Event raised when a workflow is updated."""

    def __init__(
        self,
        workflow_id: uuid.UUID,
        name: str,
        owner_id: uuid.UUID,
        changes: Dict[str, Any],
        occurred_at: Optional[datetime] = None,
    ):
        super().__init__(occurred_at)
        self.workflow_id = workflow_id
        self.name = name
        self.owner_id = owner_id
        self.changes = changes

    @property
    def event_type(self) -> str:
        return "workflow.updated"

    def to_dict(self) -> Dict[str, Any]:
        return {
            **super().to_dict(),
            "workflow_id": str(self.workflow_id),
            "name": self.name,
            "owner_id": str(self.owner_id),
            "changes": self.changes,
        }


class WorkflowStatusChangedEvent(DomainEvent):
    """Event raised when a workflow status changes."""

    def __init__(
        self,
        workflow_id: uuid.UUID,
        name: str,
        old_status: WorkflowStatus,
        new_status: WorkflowStatus,
        owner_id: uuid.UUID,
        occurred_at: Optional[datetime] = None,
    ):
        super().__init__(occurred_at)
        self.workflow_id = workflow_id
        self.name = name
        self.old_status = old_status
        self.new_status = new_status
        self.owner_id = owner_id

    @property
    def event_type(self) -> str:
        return "workflow.status_changed"

    def to_dict(self) -> Dict[str, Any]:
        return {
            **super().to_dict(),
            "workflow_id": str(self.workflow_id),
            "name": self.name,
            "old_status": self.old_status.value,
            "new_status": self.new_status.value,
            "owner_id": str(self.owner_id),
        }


class WorkflowExecutionStartedEvent(DomainEvent):
    """Event raised when workflow execution starts."""

    def __init__(
        self,
        execution_id: uuid.UUID,
        workflow_id: uuid.UUID,
        workflow_name: str,
        user_id: uuid.UUID,
        input_data: Dict[str, Any],
        occurred_at: Optional[datetime] = None,
    ):
        super().__init__(occurred_at)
        self.execution_id = execution_id
        self.workflow_id = workflow_id
        self.workflow_name = workflow_name
        self.user_id = user_id
        self.input_data = input_data

    @property
    def event_type(self) -> str:
        return "workflow_execution.started"

    def to_dict(self) -> Dict[str, Any]:
        return {
            **super().to_dict(),
            "execution_id": str(self.execution_id),
            "workflow_id": str(self.workflow_id),
            "workflow_name": self.workflow_name,
            "user_id": str(self.user_id),
            "input_data": self.input_data,
        }


class WorkflowExecutionCompletedEvent(DomainEvent):
    """Event raised when workflow execution completes."""

    def __init__(
        self,
        execution_id: uuid.UUID,
        workflow_id: uuid.UUID,
        workflow_name: str,
        user_id: uuid.UUID,
        output_data: Dict[str, Any],
        execution_time: float,
        cost: float,
        occurred_at: Optional[datetime] = None,
    ):
        super().__init__(occurred_at)
        self.execution_id = execution_id
        self.workflow_id = workflow_id
        self.workflow_name = workflow_name
        self.user_id = user_id
        self.output_data = output_data
        self.execution_time = execution_time
        self.cost = cost

    @property
    def event_type(self) -> str:
        return "workflow_execution.completed"

    def to_dict(self) -> Dict[str, Any]:
        return {
            **super().to_dict(),
            "execution_id": str(self.execution_id),
            "workflow_id": str(self.workflow_id),
            "workflow_name": self.workflow_name,
            "user_id": str(self.user_id),
            "output_data": self.output_data,
            "execution_time": self.execution_time,
            "cost": self.cost,
        }


class WorkflowExecutionFailedEvent(DomainEvent):
    """Event raised when workflow execution fails."""

    def __init__(
        self,
        execution_id: uuid.UUID,
        workflow_id: uuid.UUID,
        workflow_name: str,
        user_id: uuid.UUID,
        error_message: str,
        error_details: Dict[str, Any],
        failed_node_id: Optional[str],
        execution_time: float,
        cost: float,
        occurred_at: Optional[datetime] = None,
    ):
        super().__init__(occurred_at)
        self.execution_id = execution_id
        self.workflow_id = workflow_id
        self.workflow_name = workflow_name
        self.user_id = user_id
        self.error_message = error_message
        self.error_details = error_details
        self.failed_node_id = failed_node_id
        self.execution_time = execution_time
        self.cost = cost

    @property
    def event_type(self) -> str:
        return "workflow_execution.failed"

    def to_dict(self) -> Dict[str, Any]:
        return {
            **super().to_dict(),
            "execution_id": str(self.execution_id),
            "workflow_id": str(self.workflow_id),
            "workflow_name": self.workflow_name,
            "user_id": str(self.user_id),
            "error_message": self.error_message,
            "error_details": self.error_details,
            "failed_node_id": self.failed_node_id,
            "execution_time": self.execution_time,
            "cost": self.cost,
        }


class WorkflowNodeExecutionEvent(DomainEvent):
    """Event raised when a workflow node executes."""

    def __init__(
        self,
        execution_id: uuid.UUID,
        workflow_id: uuid.UUID,
        node_id: str,
        node_type: str,
        status: str,
        input_data: Dict[str, Any],
        output_data: Optional[Dict[str, Any]],
        execution_time: Optional[float],
        error_message: Optional[str],
        occurred_at: Optional[datetime] = None,
    ):
        super().__init__(occurred_at)
        self.execution_id = execution_id
        self.workflow_id = workflow_id
        self.node_id = node_id
        self.node_type = node_type
        self.status = status
        self.input_data = input_data
        self.output_data = output_data
        self.execution_time = execution_time
        self.error_message = error_message

    @property
    def event_type(self) -> str:
        return "workflow_node.executed"

    def to_dict(self) -> Dict[str, Any]:
        return {
            **super().to_dict(),
            "execution_id": str(self.execution_id),
            "workflow_id": str(self.workflow_id),
            "node_id": self.node_id,
            "node_type": self.node_type,
            "status": self.status,
            "input_data": self.input_data,
            "output_data": self.output_data,
            "execution_time": self.execution_time,
            "error_message": self.error_message,
        }


class WorkflowValidationEvent(DomainEvent):
    """Event raised when workflow validation occurs."""

    def __init__(
        self,
        workflow_id: uuid.UUID,
        workflow_name: str,
        is_valid: bool,
        validation_errors: List[str],
        validation_warnings: List[str],
        user_id: uuid.UUID,
        occurred_at: Optional[datetime] = None,
    ):
        super().__init__(occurred_at)
        self.workflow_id = workflow_id
        self.workflow_name = workflow_name
        self.is_valid = is_valid
        self.validation_errors = validation_errors
        self.validation_warnings = validation_warnings
        self.user_id = user_id

    @property
    def event_type(self) -> str:
        return "workflow.validated"

    def to_dict(self) -> Dict[str, Any]:
        return {
            **super().to_dict(),
            "workflow_id": str(self.workflow_id),
            "workflow_name": self.workflow_name,
            "is_valid": self.is_valid,
            "validation_errors": self.validation_errors,
            "validation_warnings": self.validation_warnings,
            "user_id": str(self.user_id),
        }


class WorkflowDeletedEvent(DomainEvent):
    """Event raised when a workflow is deleted."""

    def __init__(
        self,
        workflow_id: uuid.UUID,
        name: str,
        owner_id: uuid.UUID,
        occurred_at: Optional[datetime] = None,
    ):
        super().__init__(occurred_at)
        self.workflow_id = workflow_id
        self.name = name
        self.owner_id = owner_id

    @property
    def event_type(self) -> str:
        return "workflow.deleted"

    def to_dict(self) -> Dict[str, Any]:
        return {
            **super().to_dict(),
            "workflow_id": str(self.workflow_id),
            "name": self.name,
            "owner_id": str(self.owner_id),
        }
