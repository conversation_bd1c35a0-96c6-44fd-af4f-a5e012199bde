import { useState, useEffect } from 'react';
import { apiClient } from '@/shared/lib/api';
import { ModelInfo, ModelInstance, ModelDownloadTask, ModelHealth, ModelType, ModelProvider, ModelStatus } from '@/shared/types';

export interface ModelSearchParams {
  query: string;
  model_type?: ModelType;
  provider?: ModelProvider;
  limit?: number;
}

export interface ModelInstanceCreateRequest {
  model_id: string;
  configuration?: Record<string, any>;
}

export interface ModelServingRequest {
  port?: number;
  configuration?: Record<string, any>;
}

export interface ModelDiscoveryParams {
  query?: string;
  model_type?: ModelType;
  limit?: number;
}

// API endpoints
const MODEL_ENDPOINTS = {
  DISCOVER_HUGGINGFACE: '/models/discover/huggingface',
  DISCOVER_OLLAMA: '/models/discover/ollama',
  SEARCH: '/models/search',
  INSTANCES: '/models/instances',
  INSTANCE_BY_ID: (id: string) => `/models/instances/${id}`,
  DOWNLOAD: (id: string) => `/models/instances/${id}/download`,
  DOWNLOAD_TASK: (taskId: string) => `/models/downloads/${taskId}`,
  SERVE: (id: string) => `/models/instances/${id}/serve`,
  STOP: (id: string) => `/models/instances/${id}/stop`,
  HEALTH: (id: string) => `/models/instances/${id}/health`,
};

export const modelManagementService = {
  // Model discovery
  async discoverHuggingFaceModels(params: ModelDiscoveryParams = {}): Promise<ModelInfo[]> {
    const searchParams = new URLSearchParams();
    
    if (params.query) searchParams.append('query', params.query);
    if (params.model_type) searchParams.append('model_type', params.model_type);
    if (params.limit) searchParams.append('limit', params.limit.toString());

    const query = searchParams.toString();
    const endpoint = query ? `${MODEL_ENDPOINTS.DISCOVER_HUGGINGFACE}?${query}` : MODEL_ENDPOINTS.DISCOVER_HUGGINGFACE;
    
    const response = await apiClient.get<ModelInfo[]>(endpoint);
    return response.data;
  },

  async discoverOllamaModels(): Promise<ModelInfo[]> {
    const response = await apiClient.get<ModelInfo[]>(MODEL_ENDPOINTS.DISCOVER_OLLAMA);
    return response.data;
  },

  async searchModels(params: ModelSearchParams): Promise<ModelInfo[]> {
    const response = await apiClient.post<ModelInfo[]>(MODEL_ENDPOINTS.SEARCH, params);
    return response.data;
  },

  // Model instances
  async createModelInstance(request: ModelInstanceCreateRequest): Promise<ModelInstance> {
    const response = await apiClient.post<ModelInstance>(MODEL_ENDPOINTS.INSTANCES, request);
    return response.data;
  },

  async getModelInstance(id: string): Promise<ModelInstance> {
    const response = await apiClient.get<ModelInstance>(MODEL_ENDPOINTS.INSTANCE_BY_ID(id));
    return response.data;
  },

  async downloadModel(instanceId: string): Promise<ModelDownloadTask> {
    const response = await apiClient.post<ModelDownloadTask>(MODEL_ENDPOINTS.DOWNLOAD(instanceId));
    return response.data;
  },

  async getDownloadProgress(taskId: string): Promise<ModelDownloadTask> {
    const response = await apiClient.get<ModelDownloadTask>(MODEL_ENDPOINTS.DOWNLOAD_TASK(taskId));
    return response.data;
  },

  async cancelDownload(taskId: string): Promise<void> {
    await apiClient.delete(MODEL_ENDPOINTS.DOWNLOAD_TASK(taskId));
  },

  // Model serving
  async startModelServing(instanceId: string, request: ModelServingRequest = {}): Promise<{ success: boolean; serving_url?: string; api_endpoint?: string }> {
    const response = await apiClient.post<{ success: boolean; serving_url?: string; api_endpoint?: string }>(
      MODEL_ENDPOINTS.SERVE(instanceId), 
      request
    );
    return response.data;
  },

  async stopModelServing(instanceId: string): Promise<{ success: boolean }> {
    const response = await apiClient.post<{ success: boolean }>(MODEL_ENDPOINTS.STOP(instanceId));
    return response.data;
  },

  async checkModelHealth(instanceId: string): Promise<ModelHealth> {
    const response = await apiClient.get<ModelHealth>(MODEL_ENDPOINTS.HEALTH(instanceId));
    return response.data;
  },
};

// React hooks for model management
export function useModelDiscovery(provider: 'huggingface' | 'ollama', params: ModelDiscoveryParams = {}) {
  const [models, setModels] = useState<ModelInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchModels = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const data = provider === 'huggingface' 
          ? await modelManagementService.discoverHuggingFaceModels(params)
          : await modelManagementService.discoverOllamaModels();
        
        setModels(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to discover models');
      } finally {
        setLoading(false);
      }
    };

    fetchModels();
  }, [provider, params.query, params.model_type, params.limit]);

  const refetch = async () => {
    try {
      setError(null);
      const data = provider === 'huggingface' 
        ? await modelManagementService.discoverHuggingFaceModels(params)
        : await modelManagementService.discoverOllamaModels();
      setModels(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to discover models');
    }
  };

  return { models, loading, error, refetch };
}

export function useModelSearch(params: ModelSearchParams) {
  const [models, setModels] = useState<ModelInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const search = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await modelManagementService.searchModels(params);
      setModels(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to search models');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (params.query) {
      search();
    }
  }, [params.query, params.model_type, params.provider, params.limit]);

  return { models, loading, error, search };
}

export function useModelInstance(instanceId: string) {
  const [instance, setInstance] = useState<ModelInstance | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchInstance = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await modelManagementService.getModelInstance(instanceId);
        setInstance(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch model instance');
      } finally {
        setLoading(false);
      }
    };

    if (instanceId) {
      fetchInstance();
    }
  }, [instanceId]);

  const refetch = async () => {
    try {
      setError(null);
      const data = await modelManagementService.getModelInstance(instanceId);
      setInstance(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch model instance');
    }
  };

  return { instance, loading, error, refetch };
}

export function useDownloadProgress(taskId: string, pollInterval: number = 1000) {
  const [task, setTask] = useState<ModelDownloadTask | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!taskId) return;

    const fetchProgress = async () => {
      try {
        setError(null);
        const data = await modelManagementService.getDownloadProgress(taskId);
        setTask(data);
        setLoading(false);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch download progress');
        setLoading(false);
      }
    };

    // Initial fetch
    fetchProgress();

    // Set up polling if task is not completed
    const interval = setInterval(() => {
      if (task && (task.status === 'running' || task.status === 'pending')) {
        fetchProgress();
      }
    }, pollInterval);

    return () => clearInterval(interval);
  }, [taskId, pollInterval, task?.status]);

  const cancelDownload = async () => {
    try {
      await modelManagementService.cancelDownload(taskId);
      setTask(prev => prev ? { ...prev, status: 'cancelled' } : null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to cancel download');
    }
  };

  return { task, loading, error, cancelDownload };
}

export function useModelHealth(instanceId: string, pollInterval: number = 5000) {
  const [health, setHealth] = useState<ModelHealth | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!instanceId) return;

    const checkHealth = async () => {
      try {
        setError(null);
        const data = await modelManagementService.checkModelHealth(instanceId);
        setHealth(data);
        setLoading(false);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to check model health');
        setLoading(false);
      }
    };

    // Initial check
    checkHealth();

    // Set up polling
    const interval = setInterval(checkHealth, pollInterval);

    return () => clearInterval(interval);
  }, [instanceId, pollInterval]);

  return { health, loading, error };
}

// Utility functions
export function getModelTypeIcon(modelType: ModelType): string {
  const icons: Record<ModelType, string> = {
    [ModelType.LANGUAGE_MODEL]: '💬',
    [ModelType.EMBEDDING_MODEL]: '🔗',
    [ModelType.VISION_MODEL]: '👁️',
    [ModelType.AUDIO_MODEL]: '🎵',
    [ModelType.MULTIMODAL_MODEL]: '🎭',
    [ModelType.CODE_MODEL]: '💻',
    [ModelType.CUSTOM]: '🔧',
  };
  return icons[modelType] || '❓';
}

export function getProviderIcon(provider: ModelProvider): string {
  const icons: Record<ModelProvider, string> = {
    [ModelProvider.HUGGINGFACE]: '🤗',
    [ModelProvider.OLLAMA]: '🦙',
    [ModelProvider.OPENAI]: '🤖',
    [ModelProvider.ANTHROPIC]: '🧠',
    [ModelProvider.GOOGLE]: '🔍',
    [ModelProvider.CUSTOM]: '🔧',
  };
  return icons[provider] || '❓';
}

export function getStatusColor(status: ModelStatus): string {
  const colors: Record<ModelStatus, string> = {
    [ModelStatus.AVAILABLE]: 'gray',
    [ModelStatus.DOWNLOADING]: 'blue',
    [ModelStatus.DOWNLOADED]: 'green',
    [ModelStatus.LOADING]: 'yellow',
    [ModelStatus.LOADED]: 'green',
    [ModelStatus.SERVING]: 'green',
    [ModelStatus.ERROR]: 'red',
    [ModelStatus.STOPPED]: 'gray',
  };
  return colors[status] || 'gray';
}

export function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

export function formatSpeed(bytesPerSecond: number): string {
  return formatBytes(bytesPerSecond) + '/s';
}

export function estimateTimeRemaining(totalBytes: number, downloadedBytes: number, speed: number): string {
  if (speed <= 0) return 'Unknown';
  
  const remainingBytes = totalBytes - downloadedBytes;
  const remainingSeconds = remainingBytes / speed;
  
  if (remainingSeconds < 60) {
    return `${Math.round(remainingSeconds)}s`;
  } else if (remainingSeconds < 3600) {
    return `${Math.round(remainingSeconds / 60)}m`;
  } else {
    return `${Math.round(remainingSeconds / 3600)}h`;
  }
}
