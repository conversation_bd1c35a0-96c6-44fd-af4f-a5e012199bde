"""
Model Management Integration Tests

Integration tests for the complete model management system including
API endpoints, service layer, database operations, and WebSocket communication.
"""

import asyncio
import uuid
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from fastapi import FastAPI
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession

from src.domain.entities.model_management import ModelProvider, ModelStatus, ModelType
from src.domain.entities.user import Permission, User, UserRole, UserStatus
from src.domain.value_objects.email import Email
from src.domain.value_objects.username import Username
from src.presentation.api.routes.model_management import router


@pytest.fixture
def app():
    """Create FastAPI app with model management router."""
    app = FastAPI()
    app.include_router(router)
    return app


@pytest.fixture
def client(app):
    """Create test client."""
    return TestClient(app)


@pytest.fixture
def sample_user_id():
    """Sample user ID for testing."""
    return uuid.uuid4()


@pytest.fixture
def admin_user(sample_user_id):
    """Create admin user entity."""
    return User(
        id=str(sample_user_id),
        email=Email("<EMAIL>"),
        username=Username("admin"),
        full_name="Admin User",
        hashed_password="hashed_password",
        role=UserRole.ADMIN,
        status=UserStatus.ACTIVE,
        is_active=True,
        is_verified=True,
    )


@pytest.fixture
def mock_jwt_token():
    """Mock JWT token for testing."""
    return "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.test.token"


class TestModelManagementLifecycle:
    """Test complete model management lifecycle."""

    @patch("src.presentation.api.routes.model_management.get_model_management_service")
    @patch("src.infrastructure.database.repositories.user_repository.UserRepository")
    @patch("src.infrastructure.security.jwt.jwt_manager.verify_token")
    @patch("src.infrastructure.database.connection.get_db_session")
    def test_complete_model_lifecycle(
        self, mock_get_db, mock_verify_token, mock_user_repo_class, mock_get_service,
        client, admin_user, sample_user_id, mock_jwt_token
    ):
        """Test complete model management lifecycle: discover, download, serve, monitor."""
        
        # Setup authentication mocks
        mock_verify_token.return_value = {
            "user_id": str(sample_user_id),
            "sub": "<EMAIL>",
            "jti": "token-id"
        }
        
        mock_db = AsyncMock(spec=AsyncSession)
        mock_get_db.return_value.__aenter__.return_value = mock_db
        
        mock_user_repo = AsyncMock()
        mock_user_repo.get_by_id.return_value = admin_user
        mock_user_repo_class.return_value = mock_user_repo

        # Mock model management service
        mock_service = AsyncMock()
        mock_get_service.return_value = mock_service

        # Step 1: Discover HuggingFace models
        mock_hf_models = [
            MagicMock(
                id="microsoft/DialoGPT-medium",
                name="microsoft/DialoGPT-medium",
                display_name="DialoGPT Medium",
                description="Medium-sized conversational AI model",
                model_type=ModelType.LANGUAGE_MODEL,
                provider=ModelProvider.HUGGINGFACE,
                version="latest",
                size_bytes=**********,  # 1.5GB
                parameter_count=117000000,  # 117M parameters
                context_length=1024,
                min_ram_gb=4.0,
                gpu_required=False,
                capabilities=["text-generation", "conversation"],
                tags=["conversational", "chatbot", "microsoft"],
                license="MIT"
            )
        ]
        mock_service.discover_huggingface_models.return_value = mock_hf_models

        discover_response = client.get(
            "/models/discover/huggingface?query=DialoGPT&limit=10",
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        assert discover_response.status_code == 200
        discovered_models = discover_response.json()
        assert len(discovered_models) == 1
        assert discovered_models[0]["name"] == "microsoft/DialoGPT-medium"
        assert discovered_models[0]["model_type"] == "language_model"
        assert discovered_models[0]["provider"] == "huggingface"

        # Step 2: Discover Ollama models
        mock_ollama_models = [
            MagicMock(
                id="llama2:7b",
                name="llama2:7b",
                display_name="Llama 2 7B",
                description="Llama 2 7B parameter model",
                model_type=ModelType.LANGUAGE_MODEL,
                provider=ModelProvider.OLLAMA,
                version="7b",
                size_bytes=**********,  # 3.8GB
                parameter_count=**********,  # 7B parameters
                context_length=4096,
                min_ram_gb=8.0,
                gpu_required=True,
                capabilities=["text-generation", "instruction-following"],
                tags=["llama", "meta", "7b"],
                license="Custom"
            )
        ]
        mock_service.discover_ollama_models.return_value = mock_ollama_models

        ollama_response = client.get(
            "/models/discover/ollama",
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        assert ollama_response.status_code == 200
        ollama_models = ollama_response.json()
        assert len(ollama_models) == 1
        assert ollama_models[0]["name"] == "llama2:7b"
        assert ollama_models[0]["provider"] == "ollama"

        # Step 3: Search models across providers
        mock_search_results = mock_hf_models + mock_ollama_models
        mock_service.search_models.return_value = mock_search_results

        search_response = client.post(
            "/models/search",
            json={
                "query": "language model",
                "model_type": "language_model",
                "limit": 20
            },
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        assert search_response.status_code == 200
        search_results = search_response.json()
        assert len(search_results) == 2

        # Step 4: Create model instance
        instance_id = uuid.uuid4()
        mock_instance = MagicMock(
            id=instance_id,
            model_id="microsoft/DialoGPT-medium",
            status=ModelStatus.AVAILABLE,
            configuration={"max_length": 1000, "temperature": 0.7},
            created_by=sample_user_id
        )
        mock_service.create_model_instance.return_value = mock_instance

        create_instance_response = client.post(
            "/models/instances",
            json={
                "model_id": "microsoft/DialoGPT-medium",
                "configuration": {
                    "max_length": 1000,
                    "temperature": 0.7
                }
            },
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        assert create_instance_response.status_code == 201
        created_instance = create_instance_response.json()
        assert created_instance["model_id"] == "microsoft/DialoGPT-medium"
        assert created_instance["status"] == "available"

        # Step 5: Start model download
        download_task_id = uuid.uuid4()
        mock_download_task = MagicMock(
            id=download_task_id,
            instance_id=instance_id,
            model_id="microsoft/DialoGPT-medium",
            status="running",
            progress=0.0,
            download_url="https://huggingface.co/microsoft/DialoGPT-medium/resolve/main/pytorch_model.bin",
            destination_path="./models/microsoft/DialoGPT-medium",
            total_size=**********,
            downloaded_size=0,
            created_by=sample_user_id
        )
        mock_service.download_model.return_value = mock_download_task

        download_response = client.post(
            f"/models/instances/{instance_id}/download",
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        assert download_response.status_code == 200
        download_task = download_response.json()
        assert download_task["status"] == "running"
        assert download_task["model_id"] == "microsoft/DialoGPT-medium"

        # Step 6: Check download progress
        # Simulate progress updates
        mock_download_task.progress = 50.0
        mock_download_task.downloaded_size = 750000000
        mock_download_task.download_speed = 10000000  # 10MB/s
        mock_service.get_download_progress.return_value = mock_download_task

        progress_response = client.get(
            f"/models/downloads/{download_task_id}",
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        assert progress_response.status_code == 200
        progress_data = progress_response.json()
        assert progress_data["progress"] == 50.0
        assert progress_data["download_speed"] == 10000000

        # Step 7: Complete download and start serving
        mock_instance.status = ModelStatus.DOWNLOADED
        mock_instance.local_path = "./models/microsoft/DialoGPT-medium"
        mock_service.start_model_serving.return_value = True

        # Update instance to serving status
        mock_instance.status = ModelStatus.SERVING
        mock_instance.serving_port = 8000
        mock_instance.serving_url = "http://localhost:8000"
        mock_instance.api_endpoint = "http://localhost:8000/v1/completions"

        serve_response = client.post(
            f"/models/instances/{instance_id}/serve",
            json={"port": 8000},
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        assert serve_response.status_code == 200
        serve_data = serve_response.json()
        assert serve_data["success"] is True
        assert "serving_url" in serve_data

        # Step 8: Check model health
        mock_health = {
            "status": "healthy",
            "message": "Model is serving normally",
            "response_time_ms": "150",
            "memory_usage_mb": 2048.5,
            "requests_served": 125,
            "errors_count": 0
        }
        mock_service.check_model_health.return_value = mock_health

        health_response = client.get(
            f"/models/instances/{instance_id}/health",
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        assert health_response.status_code == 200
        health_data = health_response.json()
        assert health_data["status"] == "healthy"
        assert health_data["memory_usage_mb"] == 2048.5
        assert health_data["requests_served"] == 125

        # Step 9: Stop model serving
        mock_service.stop_model_serving.return_value = True

        stop_response = client.post(
            f"/models/instances/{instance_id}/stop",
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        assert stop_response.status_code == 200
        stop_data = stop_response.json()
        assert stop_data["success"] is True

        # Step 10: Cancel download (test cancellation)
        mock_service.cancel_download.return_value = True

        cancel_response = client.delete(
            f"/models/downloads/{download_task_id}",
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        assert cancel_response.status_code == 200
        cancel_data = cancel_response.json()
        assert cancel_data["success"] is True

        # Verify all service methods were called
        mock_service.discover_huggingface_models.assert_called()
        mock_service.discover_ollama_models.assert_called()
        mock_service.search_models.assert_called()
        mock_service.create_model_instance.assert_called()
        mock_service.download_model.assert_called()
        mock_service.get_download_progress.assert_called()
        mock_service.start_model_serving.assert_called()
        mock_service.check_model_health.assert_called()
        mock_service.stop_model_serving.assert_called()
        mock_service.cancel_download.assert_called()


class TestModelManagementValidation:
    """Test model management validation and error handling."""

    @patch("src.presentation.api.routes.model_management.get_model_management_service")
    @patch("src.infrastructure.database.repositories.user_repository.UserRepository")
    @patch("src.infrastructure.security.jwt.jwt_manager.verify_token")
    @patch("src.infrastructure.database.connection.get_db_session")
    def test_model_validation_errors(
        self, mock_get_db, mock_verify_token, mock_user_repo_class, mock_get_service,
        client, admin_user, sample_user_id, mock_jwt_token
    ):
        """Test model management validation and error handling."""
        
        # Setup mocks
        mock_verify_token.return_value = {
            "user_id": str(sample_user_id),
            "sub": "<EMAIL>",
            "jti": "token-id"
        }
        
        mock_db = AsyncMock(spec=AsyncSession)
        mock_get_db.return_value.__aenter__.return_value = mock_db
        
        mock_user_repo = AsyncMock()
        mock_user_repo.get_by_id.return_value = admin_user
        mock_user_repo_class.return_value = mock_user_repo

        mock_service = AsyncMock()
        mock_get_service.return_value = mock_service

        # Test invalid model search request
        invalid_search_data = {
            "query": "",  # Empty query should fail
            "limit": 1001  # Exceeds maximum limit
        }

        response = client.post(
            "/models/search",
            json=invalid_search_data,
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        assert response.status_code == 422  # Validation error

        # Test invalid model instance creation
        invalid_instance_data = {
            "model_id": "",  # Empty model ID should fail
        }

        response = client.post(
            "/models/instances",
            json=invalid_instance_data,
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        assert response.status_code == 422  # Validation error

        # Test invalid serving configuration
        invalid_serving_data = {
            "port": 70000,  # Port out of valid range
        }

        response = client.post(
            f"/models/instances/{uuid.uuid4()}/serve",
            json=invalid_serving_data,
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        assert response.status_code == 422  # Validation error


class TestModelManagementWebSocket:
    """Test WebSocket functionality for real-time updates."""

    @pytest.mark.asyncio
    async def test_websocket_download_progress(self):
        """Test WebSocket download progress updates."""
        from src.presentation.api.websockets.model_management import (
            websocket_manager,
            broadcast_download_progress_update
        )

        # Mock WebSocket connection
        mock_websocket = AsyncMock()
        user_id = "test-user"
        task_id = str(uuid.uuid4())

        # Connect WebSocket
        await websocket_manager.connect(mock_websocket, user_id)
        await websocket_manager.subscribe_to_download_task(mock_websocket, task_id)

        # Broadcast progress update
        progress_data = {
            "progress": 75.0,
            "downloaded_size": 750000000,
            "download_speed": 15000000,
            "status": "running"
        }

        await broadcast_download_progress_update(task_id, progress_data)

        # Verify WebSocket message was sent
        mock_websocket.send_text.assert_called()
        sent_message = mock_websocket.send_text.call_args[0][0]
        assert "download_progress" in sent_message
        assert task_id in sent_message

    @pytest.mark.asyncio
    async def test_websocket_instance_status(self):
        """Test WebSocket instance status updates."""
        from src.presentation.api.websockets.model_management import (
            websocket_manager,
            broadcast_instance_status_update
        )

        # Mock WebSocket connection
        mock_websocket = AsyncMock()
        user_id = "test-user"
        instance_id = str(uuid.uuid4())

        # Connect WebSocket
        await websocket_manager.connect(mock_websocket, user_id)
        await websocket_manager.subscribe_to_model_instance(mock_websocket, instance_id)

        # Broadcast status update
        status_data = {
            "status": "serving",
            "serving_port": 8000,
            "memory_usage_mb": 2048.5,
            "load_time_seconds": 15.2
        }

        await broadcast_instance_status_update(instance_id, status_data)

        # Verify WebSocket message was sent
        mock_websocket.send_text.assert_called()
        sent_message = mock_websocket.send_text.call_args[0][0]
        assert "instance_status" in sent_message
        assert instance_id in sent_message
