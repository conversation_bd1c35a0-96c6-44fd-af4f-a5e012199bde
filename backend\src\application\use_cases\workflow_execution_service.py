"""
Workflow Execution Service.

This module contains the business logic for executing workflows and managing
workflow execution lifecycle.
"""

import asyncio
import uuid
from datetime import UTC, datetime
from typing import Any

from src.domain.entities.user import User
from src.domain.entities.workflow import (
    ExecutionStatus,
    Workflow,
    WorkflowExecution,
    WorkflowNode,
)
from src.domain.events.workflow_events import (
    WorkflowExecutionCompletedEvent,
    WorkflowExecutionFailedEvent,
    WorkflowExecutionStartedEvent,
    WorkflowNodeExecutionEvent,
)
from src.domain.repositories.workflow_repository import WorkflowRepository
from src.infrastructure.logging.setup import get_logger

logger = get_logger(__name__)


class WorkflowExecutionService:
    """Service for executing workflows and managing execution lifecycle."""

    def __init__(
        self,
        workflow_repository: WorkflowRepository,
        event_publisher: Any,  # EventPublisher interface
    ):
        """
        Initialize workflow execution service.

        Args:
            workflow_repository: Workflow repository
            event_publisher: Event publisher for domain events
        """
        self.workflow_repository = workflow_repository
        self.event_publisher = event_publisher
        self.active_executions: dict[str, WorkflowExecution] = {}

    async def execute_workflow(
        self,
        workflow_id: uuid.UUID,
        user: User,
        input_data: dict[str, Any],
        execution_context: dict[str, Any] | None = None,
    ) -> WorkflowExecution:
        """
        Execute a workflow.

        Args:
            workflow_id: Workflow ID to execute
            user: User executing the workflow
            input_data: Input data for the workflow
            execution_context: Optional execution context

        Returns:
            WorkflowExecution entity

        Raises:
            ValueError: If workflow not found or invalid
            PermissionError: If user lacks execution permissions
            ExecutionError: If execution fails
        """
        # Get workflow
        workflow = await self.workflow_repository.get_by_id(workflow_id)
        if not workflow:
            raise ValueError(f"Workflow {workflow_id} not found")

        # Check permissions
        if not self._can_execute_workflow(workflow, user):
            raise PermissionError(
                f"User {user.id} cannot execute workflow {workflow_id}"
            )

        # Validate workflow
        validation_errors = await self._validate_workflow_for_execution(workflow)
        if validation_errors:
            raise ValueError(f"Workflow validation failed: {validation_errors}")

        # Create execution
        execution = WorkflowExecution(
            id=uuid.uuid4(),
            workflow_id=workflow_id,
            user_id=uuid.UUID(user.id),
            status=ExecutionStatus.RUNNING,
            input_data=input_data,
            execution_context=execution_context or {},
            started_at=datetime.now(UTC),
        )

        # Store execution
        execution = await self.workflow_repository.create_execution(execution)
        self.active_executions[str(execution.id)] = execution

        # Publish started event
        await self.event_publisher.publish(
            WorkflowExecutionStartedEvent(
                execution_id=execution.id,
                workflow_id=workflow_id,
                workflow_name=workflow.name,
                user_id=uuid.UUID(user.id),
                input_data=input_data,
            )
        )

        # Execute workflow asynchronously
        asyncio.create_task(self._execute_workflow_async(workflow, execution))

        logger.info(
            f"Started workflow execution {execution.id} for workflow {workflow_id}"
        )
        return execution

    async def _execute_workflow_async(
        self,
        workflow: Workflow,
        execution: WorkflowExecution,
    ) -> None:
        """
        Execute workflow asynchronously.

        Args:
            workflow: Workflow to execute
            execution: Execution entity
        """
        try:
            start_time = datetime.now(UTC)

            # Execute nodes in topological order
            execution_order = self._get_execution_order(workflow.nodes, workflow.edges)
            node_outputs: dict[str, Any] = {}

            for node_id in execution_order:
                node = next((n for n in workflow.nodes if n.id == node_id), None)
                if not node:
                    continue

                # Prepare node input
                node_input = self._prepare_node_input(
                    node, node_outputs, execution.input_data
                )

                # Execute node
                node_output = await self._execute_node(node, node_input, execution)
                node_outputs[node_id] = node_output

                # Update execution progress
                progress = len([n for n in execution_order if n in node_outputs]) / len(
                    execution_order
                )
                execution.progress = progress * 100
                execution.updated_at = datetime.now(UTC)
                await self.workflow_repository.update_execution(execution)

            # Complete execution
            end_time = datetime.now(UTC)
            execution_time = (end_time - start_time).total_seconds()

            execution.status = ExecutionStatus.COMPLETED
            execution.output_data = self._prepare_workflow_output(
                workflow, node_outputs
            )
            execution.completed_at = end_time
            execution.execution_time = execution_time
            execution.cost = self._calculate_execution_cost(workflow, execution_time)
            execution.updated_at = end_time

            await self.workflow_repository.update_execution(execution)

            # Publish completed event
            await self.event_publisher.publish(
                WorkflowExecutionCompletedEvent(
                    execution_id=execution.id,
                    workflow_id=workflow.id,
                    workflow_name=workflow.name,
                    user_id=execution.user_id,
                    output_data=execution.output_data,
                    execution_time=execution_time,
                    cost=execution.cost,
                )
            )

            logger.info(f"Completed workflow execution {execution.id}")

        except Exception as e:
            # Handle execution failure
            end_time = datetime.now(UTC)
            execution_time = (end_time - execution.started_at).total_seconds()

            execution.status = ExecutionStatus.FAILED
            execution.error_message = str(e)
            execution.error_details = {"exception_type": type(e).__name__}
            execution.completed_at = end_time
            execution.execution_time = execution_time
            execution.cost = self._calculate_execution_cost(workflow, execution_time)
            execution.updated_at = end_time

            await self.workflow_repository.update_execution(execution)

            # Publish failed event
            await self.event_publisher.publish(
                WorkflowExecutionFailedEvent(
                    execution_id=execution.id,
                    workflow_id=workflow.id,
                    workflow_name=workflow.name,
                    user_id=execution.user_id,
                    error_message=str(e),
                    error_details=execution.error_details,
                    failed_node_id=None,  # Could be enhanced to track failed node
                    execution_time=execution_time,
                    cost=execution.cost,
                )
            )

            logger.error(f"Failed workflow execution {execution.id}: {e}")

        finally:
            # Remove from active executions
            self.active_executions.pop(str(execution.id), None)

    async def _execute_node(
        self,
        node: WorkflowNode,
        input_data: dict[str, Any],
        execution: WorkflowExecution,
    ) -> dict[str, Any]:
        """
        Execute a single workflow node.

        Args:
            node: Node to execute
            input_data: Input data for the node
            execution: Current execution context

        Returns:
            Node output data

        Raises:
            NodeExecutionError: If node execution fails
        """
        start_time = datetime.now(UTC)

        try:
            # Execute based on node type
            if node.type == "agent":
                output = await self._execute_agent_node(node, input_data, execution)
            elif node.type == "model_instance":
                output = await self._execute_model_node(node, input_data, execution)
            elif node.type == "knowledge_entity":
                output = await self._execute_knowledge_node(node, input_data, execution)
            elif node.type == "chat_interface":
                output = await self._execute_chat_node(node, input_data, execution)
            elif node.type == "document_processor":
                output = await self._execute_document_node(node, input_data, execution)
            elif node.type == "vector_search":
                output = await self._execute_search_node(node, input_data, execution)
            elif node.type == "prompt_template":
                output = await self._execute_template_node(node, input_data, execution)
            elif node.type == "tool_executor":
                output = await self._execute_tool_node(node, input_data, execution)
            else:
                output = await self._execute_generic_node(node, input_data, execution)

            end_time = datetime.now(UTC)
            execution_time = (end_time - start_time).total_seconds()

            # Publish node execution event
            await self.event_publisher.publish(
                WorkflowNodeExecutionEvent(
                    execution_id=execution.id,
                    workflow_id=execution.workflow_id,
                    node_id=node.id,
                    node_type=node.type,
                    status="completed",
                    input_data=input_data,
                    output_data=output,
                    execution_time=execution_time,
                    error_message=None,
                )
            )

            return output

        except Exception as e:
            end_time = datetime.now(UTC)
            execution_time = (end_time - start_time).total_seconds()

            # Publish node execution event
            await self.event_publisher.publish(
                WorkflowNodeExecutionEvent(
                    execution_id=execution.id,
                    workflow_id=execution.workflow_id,
                    node_id=node.id,
                    node_type=node.type,
                    status="failed",
                    input_data=input_data,
                    output_data=None,
                    execution_time=execution_time,
                    error_message=str(e),
                )
            )

            raise

    async def _execute_agent_node(
        self,
        node: WorkflowNode,
        input_data: dict[str, Any],
        execution: WorkflowExecution,
    ) -> dict[str, Any]:
        """Execute an agent node."""
        try:
            # Get agent configuration
            agent_config = node.configuration
            agent_id = agent_config.get("agent_id")
            agent_name = agent_config.get("agent_name", "Unknown Agent")

            if not agent_id:
                raise ValueError("Agent node requires agent_id in configuration")

            # Prepare agent input
            agent_input = {
                "message": input_data.get("message", ""),
                "context": input_data.get("context", {}),
                "configuration": agent_config,
            }

            # Execute agent (would integrate with actual agent service)
            # For now, simulate agent execution
            import asyncio

            await asyncio.sleep(0.1)  # Simulate processing time

            agent_output = {
                "response": f"Agent {agent_name} processed: {agent_input['message']}",
                "agent_id": agent_id,
                "agent_name": agent_name,
                "confidence": 0.95,
                "metadata": {
                    "processing_time": 0.1,
                    "tokens_used": len(str(agent_input)) // 4,
                },
            }

            return {
                "result": agent_output,
                "status": "completed",
                "node_type": "agent",
            }

        except Exception as e:
            logger.error(f"Agent node execution failed: {e}")
            return {
                "result": None,
                "status": "failed",
                "error": str(e),
                "node_type": "agent",
            }

    async def _execute_model_node(
        self,
        node: WorkflowNode,
        input_data: dict[str, Any],
        execution: WorkflowExecution,
    ) -> dict[str, Any]:
        """Execute a model instance node."""
        # Implementation would integrate with model management service
        return {"result": "model_output", "status": "completed"}

    async def _execute_knowledge_node(
        self,
        node: WorkflowNode,
        input_data: dict[str, Any],
        execution: WorkflowExecution,
    ) -> dict[str, Any]:
        """Execute a knowledge entity node."""
        # Implementation would integrate with knowledge graph service
        return {"result": "knowledge_output", "status": "completed"}

    async def _execute_chat_node(
        self,
        node: WorkflowNode,
        input_data: dict[str, Any],
        execution: WorkflowExecution,
    ) -> dict[str, Any]:
        """Execute a chat interface node."""
        # Implementation would handle chat interface logic
        return {"result": "chat_output", "status": "completed"}

    async def _execute_document_node(
        self,
        node: WorkflowNode,
        input_data: dict[str, Any],
        execution: WorkflowExecution,
    ) -> dict[str, Any]:
        """Execute a document processor node."""
        # Implementation would handle document processing
        return {"result": "document_output", "status": "completed"}

    async def _execute_search_node(
        self,
        node: WorkflowNode,
        input_data: dict[str, Any],
        execution: WorkflowExecution,
    ) -> dict[str, Any]:
        """Execute a vector search node."""
        # Implementation would handle vector search
        return {"result": "search_output", "status": "completed"}

    async def _execute_template_node(
        self,
        node: WorkflowNode,
        input_data: dict[str, Any],
        execution: WorkflowExecution,
    ) -> dict[str, Any]:
        """Execute a prompt template node."""
        # Implementation would handle prompt templating
        return {"result": "template_output", "status": "completed"}

    async def _execute_tool_node(
        self,
        node: WorkflowNode,
        input_data: dict[str, Any],
        execution: WorkflowExecution,
    ) -> dict[str, Any]:
        """Execute a tool executor node."""
        # Implementation would handle tool execution
        return {"result": "tool_output", "status": "completed"}

    async def _execute_generic_node(
        self,
        node: WorkflowNode,
        input_data: dict[str, Any],
        execution: WorkflowExecution,
    ) -> dict[str, Any]:
        """Execute a generic node."""
        # Default implementation for unknown node types
        return {"result": "generic_output", "status": "completed"}

    def _can_execute_workflow(self, workflow: Workflow, user: User) -> bool:
        """Check if user can execute workflow."""
        # Check if user owns the workflow or it's public
        return str(workflow.created_by) == user.id or workflow.is_public

    async def _validate_workflow_for_execution(self, workflow: Workflow) -> list[str]:
        """Validate workflow for execution."""
        errors = []

        if not workflow.nodes:
            errors.append("Workflow has no nodes")

        # Check for input nodes
        input_nodes = [
            n for n in workflow.nodes if n.type in ["input", "chat_interface"]
        ]
        if not input_nodes:
            errors.append("Workflow must have at least one input node")

        # Check for output nodes
        output_nodes = [n for n in workflow.nodes if n.type in ["output", "agent"]]
        if not output_nodes:
            errors.append("Workflow must have at least one output node")

        return errors

    def _get_execution_order(
        self, nodes: list[WorkflowNode], edges: list[Any]
    ) -> list[str]:
        """Get topological execution order for nodes."""
        # Simple implementation - would need proper topological sort
        return [node.id for node in nodes]

    def _prepare_node_input(
        self,
        node: WorkflowNode,
        node_outputs: dict[str, Any],
        workflow_input: dict[str, Any],
    ) -> dict[str, Any]:
        """Prepare input data for a node."""
        # Combine workflow input with outputs from previous nodes
        return {**workflow_input, **node_outputs}

    def _prepare_workflow_output(
        self,
        workflow: Workflow,
        node_outputs: dict[str, Any],
    ) -> dict[str, Any]:
        """Prepare final workflow output."""
        # Extract outputs from output nodes
        output_nodes = [n for n in workflow.nodes if n.type in ["output", "agent"]]
        if output_nodes:
            return {node.id: node_outputs.get(node.id, {}) for node in output_nodes}
        return node_outputs

    def _calculate_execution_cost(
        self, workflow: Workflow, execution_time: float
    ) -> float:
        """Calculate execution cost."""
        # Simple cost calculation based on execution time and node count
        base_cost = 0.01  # Base cost per second
        node_cost = 0.001 * len(workflow.nodes)  # Cost per node
        return (base_cost * execution_time) + node_cost

    async def get_execution_status(
        self, execution_id: uuid.UUID
    ) -> WorkflowExecution | None:
        """Get execution status."""
        return await self.workflow_repository.get_execution_by_id(execution_id)

    async def cancel_execution(self, execution_id: uuid.UUID, user: User) -> bool:
        """Cancel a running execution."""
        execution = await self.workflow_repository.get_execution_by_id(execution_id)
        if not execution:
            return False

        if str(execution.user_id) != user.id:
            raise PermissionError("Cannot cancel execution owned by another user")

        if execution.status != ExecutionStatus.RUNNING:
            return False

        execution.status = ExecutionStatus.CANCELLED
        execution.completed_at = datetime.now(UTC)
        execution.updated_at = datetime.now(UTC)

        await self.workflow_repository.update_execution(execution)
        self.active_executions.pop(str(execution_id), None)

        logger.info(f"Cancelled workflow execution {execution_id}")
        return True
