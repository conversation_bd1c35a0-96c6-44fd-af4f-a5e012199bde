"""
Agent API schemas.

This module contains Pydantic schemas for agent API requests and responses.
"""

import uuid
from datetime import datetime
from typing import Any

from pydantic import BaseModel, Field

from src.domain.entities.agent import AgentStatus, AgentType


# Base schemas
class AgentCapabilitySchema(BaseModel):
    """Schema for agent capabilities."""

    name: str = Field(..., description="Capability name")
    description: str = Field(..., description="Capability description")
    parameters: dict[str, Any] = Field(
        default_factory=dict, description="Capability parameters"
    )
    required: bool = Field(default=False, description="Whether capability is required")


class AgentConfigurationSchema(BaseModel):
    """Schema for agent configuration."""

    model_name: str = Field(default="gpt-4", description="AI model name")
    temperature: float = Field(
        default=0.7, ge=0.0, le=2.0, description="Model temperature"
    )
    max_tokens: int = Field(default=2048, ge=1, le=8192, description="Maximum tokens")
    timeout_seconds: int = Field(default=300, ge=1, description="Timeout in seconds")
    retry_attempts: int = Field(default=3, ge=0, description="Number of retry attempts")
    memory_enabled: bool = Field(default=True, description="Enable conversation memory")
    tools_enabled: bool = Field(default=True, description="Enable tools")
    custom_settings: dict[str, Any] = Field(
        default_factory=dict, description="Custom settings"
    )


class AgentMetricsSchema(BaseModel):
    """Schema for agent metrics."""

    total_executions: int = Field(default=0, description="Total executions")
    successful_executions: int = Field(default=0, description="Successful executions")
    failed_executions: int = Field(default=0, description="Failed executions")
    average_execution_time: float = Field(
        default=0.0, description="Average execution time"
    )
    last_execution_time: datetime | None = Field(
        None, description="Last execution time"
    )
    total_tokens_used: int = Field(default=0, description="Total tokens used")
    total_cost: float = Field(default=0.0, description="Total cost")


# Request schemas
class AgentCreateRequest(BaseModel):
    """Schema for agent creation requests."""

    name: str = Field(..., min_length=1, max_length=255, description="Agent name")
    description: str = Field(..., max_length=1000, description="Agent description")
    agent_type: AgentType = Field(..., description="Agent type")
    version: str = Field(default="1.0.0", description="Agent version")
    configuration: AgentConfigurationSchema = Field(
        default_factory=AgentConfigurationSchema, description="Agent configuration"
    )
    capabilities: list[AgentCapabilitySchema] = Field(
        default_factory=list, description="Agent capabilities"
    )
    tags: list[str] = Field(default_factory=list, description="Agent tags")
    metadata: dict[str, Any] = Field(default_factory=dict, description="Agent metadata")
    is_public: bool = Field(default=False, description="Whether agent is public")


class AgentUpdateRequest(BaseModel):
    """Schema for agent update requests."""

    name: str | None = Field(
        None, min_length=1, max_length=255, description="Agent name"
    )
    description: str | None = Field(
        None, max_length=1000, description="Agent description"
    )
    agent_type: AgentType | None = Field(None, description="Agent type")
    version: str | None = Field(None, description="Agent version")
    configuration: AgentConfigurationSchema | None = Field(
        None, description="Agent configuration"
    )
    capabilities: list[AgentCapabilitySchema] | None = Field(
        None, description="Agent capabilities"
    )
    tags: list[str] | None = Field(None, description="Agent tags")
    metadata: dict[str, Any] | None = Field(None, description="Agent metadata")
    is_public: bool | None = Field(None, description="Whether agent is public")


class AgentExecuteRequest(BaseModel):
    """Schema for agent execution requests."""

    input_data: dict[str, Any] = Field(
        default_factory=dict, description="Input data for agent execution"
    )
    context: dict[str, Any] = Field(
        default_factory=dict, description="Execution context"
    )
    timeout_seconds: int | None = Field(None, description="Override timeout")


# Response schemas
class AgentResponse(BaseModel):
    """Schema for agent responses."""

    id: uuid.UUID = Field(..., description="Unique agent identifier")
    name: str = Field(..., description="Agent name")
    description: str = Field(..., description="Agent description")
    agent_type: AgentType = Field(..., description="Agent type")
    version: str = Field(..., description="Agent version")
    status: AgentStatus = Field(..., description="Agent status")
    current_task_id: uuid.UUID | None = Field(None, description="Current task ID")
    configuration: AgentConfigurationSchema = Field(
        ..., description="Agent configuration"
    )
    capabilities: list[AgentCapabilitySchema] = Field(
        ..., description="Agent capabilities"
    )
    metrics: AgentMetricsSchema = Field(..., description="Agent metrics")
    created_by: uuid.UUID = Field(..., description="Creator user ID")
    parent_agent_id: uuid.UUID | None = Field(None, description="Parent agent ID")
    child_agent_ids: list[uuid.UUID] = Field(..., description="Child agent IDs")
    tags: list[str] = Field(..., description="Agent tags")
    metadata: dict[str, Any] = Field(..., description="Agent metadata")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    class Config:
        """Pydantic configuration."""

        from_attributes = True


class AgentListResponse(BaseModel):
    """Schema for agent list responses."""

    agents: list[AgentResponse] = Field(..., description="List of agents")
    total: int = Field(..., description="Total number of agents")
    limit: int = Field(..., description="Limit used for pagination")
    offset: int = Field(..., description="Offset used for pagination")


class AgentExecutionResponse(BaseModel):
    """Schema for agent execution responses."""

    id: uuid.UUID = Field(..., description="Unique execution identifier")
    agent_id: uuid.UUID = Field(..., description="Associated agent ID")
    status: str = Field(..., description="Execution status")
    input_data: dict[str, Any] = Field(..., description="Execution input data")
    output_data: dict[str, Any] = Field(..., description="Execution output data")
    error_data: dict[str, Any] | None = Field(
        None, description="Error information if failed"
    )
    execution_time: float | None = Field(None, description="Execution time in seconds")
    tokens_used: int = Field(..., description="Tokens used")
    cost: float = Field(..., description="Execution cost")
    started_at: datetime | None = Field(None, description="Execution start timestamp")
    completed_at: datetime | None = Field(
        None, description="Execution completion timestamp"
    )
    created_at: datetime = Field(..., description="Creation timestamp")

    class Config:
        """Pydantic configuration."""

        from_attributes = True


class AgentMetricsSummaryResponse(BaseModel):
    """Schema for agent metrics summary responses."""

    total_agents: int = Field(..., description="Total number of agents")
    active_agents: int = Field(..., description="Number of active agents")
    total_executions: int = Field(..., description="Total executions across all agents")
    total_cost: float = Field(..., description="Total cost across all agents")
    total_tokens_used: int = Field(
        ..., description="Total tokens used across all agents"
    )
    average_success_rate: float = Field(
        ..., description="Average success rate percentage"
    )


# Query parameter schemas
class AgentListParams(BaseModel):
    """Schema for agent list query parameters."""

    status: AgentStatus | None = Field(None, description="Filter by agent status")
    agent_type: AgentType | None = Field(None, description="Filter by agent type")
    include_public: bool = Field(True, description="Include public agents")
    limit: int = Field(
        100, ge=1, le=1000, description="Maximum number of agents to return"
    )
    offset: int = Field(0, ge=0, description="Number of agents to skip")


class AgentSearchParams(BaseModel):
    """Schema for agent search query parameters."""

    query: str = Field(..., min_length=1, description="Search query string")
    include_public: bool = Field(True, description="Include public agents")
    limit: int = Field(
        100, ge=1, le=1000, description="Maximum number of agents to return"
    )
    offset: int = Field(0, ge=0, description="Number of agents to skip")


# Error response schemas
class AgentErrorResponse(BaseModel):
    """Schema for agent error responses."""

    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    details: dict[str, Any] | None = Field(None, description="Additional error details")


class AgentValidationErrorResponse(BaseModel):
    """Schema for agent validation error responses."""

    error: str = Field(default="validation_error", description="Error type")
    message: str = Field(..., description="Error message")
    validation_errors: list[str] = Field(..., description="List of validation errors")


# Success response schemas
class AgentDeleteResponse(BaseModel):
    """Schema for agent deletion responses."""

    success: bool = Field(True, description="Whether deletion was successful")
    message: str = Field(
        default="Agent deleted successfully", description="Success message"
    )


class AgentStatusResponse(BaseModel):
    """Schema for agent status responses."""

    id: uuid.UUID = Field(..., description="Agent ID")
    status: AgentStatus = Field(..., description="Current agent status")
    message: str = Field(..., description="Status message")
    updated_at: datetime = Field(..., description="Status update timestamp")


# Conversation schemas
class AgentConversationResponse(BaseModel):
    """Schema for agent conversation responses."""

    id: uuid.UUID = Field(..., description="Conversation ID")
    agent_id: uuid.UUID = Field(..., description="Agent ID")
    user_id: uuid.UUID = Field(..., description="User ID")
    title: str | None = Field(None, description="Conversation title")
    message_count: int = Field(..., description="Number of messages")
    total_tokens: int = Field(..., description="Total tokens used")
    total_cost: float = Field(..., description="Total cost")
    is_active: bool = Field(..., description="Whether conversation is active")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")


class AgentMessageResponse(BaseModel):
    """Schema for agent message responses."""

    id: uuid.UUID = Field(..., description="Message ID")
    conversation_id: uuid.UUID = Field(..., description="Conversation ID")
    role: str = Field(..., description="Message role")
    content: str = Field(..., description="Message content")
    metadata: dict[str, Any] = Field(..., description="Message metadata")
    tokens_used: int = Field(..., description="Tokens used")
    cost: float = Field(..., description="Message cost")
    response_time: float | None = Field(None, description="Response time")
    created_at: datetime = Field(..., description="Creation timestamp")


class AgentChatRequest(BaseModel):
    """Schema for agent chat requests."""

    message: str = Field(..., min_length=1, description="User message")
    conversation_id: uuid.UUID | None = Field(
        None, description="Existing conversation ID"
    )
    context: dict[str, Any] = Field(
        default_factory=dict, description="Additional context"
    )


class AgentChatResponse(BaseModel):
    """Schema for agent chat responses."""

    conversation_id: uuid.UUID = Field(..., description="Conversation ID")
    message_id: uuid.UUID = Field(..., description="Message ID")
    response: str = Field(..., description="Agent response")
    tokens_used: int = Field(..., description="Tokens used")
    cost: float = Field(..., description="Response cost")
    response_time: float = Field(..., description="Response time in seconds")
