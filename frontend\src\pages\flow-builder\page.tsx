'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Node, Edge } from 'reactflow';
import { Plus, FileText, Folder, Search, Filter, MoreHorizontal } from 'lucide-react';
import { EnhancedFlowBuilderWrapper } from '@/features/flow-builder/ui/enhanced-flow-builder';
import { Button } from '@/shared/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/ui/card';
import { Badge } from '@/shared/ui/badge';
import { Input } from '@/shared/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/shared/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/shared/ui/dialog';
import { useToast } from '@/shared/hooks/use-toast';

interface Workflow {
  id: string;
  name: string;
  description: string;
  nodes: Node[];
  edges: Edge[];
  status: 'draft' | 'active' | 'archived';
  category: string;
  tags: string[];
  createdAt: string;
  updatedAt: string;
  executionCount: number;
  lastExecuted?: string;
}

interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  complexity: 'simple' | 'moderate' | 'complex';
  nodes: Node[];
  edges: Edge[];
  tags: string[];
  isPopular?: boolean;
  isNew?: boolean;
}

// Mock workflow templates
const workflowTemplates: WorkflowTemplate[] = [
  {
    id: 'chat-assistant',
    name: 'AI Chat Assistant',
    description: 'A conversational AI assistant with knowledge base integration',
    category: 'conversational',
    complexity: 'simple',
    nodes: [
      {
        id: 'input-1',
        type: 'chat_interface',
        position: { x: 100, y: 100 },
        data: { interfaceType: 'text', contextLength: 4096 }
      },
      {
        id: 'agent-1',
        type: 'agent',
        position: { x: 400, y: 100 },
        data: { agentName: 'Chat Assistant', agentType: 'conversational', status: 'active' }
      },
      {
        id: 'model-1',
        type: 'model_instance',
        position: { x: 700, y: 100 },
        data: { modelName: 'GPT-3.5 Turbo', status: 'serving' }
      }
    ],
    edges: [
      { id: 'e1-2', source: 'input-1', target: 'agent-1', type: 'smoothstep' },
      { id: 'e2-3', source: 'agent-1', target: 'model-1', type: 'smoothstep' }
    ],
    tags: ['chat', 'assistant', 'conversational'],
    isPopular: true,
  },
  {
    id: 'document-qa',
    name: 'Document Q&A System',
    description: 'Question answering system with document processing and vector search',
    category: 'knowledge',
    complexity: 'moderate',
    nodes: [
      {
        id: 'doc-processor',
        type: 'document_processor',
        position: { x: 100, y: 100 },
        data: { processorType: 'pdf', outputFormat: 'json' }
      },
      {
        id: 'embedding-gen',
        type: 'embedding_generator',
        position: { x: 400, y: 100 },
        data: { modelName: 'text-embedding-ada-002', dimensions: 1536 }
      },
      {
        id: 'vector-search',
        type: 'vector_search',
        position: { x: 700, y: 100 },
        data: { searchType: 'similarity', topK: 5, threshold: 0.8 }
      },
      {
        id: 'qa-agent',
        type: 'agent',
        position: { x: 400, y: 300 },
        data: { agentName: 'QA Agent', agentType: 'knowledge_worker', status: 'active' }
      }
    ],
    edges: [
      { id: 'e1-2', source: 'doc-processor', target: 'embedding-gen', type: 'smoothstep' },
      { id: 'e2-3', source: 'embedding-gen', target: 'vector-search', type: 'smoothstep' },
      { id: 'e3-4', source: 'vector-search', target: 'qa-agent', type: 'smoothstep' }
    ],
    tags: ['document', 'qa', 'search', 'knowledge'],
    isNew: true,
  },
  {
    id: 'multi-agent-research',
    name: 'Multi-Agent Research System',
    description: 'Collaborative research system with multiple specialized agents',
    category: 'research',
    complexity: 'complex',
    nodes: [
      {
        id: 'research-coordinator',
        type: 'agent',
        position: { x: 400, y: 100 },
        data: { agentName: 'Research Coordinator', agentType: 'research_assistant', status: 'active' }
      },
      {
        id: 'data-collector',
        type: 'agent',
        position: { x: 200, y: 300 },
        data: { agentName: 'Data Collector', agentType: 'data_analyst', status: 'active' }
      },
      {
        id: 'analyzer',
        type: 'agent',
        position: { x: 600, y: 300 },
        data: { agentName: 'Analyzer', agentType: 'data_analyst', status: 'active' }
      },
      {
        id: 'knowledge-base',
        type: 'knowledge_entity',
        position: { x: 400, y: 500 },
        data: { entityName: 'Research Knowledge Base', entityType: 'database' }
      }
    ],
    edges: [
      { id: 'e1-2', source: 'research-coordinator', target: 'data-collector', type: 'smoothstep' },
      { id: 'e1-3', source: 'research-coordinator', target: 'analyzer', type: 'smoothstep' },
      { id: 'e2-4', source: 'data-collector', target: 'knowledge-base', type: 'smoothstep' },
      { id: 'e3-4', source: 'analyzer', target: 'knowledge-base', type: 'smoothstep' }
    ],
    tags: ['research', 'multi-agent', 'collaboration', 'analysis'],
  }
];

export default function FlowBuilderPage() {
  const [currentView, setCurrentView] = useState<'list' | 'builder'>('list');
  const [selectedWorkflow, setSelectedWorkflow] = useState<Workflow | null>(null);
  const [workflows, setWorkflows] = useState<Workflow[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showTemplateDialog, setShowTemplateDialog] = useState(false);
  const { toast } = useToast();

  // Mock workflows data
  useEffect(() => {
    // In a real app, this would fetch from an API
    const mockWorkflows: Workflow[] = [
      {
        id: '1',
        name: 'Customer Support Bot',
        description: 'Automated customer support with escalation to human agents',
        nodes: [],
        edges: [],
        status: 'active',
        category: 'customer-service',
        tags: ['support', 'automation', 'escalation'],
        createdAt: '2024-01-15T10:00:00Z',
        updatedAt: '2024-01-20T14:30:00Z',
        executionCount: 156,
        lastExecuted: '2024-01-20T14:30:00Z',
      },
      {
        id: '2',
        name: 'Content Generation Pipeline',
        description: 'Automated content creation and review workflow',
        nodes: [],
        edges: [],
        status: 'draft',
        category: 'content',
        tags: ['content', 'generation', 'review'],
        createdAt: '2024-01-18T09:15:00Z',
        updatedAt: '2024-01-19T16:45:00Z',
        executionCount: 23,
      }
    ];
    setWorkflows(mockWorkflows);
  }, []);

  const handleCreateWorkflow = (template?: WorkflowTemplate) => {
    const newWorkflow: Workflow = {
      id: Date.now().toString(),
      name: template ? `${template.name} Copy` : 'New Workflow',
      description: template?.description || 'A new workflow',
      nodes: template?.nodes || [],
      edges: template?.edges || [],
      status: 'draft',
      category: template?.category || 'general',
      tags: template?.tags || [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      executionCount: 0,
    };

    setSelectedWorkflow(newWorkflow);
    setCurrentView('builder');
    setShowTemplateDialog(false);
  };

  const handleSaveWorkflow = async (nodes: Node[], edges: Edge[]) => {
    if (!selectedWorkflow) return;

    const updatedWorkflow = {
      ...selectedWorkflow,
      nodes,
      edges,
      updatedAt: new Date().toISOString(),
    };

    // Update or add workflow
    setWorkflows(prev => {
      const existing = prev.find(w => w.id === updatedWorkflow.id);
      if (existing) {
        return prev.map(w => w.id === updatedWorkflow.id ? updatedWorkflow : w);
      } else {
        return [...prev, updatedWorkflow];
      }
    });

    setSelectedWorkflow(updatedWorkflow);
  };

  const handleExecuteWorkflow = async (nodes: Node[], edges: Edge[]) => {
    if (!selectedWorkflow) return;

    // Simulate workflow execution
    const updatedWorkflow = {
      ...selectedWorkflow,
      executionCount: selectedWorkflow.executionCount + 1,
      lastExecuted: new Date().toISOString(),
    };

    setWorkflows(prev => prev.map(w => w.id === updatedWorkflow.id ? updatedWorkflow : w));
    setSelectedWorkflow(updatedWorkflow);
  };

  const filteredWorkflows = workflows.filter(workflow => {
    const matchesSearch = workflow.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         workflow.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         workflow.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || workflow.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  const filteredTemplates = workflowTemplates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  if (currentView === 'builder' && selectedWorkflow) {
    return (
      <div className="h-screen">
        <EnhancedFlowBuilderWrapper
          workflowId={selectedWorkflow.id}
          initialNodes={selectedWorkflow.nodes}
          initialEdges={selectedWorkflow.edges}
          onSave={handleSaveWorkflow}
          onExecute={handleExecuteWorkflow}
        />
        
        {/* Back to List Button */}
        <div className="absolute top-4 left-4 z-50">
          <Button
            variant="outline"
            onClick={() => setCurrentView('list')}
            className="bg-background shadow-lg"
          >
            ← Back to Workflows
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-4xl font-bold tracking-tight">Flow Builder</h1>
          <p className="text-muted-foreground text-lg">
            Create and manage AI agent workflows with visual flow builder
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => setShowTemplateDialog(true)}>
            <FileText className="h-4 w-4 mr-2" />
            Templates
          </Button>
          <Button onClick={() => handleCreateWorkflow()}>
            <Plus className="h-4 w-4 mr-2" />
            New Workflow
          </Button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex items-center space-x-4">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search workflows..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <select
          value={selectedCategory}
          onChange={(e) => setSelectedCategory(e.target.value)}
          className="px-3 py-2 border rounded-md"
        >
          <option value="all">All Categories</option>
          <option value="conversational">Conversational</option>
          <option value="knowledge">Knowledge</option>
          <option value="research">Research</option>
          <option value="customer-service">Customer Service</option>
          <option value="content">Content</option>
        </select>
      </div>

      {/* Workflows Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {filteredWorkflows.map((workflow) => (
          <motion.div
            key={workflow.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.2 }}
          >
            <WorkflowCard
              workflow={workflow}
              onEdit={() => {
                setSelectedWorkflow(workflow);
                setCurrentView('builder');
              }}
            />
          </motion.div>
        ))}
      </div>

      {filteredWorkflows.length === 0 && (
        <div className="text-center py-12">
          <Folder className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No workflows found</h3>
          <p className="text-muted-foreground mb-4">
            {searchQuery ? 'Try adjusting your search criteria' : 'Create your first workflow to get started'}
          </p>
          <Button onClick={() => handleCreateWorkflow()}>
            <Plus className="h-4 w-4 mr-2" />
            Create Workflow
          </Button>
        </div>
      )}

      {/* Template Dialog */}
      <Dialog open={showTemplateDialog} onOpenChange={setShowTemplateDialog}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Workflow Templates</DialogTitle>
          </DialogHeader>
          
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {filteredTemplates.map((template) => (
              <TemplateCard
                key={template.id}
                template={template}
                onSelect={() => handleCreateWorkflow(template)}
              />
            ))}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

interface WorkflowCardProps {
  workflow: Workflow;
  onEdit: () => void;
}

function WorkflowCard({ workflow, onEdit }: WorkflowCardProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'green';
      case 'draft': return 'yellow';
      case 'archived': return 'gray';
      default: return 'gray';
    }
  };

  return (
    <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={onEdit}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">{workflow.name}</CardTitle>
          <Badge variant="outline" className={`text-${getStatusColor(workflow.status)}-600`}>
            {workflow.status}
          </Badge>
        </div>
        <p className="text-sm text-muted-foreground line-clamp-2">
          {workflow.description}
        </p>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-3">
          <div className="flex items-center justify-between text-sm">
            <span>Nodes: {workflow.nodes.length}</span>
            <span>Executions: {workflow.executionCount}</span>
          </div>
          
          <div className="flex flex-wrap gap-1">
            {workflow.tags.slice(0, 3).map((tag) => (
              <Badge key={tag} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
            {workflow.tags.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{workflow.tags.length - 3}
              </Badge>
            )}
          </div>
          
          <div className="text-xs text-muted-foreground">
            Updated {new Date(workflow.updatedAt).toLocaleDateString()}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

interface TemplateCardProps {
  template: WorkflowTemplate;
  onSelect: () => void;
}

function TemplateCard({ template, onSelect }: TemplateCardProps) {
  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'simple': return 'green';
      case 'moderate': return 'yellow';
      case 'complex': return 'red';
      default: return 'gray';
    }
  };

  return (
    <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={onSelect}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base">{template.name}</CardTitle>
          <div className="flex items-center space-x-1">
            <Badge variant="outline" className={`text-${getComplexityColor(template.complexity)}-600 text-xs`}>
              {template.complexity}
            </Badge>
            {template.isNew && <Badge variant="secondary" className="text-xs">New</Badge>}
            {template.isPopular && <Badge variant="secondary" className="text-xs">Popular</Badge>}
          </div>
        </div>
        <p className="text-sm text-muted-foreground line-clamp-2">
          {template.description}
        </p>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-2">
          <div className="text-sm">
            {template.nodes.length} nodes, {template.edges.length} connections
          </div>
          
          <div className="flex flex-wrap gap-1">
            {template.tags.slice(0, 3).map((tag) => (
              <Badge key={tag} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
            {template.tags.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{template.tags.length - 3}
              </Badge>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
