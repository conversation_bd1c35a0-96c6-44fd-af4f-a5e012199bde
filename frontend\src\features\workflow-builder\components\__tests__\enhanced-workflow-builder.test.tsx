/**
 * Enhanced Workflow Builder Tests
 *
 * Comprehensive test suite for the enhanced workflow builder with
 * improved drag-and-drop functionality and animation integration.
 */

import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { WorkflowBuilder } from '../workflow-builder';

// Mock ReactFlow
vi.mock('reactflow', () => ({
  ReactFlow: ({ children, ...props }: any) => (
    <div data-testid="react-flow" {...props}>
      {children}
    </div>
  ),
  ReactFlowProvider: ({ children }: any) => <div>{children}</div>,
  useReactFlow: () => ({
    screenToFlowPosition: ({ x, y }: { x: number; y: number }) => ({ x, y }),
    fitView: vi.fn(),
    zoomIn: vi.fn(),
    zoomOut: vi.fn(),
    getViewport: () => ({ x: 0, y: 0, zoom: 1 }),
    setViewport: vi.fn(),
  }),
  Background: () => <div data-testid="background" />,
  Controls: () => <div data-testid="controls" />,
  MiniMap: () => <div data-testid="minimap" />,
  Panel: ({ children }: any) => <div data-testid="panel">{children}</div>,
  Handle: () => <div data-testid="handle" />,
  Position: {
    Left: 'left',
    Right: 'right',
    Top: 'top',
    Bottom: 'bottom',
  },
  BackgroundVariant: {
    Dots: 'dots',
  },
  useNodesState: (initialNodes: any) => [
    initialNodes,
    vi.fn(),
    vi.fn(),
  ],
  useEdgesState: (initialEdges: any) => [
    initialEdges,
    vi.fn(),
    vi.fn(),
  ],
  addEdge: vi.fn(),
}));

// Mock ResizablePanel components
vi.mock('@/shared/ui/resizable', () => ({
  ResizablePanelGroup: ({ children }: any) => <div data-testid="resizable-panel-group">{children}</div>,
  ResizablePanel: ({ children }: any) => <div data-testid="resizable-panel">{children}</div>,
  ResizableHandle: () => <div data-testid="resizable-handle" />,
}));

// Mock toast
vi.mock('@/shared/ui/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn(),
  }),
}));

// Mock agent model
vi.mock('@/entities/agent/model', () => ({
  createAgent: vi.fn((data) => ({
    id: 'test-agent-id',
    name: data.name,
    description: data.description,
    agent_type: data.agent_type,
    status: data.status,
    capabilities: data.capabilities || [],
    metrics: {
      total_executions: 0,
      successful_executions: 0,
      failed_executions: 0,
      average_execution_time: 0,
    },
  })),
}));

describe('Enhanced Workflow Builder', () => {
  const mockProps = {
    onSave: vi.fn(),
    onExecute: vi.fn(),
    onNodesChange: vi.fn(),
    onEdgesChange: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders workflow builder with node library and canvas', () => {
    render(<WorkflowBuilder {...mockProps} />);

    expect(screen.getByTestId('resizable-panel-group')).toBeInTheDocument();
    expect(screen.getByText('Node Library')).toBeInTheDocument();
    expect(screen.getByTestId('react-flow')).toBeInTheDocument();
  });

  it('displays node templates in library', () => {
    render(<WorkflowBuilder {...mockProps} />);

    // Check for predefined node templates
    expect(screen.getByText('Chat Agent')).toBeInTheDocument();
    expect(screen.getByText('Reasoning Agent')).toBeInTheDocument();
    expect(screen.getByText('API Call')).toBeInTheDocument();
    expect(screen.getByText('Database Query')).toBeInTheDocument();
    expect(screen.getByText('Condition')).toBeInTheDocument();
  });

  it('filters nodes by search term', async () => {
    render(<WorkflowBuilder {...mockProps} />);

    const searchInput = screen.getByPlaceholderText('Search nodes...');
    fireEvent.change(searchInput, { target: { value: 'chat' } });

    await waitFor(() => {
      expect(screen.getByText('Chat Agent')).toBeInTheDocument();
      expect(screen.queryByText('Database Query')).not.toBeInTheDocument();
    });
  });

  it('filters nodes by category', async () => {
    render(<WorkflowBuilder {...mockProps} />);

    const agentsTab = screen.getByText('Agents');
    fireEvent.click(agentsTab);

    await waitFor(() => {
      expect(screen.getByText('Chat Agent')).toBeInTheDocument();
      expect(screen.getByText('Reasoning Agent')).toBeInTheDocument();
      expect(screen.queryByText('API Call')).not.toBeInTheDocument();
    });
  });

  it('handles drag start with visual feedback', () => {
    render(<WorkflowBuilder {...mockProps} />);

    const chatAgentNode = screen.getByText('Chat Agent').closest('div[draggable="true"]');
    expect(chatAgentNode).toBeInTheDocument();

    // Mock drag event with proper dataTransfer
    const mockDataTransfer = {
      effectAllowed: '',
      setData: vi.fn(),
      getData: vi.fn(),
      dropEffect: 'move',
    };

    const dragEvent = new Event('dragstart', { bubbles: true });
    Object.defineProperty(dragEvent, 'dataTransfer', {
      value: mockDataTransfer,
      writable: false,
    });

    fireEvent(chatAgentNode!, dragEvent);

    // Visual feedback should be applied via CSS classes or inline styles
    expect(chatAgentNode).toHaveClass('hover:scale-105');
  });

  it('handles drag end to reset visual feedback', () => {
    render(<WorkflowBuilder {...mockProps} />);

    const chatAgentNode = screen.getByText('Chat Agent').closest('div[draggable="true"]');
    expect(chatAgentNode).toBeInTheDocument();

    // Mock drag events with proper dataTransfer
    const mockDataTransfer = {
      effectAllowed: '',
      setData: vi.fn(),
      getData: vi.fn(),
      dropEffect: 'move',
    };

    const dragStartEvent = new Event('dragstart', { bubbles: true });
    Object.defineProperty(dragStartEvent, 'dataTransfer', {
      value: mockDataTransfer,
      writable: false,
    });
    fireEvent(chatAgentNode!, dragStartEvent);

    // Simulate drag end
    const dragEndEvent = new Event('dragend', { bubbles: true });
    fireEvent(chatAgentNode!, dragEndEvent);

    // Node should maintain its draggable state
    expect(chatAgentNode).toHaveAttribute('draggable', 'true');
  });

  it('provides visual feedback on canvas drag over', () => {
    render(<WorkflowBuilder {...mockProps} />);

    const canvas = screen.getByTestId('react-flow');

    const mockDataTransfer = {
      dropEffect: 'move',
      effectAllowed: 'move',
    };

    const dragOverEvent = new Event('dragover', { bubbles: true });
    Object.defineProperty(dragOverEvent, 'dataTransfer', {
      value: mockDataTransfer,
      writable: false,
    });

    fireEvent(canvas, dragOverEvent);

    // Canvas should be present and handle drag over
    expect(canvas).toBeInTheDocument();
  });

  it('resets canvas visual feedback on drag leave', () => {
    render(<WorkflowBuilder {...mockProps} />);

    const canvas = screen.getByTestId('react-flow');

    // Simulate drag over
    const mockDataTransfer = {
      dropEffect: 'move',
      effectAllowed: 'move',
    };

    const dragOverEvent = new Event('dragover', { bubbles: true });
    Object.defineProperty(dragOverEvent, 'dataTransfer', {
      value: mockDataTransfer,
      writable: false,
    });
    fireEvent(canvas, dragOverEvent);

    // Simulate drag leave
    const dragLeaveEvent = new Event('dragleave', { bubbles: true });
    fireEvent(canvas, dragLeaveEvent);

    // Canvas should still be present
    expect(canvas).toBeInTheDocument();
  });

  it('toggles node library collapse state', () => {
    render(<WorkflowBuilder {...mockProps} />);

    const collapseButton = screen.getByLabelText(/arrow/i) || screen.getByRole('button', { name: /collapse/i });

    if (collapseButton) {
      fireEvent.click(collapseButton);
      // Library should be collapsed - specific assertions would depend on implementation
    }
  });

  it('handles workflow execution with validation', () => {
    render(<WorkflowBuilder {...mockProps} />);

    // Try to execute empty workflow
    const executeButton = screen.queryByText('Execute');
    if (executeButton) {
      fireEvent.click(executeButton);
      // Should show validation error for empty workflow
    }
  });

  it('maintains accessibility for drag and drop operations', () => {
    render(<WorkflowBuilder {...mockProps} />);

    const draggableNodes = screen.getAllByRole('button', { name: /drag/i }) ||
                          screen.getAllByText(/Chat Agent|API Call|Condition/);

    draggableNodes.forEach(node => {
      expect(node.closest('[draggable="true"]')).toBeInTheDocument();
    });
  });

  it('supports keyboard navigation for accessibility', () => {
    render(<WorkflowBuilder {...mockProps} />);

    const searchInput = screen.getByPlaceholderText('Search nodes...');

    // Test keyboard navigation
    fireEvent.keyDown(searchInput, { key: 'Tab' });
    fireEvent.keyDown(searchInput, { key: 'Enter' });

    // Should maintain focus management
    expect(document.activeElement).toBeDefined();
  });
});
