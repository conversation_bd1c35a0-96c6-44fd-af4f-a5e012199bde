/**
 * Enhanced Workflow Builder Storybook Stories
 * 
 * Interactive documentation and testing for the enhanced workflow builder
 * with improved drag-and-drop functionality and animation integration.
 */

import type { Meta, StoryObj } from '@storybook/react';
import { action } from '@storybook/addon-actions';
import { WorkflowBuilder } from '../features/workflow-builder/components/workflow-builder';
import { WorkflowNode, WorkflowEdge, NodeType, WorkflowStatus } from '../features/workflow-builder/types';
import { AgentType, AgentStatus } from '../shared/types';

// Mock ReactFlow for Storybook
const mockReactFlow = {
  ReactFlow: ({ children, ...props }: any) => (
    <div 
      style={{ 
        width: '100%', 
        height: '600px', 
        border: '2px dashed #e2e8f0',
        borderRadius: '8px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f8fafc'
      }}
      {...props}
    >
      <div style={{ textAlign: 'center', color: '#64748b' }}>
        <h3>ReactFlow Canvas</h3>
        <p>Drag nodes from the library to create workflows</p>
        {children}
      </div>
    </div>
  ),
  ReactFlowProvider: ({ children }: any) => <div>{children}</div>,
  useReactFlow: () => ({
    screenToFlowPosition: ({ x, y }: { x: number; y: number }) => ({ x, y }),
    fitView: action('fitView'),
    zoomIn: action('zoomIn'),
    zoomOut: action('zoomOut'),
    getViewport: () => ({ x: 0, y: 0, zoom: 1 }),
    setViewport: action('setViewport'),
  }),
  Background: () => <div style={{ position: 'absolute', inset: 0, opacity: 0.1 }} />,
  Controls: () => (
    <div style={{ position: 'absolute', bottom: 10, right: 10, background: 'white', padding: 8, borderRadius: 4 }}>
      Controls
    </div>
  ),
  MiniMap: () => (
    <div style={{ position: 'absolute', bottom: 10, left: 10, background: 'white', padding: 8, borderRadius: 4 }}>
      MiniMap
    </div>
  ),
  Panel: ({ children, position }: any) => (
    <div style={{ 
      position: 'absolute', 
      top: position?.includes('top') ? 10 : 'auto',
      bottom: position?.includes('bottom') ? 10 : 'auto',
      left: position?.includes('left') ? 10 : 'auto',
      right: position?.includes('right') ? 10 : 'auto',
      background: 'white', 
      padding: 8, 
      borderRadius: 4,
      boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
    }}>
      {children}
    </div>
  ),
  Handle: () => <div style={{ width: 8, height: 8, background: '#3b82f6', borderRadius: '50%' }} />,
  Position: {
    Left: 'left',
    Right: 'right',
    Top: 'top',
    Bottom: 'bottom',
  },
  BackgroundVariant: {
    Dots: 'dots',
  },
  useNodesState: (initialNodes: any) => [
    initialNodes,
    action('setNodes'),
    action('onNodesChange'),
  ],
  useEdgesState: (initialEdges: any) => [
    initialEdges,
    action('setEdges'),
    action('onEdgesChange'),
  ],
  addEdge: action('addEdge'),
};

// Apply mock
Object.assign(window, { ReactFlow: mockReactFlow });

const meta: Meta<typeof WorkflowBuilder> = {
  title: 'Features/Enhanced Workflow Builder',
  component: WorkflowBuilder,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: `
# Enhanced Workflow Builder

An advanced drag-and-drop workflow builder with improved animations and user experience.

## Features

- **Drag-and-Drop Interface**: Intuitive node library with visual feedback
- **Enhanced Animations**: Smooth transitions using our Enhanced Animation Presets
- **Real-time Validation**: Instant feedback on workflow structure
- **Accessibility**: WCAG 2.1 AA compliant with keyboard navigation
- **Performance**: Optimized for 100+ nodes with 60fps animations

## Usage

1. **Browse Node Library**: Search and filter available nodes
2. **Drag to Canvas**: Drag nodes from library to create workflows
3. **Connect Nodes**: Click and drag between node handles to create connections
4. **Configure**: Double-click nodes to configure their settings
5. **Execute**: Run your workflow with real-time status updates

## Node Types

- **Agent Nodes**: AI agents for various tasks (Chat, Reasoning, Multimodal)
- **Tool Nodes**: External integrations (API calls, Database queries)
- **Logic Nodes**: Conditional branching and flow control
- **Data Nodes**: Input/Output handling and data transformation
        `,
      },
    },
  },
  argTypes: {
    readonly: {
      control: 'boolean',
      description: 'Whether the workflow is read-only',
    },
    workflowId: {
      control: 'text',
      description: 'Unique identifier for the workflow',
    },
  },
};

export default meta;
type Story = StoryObj<typeof WorkflowBuilder>;

// Sample workflow data
const sampleNodes: WorkflowNode[] = [
  {
    id: 'node-1',
    type: NodeType.AGENT,
    position: { x: 100, y: 100 },
    data: {
      agent: {
        id: 'agent-1',
        name: 'Chat Agent',
        description: 'Handles customer conversations',
        agent_type: AgentType.CHAT,
        status: AgentStatus.IDLE,
        capabilities: [
          { name: 'chat', description: 'Text conversation', parameters: {}, required: true },
          { name: 'sentiment', description: 'Sentiment analysis', parameters: {}, required: false },
        ],
        metrics: {
          total_executions: 150,
          successful_executions: 142,
          failed_executions: 8,
          average_execution_time: 2.3,
        },
      },
    },
  },
  {
    id: 'node-2',
    type: NodeType.TOOL,
    position: { x: 400, y: 100 },
    data: {
      toolType: 'api',
      toolName: 'Customer API',
      description: 'Fetch customer information',
    },
  },
  {
    id: 'node-3',
    type: NodeType.CONDITION,
    position: { x: 250, y: 250 },
    data: {
      conditionType: 'simple',
      condition: 'sentiment === "positive"',
      description: 'Check if sentiment is positive',
    },
  },
];

const sampleEdges: WorkflowEdge[] = [
  {
    id: 'edge-1',
    source: 'node-1',
    target: 'node-2',
    type: 'smoothstep',
    animated: false,
  },
  {
    id: 'edge-2',
    source: 'node-2',
    target: 'node-3',
    type: 'smoothstep',
    animated: false,
  },
];

export const Default: Story = {
  args: {
    onSave: action('onSave'),
    onExecute: action('onExecute'),
    onNodesChange: action('onNodesChange'),
    onEdgesChange: action('onEdgesChange'),
  },
};

export const WithSampleWorkflow: Story = {
  args: {
    initialNodes: sampleNodes,
    initialEdges: sampleEdges,
    workflowId: 'sample-workflow',
    onSave: action('onSave'),
    onExecute: action('onExecute'),
    onNodesChange: action('onNodesChange'),
    onEdgesChange: action('onEdgesChange'),
  },
};

export const ReadOnlyMode: Story = {
  args: {
    initialNodes: sampleNodes,
    initialEdges: sampleEdges,
    readonly: true,
    workflowId: 'readonly-workflow',
    onSave: action('onSave'),
    onExecute: action('onExecute'),
    onNodesChange: action('onNodesChange'),
    onEdgesChange: action('onEdgesChange'),
  },
};

export const EmptyWorkflow: Story = {
  args: {
    workflowId: 'empty-workflow',
    onSave: action('onSave'),
    onExecute: action('onExecute'),
    onNodesChange: action('onNodesChange'),
    onEdgesChange: action('onEdgesChange'),
  },
};

export const InteractiveDemo: Story = {
  args: {
    workflowId: 'interactive-demo',
    onSave: action('onSave'),
    onExecute: action('onExecute'),
    onNodesChange: action('onNodesChange'),
    onEdgesChange: action('onEdgesChange'),
  },
  parameters: {
    docs: {
      description: {
        story: `
### Interactive Demo

Try the following interactions:

1. **Search Nodes**: Use the search bar to find specific node types
2. **Filter by Category**: Switch between Agents, Tools, and Logic tabs
3. **Drag and Drop**: Drag any node from the library to the canvas
4. **Visual Feedback**: Notice the hover effects and drag animations
5. **Collapse Library**: Use the arrow button to collapse the node library

The workflow builder provides real-time visual feedback and maintains accessibility standards.
        `,
      },
    },
  },
};
