"""
Workflow database models.

This module contains SQLAlchemy models for workflow-related entities.
"""

import uuid
from datetime import UTC, datetime
from typing import Any, Dict, List

from sqlalchemy import <PERSON>SO<PERSON>, <PERSON>olean, DateTime, Enum, Float, Integer, String, Text, UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from src.domain.entities.workflow import (
    ExecutionStatus,
    Workflow,
    WorkflowExecution,
    WorkflowStatus,
)
from src.infrastructure.database.models.base import Base


class WorkflowModel(Base):
    """SQLAlchemy model for workflows."""
    
    __tablename__ = "workflows"
    
    # Primary key
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), 
        primary_key=True, 
        default=uuid.uuid4
    )
    
    # Basic information
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[str] = mapped_column(Text, nullable=True)
    version: Mapped[str] = mapped_column(String(50), nullable=False, default="1.0.0")
    
    # Structure (stored as JSON)
    nodes: Mapped[List[Dict[str, Any]]] = mapped_column(JSON, nullable=False, default=list)
    edges: Mapped[List[Dict[str, Any]]] = mapped_column(JSON, nullable=False, default=list)
    
    # Configuration
    status: Mapped[WorkflowStatus] = mapped_column(
        Enum(WorkflowStatus), 
        nullable=False, 
        default=WorkflowStatus.DRAFT
    )
    is_public: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)
    
    # Ownership
    owner_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), nullable=False)
    
    # Metadata (stored as JSON)
    metadata: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=False, default=dict)
    performance: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=False, default=dict)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        nullable=False, 
        default=lambda: datetime.now(UTC)
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        nullable=False, 
        default=lambda: datetime.now(UTC),
        onupdate=lambda: datetime.now(UTC)
    )
    
    # Relationships
    executions: Mapped[List["WorkflowExecutionModel"]] = relationship(
        "WorkflowExecutionModel",
        back_populates="workflow",
        cascade="all, delete-orphan"
    )
    
    @classmethod
    def from_domain_entity(cls, workflow: Workflow) -> "WorkflowModel":
        """Create a WorkflowModel from a Workflow domain entity."""
        return cls(
            id=workflow.id,
            name=workflow.name,
            description=workflow.description,
            version=workflow.version,
            nodes=[node.model_dump() for node in workflow.nodes],
            edges=[edge.model_dump() for edge in workflow.edges],
            status=workflow.status,
            is_public=workflow.is_public,
            owner_id=workflow.owner_id,
            metadata=workflow.metadata.model_dump(),
            performance=workflow.performance.model_dump(),
            created_at=workflow.created_at,
            updated_at=workflow.updated_at,
        )
    
    def to_domain_entity(self) -> Workflow:
        """Convert the WorkflowModel to a Workflow domain entity."""
        from src.domain.entities.workflow import (
            WorkflowEdge,
            WorkflowMetadata,
            WorkflowNode,
            WorkflowPerformance,
        )
        
        # Convert nodes and edges from JSON to domain objects
        nodes = [WorkflowNode(**node_data) for node_data in self.nodes]
        edges = [WorkflowEdge(**edge_data) for edge_data in self.edges]
        
        # Convert metadata and performance from JSON to domain objects
        metadata = WorkflowMetadata(**self.metadata) if self.metadata else WorkflowMetadata()
        performance = WorkflowPerformance(**self.performance) if self.performance else WorkflowPerformance()
        
        return Workflow(
            id=self.id,
            name=self.name,
            description=self.description,
            version=self.version,
            nodes=nodes,
            edges=edges,
            status=self.status,
            is_public=self.is_public,
            owner_id=self.owner_id,
            metadata=metadata,
            performance=performance,
            created_at=self.created_at,
            updated_at=self.updated_at,
        )
    
    def update_from_domain_entity(self, workflow: Workflow) -> None:
        """Update the model from a domain entity."""
        self.name = workflow.name
        self.description = workflow.description
        self.version = workflow.version
        self.nodes = [node.model_dump() for node in workflow.nodes]
        self.edges = [edge.model_dump() for edge in workflow.edges]
        self.status = workflow.status
        self.is_public = workflow.is_public
        self.metadata = workflow.metadata.model_dump()
        self.performance = workflow.performance.model_dump()
        self.updated_at = workflow.updated_at


class WorkflowExecutionModel(Base):
    """SQLAlchemy model for workflow executions."""
    
    __tablename__ = "workflow_executions"
    
    # Primary key
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), 
        primary_key=True, 
        default=uuid.uuid4
    )
    
    # Foreign keys
    workflow_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), 
        nullable=False
    )
    user_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), nullable=False)
    
    # Execution status
    status: Mapped[ExecutionStatus] = mapped_column(
        Enum(ExecutionStatus), 
        nullable=False, 
        default=ExecutionStatus.PENDING
    )
    
    # Execution data (stored as JSON)
    input_data: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=False, default=dict)
    output_data: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=False, default=dict)
    error_data: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=True)
    
    # Progress tracking
    current_node_id: Mapped[str] = mapped_column(String(255), nullable=True)
    completed_nodes: Mapped[List[str]] = mapped_column(JSON, nullable=False, default=list)
    execution_log: Mapped[List[Dict[str, Any]]] = mapped_column(JSON, nullable=False, default=list)
    
    # Metrics
    execution_time: Mapped[float] = mapped_column(Float, nullable=True)
    cost: Mapped[float] = mapped_column(Float, nullable=False, default=0.0)
    
    # Timestamps
    started_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=True)
    completed_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=True)
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        nullable=False, 
        default=lambda: datetime.now(UTC)
    )
    
    # Relationships
    workflow: Mapped["WorkflowModel"] = relationship(
        "WorkflowModel",
        back_populates="executions"
    )
    
    @classmethod
    def from_domain_entity(cls, execution: WorkflowExecution) -> "WorkflowExecutionModel":
        """Create a WorkflowExecutionModel from a WorkflowExecution domain entity."""
        return cls(
            id=execution.id,
            workflow_id=execution.workflow_id,
            user_id=execution.user_id,
            status=execution.status,
            input_data=execution.input_data,
            output_data=execution.output_data,
            error_data=execution.error_data,
            current_node_id=execution.current_node_id,
            completed_nodes=execution.completed_nodes,
            execution_log=execution.execution_log,
            execution_time=execution.execution_time,
            cost=execution.cost,
            started_at=execution.started_at,
            completed_at=execution.completed_at,
            created_at=execution.created_at,
        )
    
    def to_domain_entity(self) -> WorkflowExecution:
        """Convert the WorkflowExecutionModel to a WorkflowExecution domain entity."""
        return WorkflowExecution(
            id=self.id,
            workflow_id=self.workflow_id,
            user_id=self.user_id,
            status=self.status,
            input_data=self.input_data,
            output_data=self.output_data,
            error_data=self.error_data,
            current_node_id=self.current_node_id,
            completed_nodes=self.completed_nodes,
            execution_log=self.execution_log,
            execution_time=self.execution_time,
            cost=self.cost,
            started_at=self.started_at,
            completed_at=self.completed_at,
            created_at=self.created_at,
        )
    
    def update_from_domain_entity(self, execution: WorkflowExecution) -> None:
        """Update the model from a domain entity."""
        self.status = execution.status
        self.input_data = execution.input_data
        self.output_data = execution.output_data
        self.error_data = execution.error_data
        self.current_node_id = execution.current_node_id
        self.completed_nodes = execution.completed_nodes
        self.execution_log = execution.execution_log
        self.execution_time = execution.execution_time
        self.cost = execution.cost
        self.started_at = execution.started_at
        self.completed_at = execution.completed_at
