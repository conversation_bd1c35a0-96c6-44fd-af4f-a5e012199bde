'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Search, Download, Activity, Database, BarChart3, Settings } from 'lucide-react';
import { ModelDiscovery } from '@/features/model-management/ui/model-discovery';
import { DownloadProgress, useDownloadTasks } from '@/features/model-management/ui/download-progress';
import { ModelServing } from '@/features/model-management/ui/model-serving';
import { modelManagementService } from '@/entities/model-management/api';
import { ModelInfo, ModelInstance, ModelDownloadTask } from '@/shared/types';
import { Button } from '@/shared/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/shared/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/ui/card';
import { useToast } from '@/shared/hooks/use-toast';

type ViewMode = 'discovery' | 'downloads' | 'serving' | 'analytics';

export default function ModelManagementPage() {
  const [viewMode, setViewMode] = useState<ViewMode>('discovery');
  const [instances, setInstances] = useState<ModelInstance[]>([]);
  const { toast } = useToast();

  // Mock user ID - in real app this would come from auth context
  const userId = 'current-user-id';
  
  const { tasks, addTask, updateTask } = useDownloadTasks(userId);

  const handleModelDownload = async (model: ModelInfo) => {
    try {
      // Create model instance first
      const instance = await modelManagementService.createModelInstance({
        model_id: model.id,
        configuration: {}
      });

      // Start download
      const downloadTask = await modelManagementService.downloadModel(instance.id);
      
      // Add to local state
      setInstances(prev => [...prev, instance]);
      addTask(downloadTask);

      toast({
        title: 'Download Started',
        description: `Started downloading ${model.display_name}`,
      });

      // Switch to downloads tab
      setViewMode('downloads');

    } catch (error) {
      toast({
        title: 'Download Failed',
        description: `Failed to start download: ${error}`,
        variant: 'destructive',
      });
    }
  };

  const handleStartServing = async (instanceId: string, config?: any) => {
    try {
      await modelManagementService.startModelServing(instanceId, config || {});
      
      // Update instance status
      setInstances(prev => prev.map(instance => 
        instance.id === instanceId 
          ? { ...instance, status: 'serving' as any }
          : instance
      ));

      toast({
        title: 'Serving Started',
        description: 'Model is now serving requests',
      });

    } catch (error) {
      toast({
        title: 'Serving Failed',
        description: `Failed to start serving: ${error}`,
        variant: 'destructive',
      });
    }
  };

  const handleStopServing = async (instanceId: string) => {
    try {
      await modelManagementService.stopModelServing(instanceId);
      
      // Update instance status
      setInstances(prev => prev.map(instance => 
        instance.id === instanceId 
          ? { ...instance, status: 'stopped' as any }
          : instance
      ));

      toast({
        title: 'Serving Stopped',
        description: 'Model serving has been stopped',
      });

    } catch (error) {
      toast({
        title: 'Stop Failed',
        description: `Failed to stop serving: ${error}`,
        variant: 'destructive',
      });
    }
  };

  const handleCancelDownload = async (taskId: string) => {
    try {
      await modelManagementService.cancelDownload(taskId);
      
      toast({
        title: 'Download Cancelled',
        description: 'Download has been cancelled',
      });

    } catch (error) {
      toast({
        title: 'Cancel Failed',
        description: `Failed to cancel download: ${error}`,
        variant: 'destructive',
      });
    }
  };

  // Calculate analytics
  const analytics = {
    totalInstances: instances.length,
    servingInstances: instances.filter(i => i.status === 'serving').length,
    downloadingInstances: instances.filter(i => i.status === 'downloading').length,
    activeTasks: tasks.filter(t => t.status === 'running').length,
    totalMemoryUsage: instances
      .filter(i => i.status === 'serving')
      .reduce((sum, i) => sum + (i.memory_usage_mb || 0), 0),
    totalGpuMemoryUsage: instances
      .filter(i => i.status === 'serving')
      .reduce((sum, i) => sum + (i.gpu_memory_usage_mb || 0), 0),
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-4xl font-bold tracking-tight">Model Management</h1>
          <p className="text-muted-foreground text-lg">
            Discover, download, and serve AI models locally
          </p>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Model Instances</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.totalInstances}</div>
            <p className="text-xs text-muted-foreground">
              {analytics.servingInstances} serving
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Downloads</CardTitle>
            <Download className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.activeTasks}</div>
            <p className="text-xs text-muted-foreground">
              {analytics.downloadingInstances} downloading
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Memory Usage</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {(analytics.totalMemoryUsage / 1024).toFixed(1)} GB
            </div>
            <p className="text-xs text-muted-foreground">
              RAM usage
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">GPU Memory</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {(analytics.totalGpuMemoryUsage / 1024).toFixed(1)} GB
            </div>
            <p className="text-xs text-muted-foreground">
              GPU usage
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Navigation Tabs */}
      <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as ViewMode)}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="discovery" className="flex items-center gap-2">
            <Search className="h-4 w-4" />
            Discovery
          </TabsTrigger>
          <TabsTrigger value="downloads" className="flex items-center gap-2">
            <Download className="h-4 w-4" />
            Downloads ({tasks.length})
          </TabsTrigger>
          <TabsTrigger value="serving" className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            Serving ({analytics.servingInstances})
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Analytics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="discovery" className="mt-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <ModelDiscovery
              onDownloadModel={handleModelDownload}
              className="h-full"
            />
          </motion.div>
        </TabsContent>

        <TabsContent value="downloads" className="mt-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <DownloadProgress
              tasks={tasks}
              onCancelDownload={handleCancelDownload}
              onRetryDownload={(taskId) => {
                // Implement retry logic
                toast({
                  title: 'Retry Download',
                  description: 'Download retry functionality coming soon',
                });
              }}
            />
          </motion.div>
        </TabsContent>

        <TabsContent value="serving" className="mt-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <ModelServing
              instances={instances}
              onStartServing={handleStartServing}
              onStopServing={handleStopServing}
            />
          </motion.div>
        </TabsContent>

        <TabsContent value="analytics" className="mt-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <ModelAnalytics analytics={analytics} instances={instances} tasks={tasks} />
          </motion.div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

interface ModelAnalyticsProps {
  analytics: {
    totalInstances: number;
    servingInstances: number;
    downloadingInstances: number;
    activeTasks: number;
    totalMemoryUsage: number;
    totalGpuMemoryUsage: number;
  };
  instances: ModelInstance[];
  tasks: ModelDownloadTask[];
}

function ModelAnalytics({ analytics, instances, tasks }: ModelAnalyticsProps) {
  const statusCounts = instances.reduce((acc, instance) => {
    acc[instance.status] = (acc[instance.status] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const taskStatusCounts = tasks.reduce((acc, task) => {
    acc[task.status] = (acc[task.status] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return (
    <div className="space-y-6">
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Instance Status Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(statusCounts).map(([status, count]) => (
                <div key={status} className="flex items-center justify-between">
                  <span className="text-sm font-medium capitalize">
                    {status.replace('_', ' ')}
                  </span>
                  <div className="flex items-center space-x-2">
                    <div className="w-24 bg-muted rounded-full h-2">
                      <div 
                        className="bg-primary h-2 rounded-full" 
                        style={{ width: `${(count / analytics.totalInstances) * 100}%` }}
                      />
                    </div>
                    <span className="text-sm text-muted-foreground">{count}</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Download Task Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(taskStatusCounts).map(([status, count]) => (
                <div key={status} className="flex items-center justify-between">
                  <span className="text-sm font-medium capitalize">
                    {status.replace('_', ' ')}
                  </span>
                  <div className="flex items-center space-x-2">
                    <div className="w-24 bg-muted rounded-full h-2">
                      <div 
                        className="bg-primary h-2 rounded-full" 
                        style={{ width: `${(count / tasks.length) * 100}%` }}
                      />
                    </div>
                    <span className="text-sm text-muted-foreground">{count}</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Resource Usage</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex justify-between">
              <span>Total Instances</span>
              <span className="font-medium">{analytics.totalInstances}</span>
            </div>
            <div className="flex justify-between">
              <span>Serving Instances</span>
              <span className="font-medium">{analytics.servingInstances}</span>
            </div>
            <div className="flex justify-between">
              <span>Memory Usage</span>
              <span className="font-medium">{(analytics.totalMemoryUsage / 1024).toFixed(1)} GB</span>
            </div>
            <div className="flex justify-between">
              <span>GPU Memory Usage</span>
              <span className="font-medium">{(analytics.totalGpuMemoryUsage / 1024).toFixed(1)} GB</span>
            </div>
            <div className="flex justify-between">
              <span>Active Downloads</span>
              <span className="font-medium">{analytics.activeTasks}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            Activity tracking coming soon...
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
