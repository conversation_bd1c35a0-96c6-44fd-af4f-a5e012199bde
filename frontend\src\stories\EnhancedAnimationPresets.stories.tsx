import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react';
import { 
  EnhancedAnimationPresets,
  AGENT_ANIMATION_PRESETS,
  FLOW_BUILDER_ANIMATION_PRESETS,
  WORKFLOW_ANIMATION_PRESETS,
  getAnimationPresetsByCategory
} from '@/shared/ui/enhanced-animation-presets';
import { AgentStatusIndicator } from '@/shared/ui/agent-status';
import { AgentStatus } from '@/shared/types/agent';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/ui/card';
import { Button } from '@/shared/ui/button';
import { Badge } from '@/shared/ui/badge';
import React, { useState } from 'react';

const meta: Meta<typeof EnhancedAnimationPresets> = {
  title: 'Shared/UI/EnhancedAnimationPresets',
  component: EnhancedAnimationPresets,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
Enhanced Animation Presets component provides comprehensive animation support for AI agents and workflow builders.

## Features
- **Type-safe animation presets** for different contexts (agent, flow, workflow)
- **Multiple trigger modes** (mount, hover, click, focus, manual)
- **Performance optimized** with 60fps target using Anime.js
- **Accessibility support** with reduced motion detection
- **Comprehensive callback system** for animation lifecycle events

## Animation Categories
- **Agent Animations**: Status changes, thinking, processing, success/error states
- **Flow Builder Animations**: Node enter/exit, hover effects, selection states
- **Workflow Animations**: Start, complete, error states
- **Status Indicators**: Pulsing and state change animations
- **Model Operations**: Loading and processing states
- **Card Interactions**: Hover and selection effects
        `,
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    animationPreset: {
      control: 'select',
      options: [
        ...AGENT_ANIMATION_PRESETS,
        ...FLOW_BUILDER_ANIMATION_PRESETS,
        ...WORKFLOW_ANIMATION_PRESETS,
        'statusIndicatorPulse',
        'modelLoading',
        'cardHoverAgent',
      ],
      description: 'The animation preset to use',
    },
    trigger: {
      control: 'select',
      options: ['mount', 'hover', 'click', 'focus', 'manual'],
      description: 'When to trigger the animation',
    },
    loop: {
      control: 'boolean',
      description: 'Whether the animation should loop',
    },
    autoplay: {
      control: 'boolean',
      description: 'Whether the animation should autoplay',
    },
    disabled: {
      control: 'boolean',
      description: 'Whether animations are disabled',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Default: Story = {
  args: {
    animationPreset: 'agentThinking',
    trigger: 'mount',
    loop: true,
    autoplay: true,
    disabled: false,
  },
  render: (args) => (
    <EnhancedAnimationPresets {...args}>
      <Card className="w-64">
        <CardHeader>
          <CardTitle>AI Agent</CardTitle>
        </CardHeader>
        <CardContent>
          <AgentStatusIndicator status={AgentStatus.RUNNING} />
          <p className="mt-2 text-sm text-muted-foreground">
            This agent is currently thinking...
          </p>
        </CardContent>
      </Card>
    </EnhancedAnimationPresets>
  ),
};

// Agent animations showcase
export const AgentAnimations: Story = {
  render: () => {
    const [selectedAnimation, setSelectedAnimation] = useState<string>('agentThinking');
    
    return (
      <div className="space-y-6">
        <div className="text-center">
          <h3 className="text-lg font-semibold mb-4">Agent Animation Presets</h3>
          <div className="flex flex-wrap gap-2 justify-center mb-6">
            {AGENT_ANIMATION_PRESETS.map((preset) => (
              <Button
                key={preset}
                variant={selectedAnimation === preset ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedAnimation(preset)}
              >
                {preset}
              </Button>
            ))}
          </div>
        </div>
        
        <div className="flex justify-center">
          <EnhancedAnimationPresets
            key={selectedAnimation} // Force re-mount for animation
            animationPreset={selectedAnimation as any}
            trigger="mount"
            loop={selectedAnimation.includes('thinking') || selectedAnimation.includes('processing')}
          >
            <Card className="w-80">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  AI Agent
                  <Badge variant="secondary">{selectedAnimation}</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <AgentStatusIndicator 
                  status={
                    selectedAnimation.includes('error') ? AgentStatus.FAILED :
                    selectedAnimation.includes('success') ? AgentStatus.COMPLETED :
                    selectedAnimation.includes('thinking') ? AgentStatus.RUNNING :
                    AgentStatus.IDLE
                  } 
                />
                <p className="mt-2 text-sm text-muted-foreground">
                  Demonstrating {selectedAnimation} animation
                </p>
              </CardContent>
            </Card>
          </EnhancedAnimationPresets>
        </div>
      </div>
    );
  },
};

// Flow builder animations showcase
export const FlowBuilderAnimations: Story = {
  render: () => {
    const [selectedAnimation, setSelectedAnimation] = useState<string>('nodeEnter');
    
    return (
      <div className="space-y-6">
        <div className="text-center">
          <h3 className="text-lg font-semibold mb-4">Flow Builder Animation Presets</h3>
          <div className="flex flex-wrap gap-2 justify-center mb-6">
            {FLOW_BUILDER_ANIMATION_PRESETS.map((preset) => (
              <Button
                key={preset}
                variant={selectedAnimation === preset ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedAnimation(preset)}
              >
                {preset}
              </Button>
            ))}
          </div>
        </div>
        
        <div className="flex justify-center">
          <EnhancedAnimationPresets
            key={selectedAnimation} // Force re-mount for animation
            animationPreset={selectedAnimation as any}
            trigger="mount"
            loop={selectedAnimation.includes('pulse')}
          >
            <div className="w-32 h-32 bg-primary rounded-lg flex items-center justify-center text-primary-foreground font-semibold shadow-lg">
              Flow Node
            </div>
          </EnhancedAnimationPresets>
        </div>
        
        <div className="text-center text-sm text-muted-foreground">
          <p>Animation: <strong>{selectedAnimation}</strong></p>
          <p>Perfect for drag-and-drop workflow builders</p>
        </div>
      </div>
    );
  },
};

// Workflow animations showcase
export const WorkflowAnimations: Story = {
  render: () => {
    const [selectedAnimation, setSelectedAnimation] = useState<string>('workflowStart');
    
    return (
      <div className="space-y-6">
        <div className="text-center">
          <h3 className="text-lg font-semibold mb-4">Workflow Animation Presets</h3>
          <div className="flex flex-wrap gap-2 justify-center mb-6">
            {WORKFLOW_ANIMATION_PRESETS.map((preset) => (
              <Button
                key={preset}
                variant={selectedAnimation === preset ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedAnimation(preset)}
              >
                {preset}
              </Button>
            ))}
          </div>
        </div>
        
        <div className="flex justify-center">
          <EnhancedAnimationPresets
            key={selectedAnimation} // Force re-mount for animation
            animationPreset={selectedAnimation as any}
            trigger="mount"
          >
            <Card className="w-96">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  Workflow Execution
                  <Badge 
                    variant={
                      selectedAnimation.includes('error') ? 'destructive' :
                      selectedAnimation.includes('complete') ? 'default' :
                      'secondary'
                    }
                  >
                    {selectedAnimation.replace('workflow', '')}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm">Step 1: Data Processing</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-sm">Step 2: AI Analysis</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                    <span className="text-sm">Step 3: Result Generation</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </EnhancedAnimationPresets>
        </div>
      </div>
    );
  },
};

// Interactive triggers showcase
export const InteractiveTriggers: Story = {
  render: () => (
    <div className="space-y-8">
      <div className="text-center">
        <h3 className="text-lg font-semibold mb-4">Interactive Animation Triggers</h3>
        <p className="text-sm text-muted-foreground mb-6">
          Try different interaction methods to trigger animations
        </p>
      </div>
      
      <div className="grid grid-cols-2 gap-6">
        <div className="text-center space-y-4">
          <h4 className="font-medium">Hover Trigger</h4>
          <EnhancedAnimationPresets
            animationPreset="nodeHover"
            trigger="hover"
          >
            <Card className="w-48 cursor-pointer transition-colors hover:bg-muted/50">
              <CardContent className="p-6">
                <p className="text-sm">Hover over me!</p>
              </CardContent>
            </Card>
          </EnhancedAnimationPresets>
        </div>
        
        <div className="text-center space-y-4">
          <h4 className="font-medium">Click Trigger</h4>
          <EnhancedAnimationPresets
            animationPreset="agentSuccess"
            trigger="click"
          >
            <Button size="lg">
              Click me for success animation!
            </Button>
          </EnhancedAnimationPresets>
        </div>
        
        <div className="text-center space-y-4">
          <h4 className="font-medium">Focus Trigger</h4>
          <EnhancedAnimationPresets
            animationPreset="nodeSelect"
            trigger="focus"
          >
            <Button variant="outline" size="lg">
              Tab to focus me!
            </Button>
          </EnhancedAnimationPresets>
        </div>
        
        <div className="text-center space-y-4">
          <h4 className="font-medium">Mount Trigger</h4>
          <EnhancedAnimationPresets
            animationPreset="workflowStart"
            trigger="mount"
          >
            <Badge variant="secondary" className="text-sm px-4 py-2">
              Animates on mount
            </Badge>
          </EnhancedAnimationPresets>
        </div>
      </div>
    </div>
  ),
};

// Performance showcase
export const PerformanceShowcase: Story = {
  render: () => {
    const [isVisible, setIsVisible] = useState(true);
    
    return (
      <div className="space-y-6">
        <div className="text-center">
          <h3 className="text-lg font-semibold mb-4">Performance & Accessibility</h3>
          <p className="text-sm text-muted-foreground mb-6">
            Optimized for 60fps with reduced motion support
          </p>
          <Button onClick={() => setIsVisible(!isVisible)}>
            {isVisible ? 'Hide' : 'Show'} Multiple Animations
          </Button>
        </div>
        
        {isVisible && (
          <div className="grid grid-cols-3 gap-4">
            {Array.from({ length: 9 }, (_, i) => (
              <EnhancedAnimationPresets
                key={i}
                animationPreset={
                  i % 3 === 0 ? 'agentThinking' :
                  i % 3 === 1 ? 'statusIndicatorPulse' :
                  'modelLoading'
                }
                trigger="mount"
                loop={true}
                animationConfig={{ delay: i * 100 }} // Stagger the animations
              >
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white text-xs font-bold">
                  {i + 1}
                </div>
              </EnhancedAnimationPresets>
            ))}
          </div>
        )}
        
        <div className="text-center text-xs text-muted-foreground">
          <p>Multiple simultaneous animations with staggered delays</p>
          <p>Respects user's motion preferences automatically</p>
        </div>
      </div>
    );
  },
};
