"""
Agent API routes.

This module contains FastAPI routes for agent management operations.
"""

import uuid

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from src.application.use_cases.agent_service import AgentService
from src.application.use_cases.mcp_service import MCPService
from src.domain.entities.agent import Agent, AgentStatus, AgentType
from src.domain.entities.user import Permission
from src.infrastructure.database.connection import get_db_session
from src.infrastructure.database.repositories.agent_repository_impl import (
    AgentRepositoryImpl,
)
from src.presentation.dependencies.auth import require_permissions
from src.presentation.schemas.agent_schemas import (
    AgentChatRequest,
    AgentChatResponse,
    AgentCreateRequest,
    AgentDeleteResponse,
    AgentErrorResponse,
    AgentListResponse,
    AgentMetricsSummaryResponse,
    AgentResponse,
    AgentStatusResponse,
    AgentUpdateRequest,
    AgentValidationErrorResponse,
)

router = APIRouter(prefix="/agents", tags=["agents"])


async def get_agent_service(
    db: AsyncSession = Depends(get_db_session),
) -> AgentService:
    """Get agent service instance."""
    agent_repo = AgentRepositoryImpl(db)
    mcp_service = MCPService()  # TODO: Inject properly
    return AgentService(agent_repo, mcp_service)


@router.post(
    "/",
    response_model=AgentResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new agent",
    description="Create a new AI agent with the provided configuration.",
    responses={
        201: {"description": "Agent created successfully"},
        400: {"model": AgentValidationErrorResponse, "description": "Validation error"},
        401: {"description": "Authentication required"},
        403: {"model": AgentErrorResponse, "description": "Permission denied"},
    },
)
async def create_agent(
    request: AgentCreateRequest,
    current_user: dict = Depends(require_permissions(Permission.AGENT_CREATE.value)),
    agent_service: AgentService = Depends(get_agent_service),
) -> AgentResponse:
    """Create a new agent."""
    try:
        # Convert request to domain entity
        from src.domain.entities.agent import (
            AgentCapability,
            AgentConfiguration,
            AgentMetrics,
        )

        # Convert configuration
        configuration = AgentConfiguration(
            model_name=request.configuration.model_name,
            temperature=request.configuration.temperature,
            max_tokens=request.configuration.max_tokens,
            timeout_seconds=request.configuration.timeout_seconds,
            retry_attempts=request.configuration.retry_attempts,
            memory_enabled=request.configuration.memory_enabled,
            tools_enabled=request.configuration.tools_enabled,
            custom_settings=request.configuration.custom_settings,
        )

        # Convert capabilities
        capabilities = [
            AgentCapability(
                name=cap.name,
                description=cap.description,
                parameters=cap.parameters,
                required=cap.required,
            )
            for cap in request.capabilities
        ]

        # Set metadata for public access
        metadata = request.metadata.copy()
        metadata["is_public"] = request.is_public

        agent = Agent(
            name=request.name,
            description=request.description,
            agent_type=request.agent_type,
            version=request.version,
            configuration=configuration,
            capabilities=capabilities,
            created_by=uuid.UUID(
                "00000000-0000-0000-0000-000000000000"
            ),  # Will be set by service
            tags=request.tags,
            metadata=metadata,
            metrics=AgentMetrics(),
        )

        # Get user entity from current_user
        user_entity = current_user["user_entity"]

        # Create agent
        created_agent = await agent_service.create_agent(agent, user_entity)

        # Convert to response schema
        return AgentResponse.model_validate(created_agent.to_dict())

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={"error": "validation_error", "message": str(e)},
        )
    except PermissionError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail={"error": "permission_denied", "message": str(e)},
        )


@router.get(
    "/",
    response_model=AgentListResponse,
    summary="List agents",
    description="Get a list of agents accessible to the current user.",
    responses={
        200: {"description": "Agents retrieved successfully"},
        401: {"description": "Authentication required"},
        403: {"model": AgentErrorResponse, "description": "Permission denied"},
    },
)
async def list_agents(
    agent_status: str = Query(None, description="Filter by agent status"),
    agent_type: str = Query(None, description="Filter by agent type"),
    include_public: bool = Query(True, description="Include public agents"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of agents"),
    offset: int = Query(0, ge=0, description="Number of agents to skip"),
    current_user: dict = Depends(require_permissions(Permission.AGENT_READ.value)),
    agent_service: AgentService = Depends(get_agent_service),
) -> AgentListResponse:
    """List agents for the current user."""
    try:
        # Get user entity from current_user
        user_entity = current_user["user_entity"]

        # Convert status string to enum if provided
        status_filter = None
        if agent_status:
            try:
                status_filter = AgentStatus(agent_status)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail={
                        "error": "invalid_status",
                        "message": f"Invalid status: {agent_status}",
                    },
                )

        # Convert type string to enum if provided
        type_filter = None
        if agent_type:
            try:
                type_filter = AgentType(agent_type)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail={
                        "error": "invalid_type",
                        "message": f"Invalid type: {agent_type}",
                    },
                )

        # Get agents
        agents = await agent_service.list_agents(
            user_entity, status_filter, type_filter, include_public, limit, offset
        )

        # Convert to response schemas
        agent_responses = [
            AgentResponse.model_validate(agent.to_dict()) for agent in agents
        ]

        return AgentListResponse(
            agents=agent_responses,
            total=len(agent_responses),  # TODO: Get actual total count
            limit=limit,
            offset=offset,
        )

    except PermissionError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail={"error": "permission_denied", "message": str(e)},
        )


@router.get(
    "/{agent_id}",
    response_model=AgentResponse,
    summary="Get an agent",
    description="Get a specific agent by ID.",
    responses={
        200: {"description": "Agent retrieved successfully"},
        401: {"description": "Authentication required"},
        403: {"model": AgentErrorResponse, "description": "Permission denied"},
        404: {"model": AgentErrorResponse, "description": "Agent not found"},
    },
)
async def get_agent(
    agent_id: uuid.UUID,
    current_user: dict = Depends(require_permissions(Permission.AGENT_READ.value)),
    agent_service: AgentService = Depends(get_agent_service),
) -> AgentResponse:
    """Get a specific agent by ID."""
    try:
        # Get user entity from current_user
        user_entity = current_user["user_entity"]

        # Get agent
        agent = await agent_service.get_agent(agent_id, user_entity)

        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={"error": "not_found", "message": "Agent not found"},
            )

        # Convert to response schema
        return AgentResponse.model_validate(agent.to_dict())

    except PermissionError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail={"error": "permission_denied", "message": str(e)},
        )


@router.put(
    "/{agent_id}",
    response_model=AgentResponse,
    summary="Update an agent",
    description="Update an existing agent.",
    responses={
        200: {"description": "Agent updated successfully"},
        400: {"model": AgentValidationErrorResponse, "description": "Validation error"},
        401: {"description": "Authentication required"},
        403: {"model": AgentErrorResponse, "description": "Permission denied"},
        404: {"model": AgentErrorResponse, "description": "Agent not found"},
    },
)
async def update_agent(
    agent_id: uuid.UUID,
    request: AgentUpdateRequest,
    current_user: dict = Depends(require_permissions(Permission.AGENT_UPDATE.value)),
    agent_service: AgentService = Depends(get_agent_service),
) -> AgentResponse:
    """Update an existing agent."""
    try:
        # Get user entity from current_user
        user_entity = current_user["user_entity"]

        # Get existing agent
        existing_agent = await agent_service.get_agent(agent_id, user_entity)
        if not existing_agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={"error": "not_found", "message": "Agent not found"},
            )

        # Update agent fields
        if request.name is not None:
            existing_agent.name = request.name
        if request.description is not None:
            existing_agent.description = request.description
        if request.agent_type is not None:
            existing_agent.agent_type = request.agent_type
        if request.version is not None:
            existing_agent.version = request.version
        if request.tags is not None:
            existing_agent.tags = request.tags

        # Update configuration if provided
        if request.configuration is not None:
            existing_agent.configuration.model_name = request.configuration.model_name
            existing_agent.configuration.temperature = request.configuration.temperature
            existing_agent.configuration.max_tokens = request.configuration.max_tokens
            existing_agent.configuration.timeout_seconds = (
                request.configuration.timeout_seconds
            )
            existing_agent.configuration.retry_attempts = (
                request.configuration.retry_attempts
            )
            existing_agent.configuration.memory_enabled = (
                request.configuration.memory_enabled
            )
            existing_agent.configuration.tools_enabled = (
                request.configuration.tools_enabled
            )
            existing_agent.configuration.custom_settings = (
                request.configuration.custom_settings
            )

        # Update capabilities if provided
        if request.capabilities is not None:
            from src.domain.entities.agent import AgentCapability

            existing_agent.capabilities = [
                AgentCapability(
                    name=cap.name,
                    description=cap.description,
                    parameters=cap.parameters,
                    required=cap.required,
                )
                for cap in request.capabilities
            ]

        # Update metadata if provided
        if request.metadata is not None:
            existing_agent.metadata.update(request.metadata)

        if request.is_public is not None:
            existing_agent.metadata["is_public"] = request.is_public

        # Update agent
        updated_agent = await agent_service.update_agent(existing_agent, user_entity)

        # Convert to response schema
        return AgentResponse.model_validate(updated_agent.to_dict())

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={"error": "validation_error", "message": str(e)},
        )
    except PermissionError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail={"error": "permission_denied", "message": str(e)},
        )


@router.delete(
    "/{agent_id}",
    response_model=AgentDeleteResponse,
    summary="Delete an agent",
    description="Delete an existing agent.",
    responses={
        200: {"description": "Agent deleted successfully"},
        401: {"description": "Authentication required"},
        403: {"model": AgentErrorResponse, "description": "Permission denied"},
        404: {"model": AgentErrorResponse, "description": "Agent not found"},
    },
)
async def delete_agent(
    agent_id: uuid.UUID,
    current_user: dict = Depends(require_permissions(Permission.AGENT_DELETE.value)),
    agent_service: AgentService = Depends(get_agent_service),
) -> AgentDeleteResponse:
    """Delete an existing agent."""
    try:
        # Get user entity from current_user
        user_entity = current_user["user_entity"]

        # Delete agent
        deleted = await agent_service.delete_agent(agent_id, user_entity)

        if not deleted:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={"error": "not_found", "message": "Agent not found"},
            )

        return AgentDeleteResponse(success=True, message="Agent deleted successfully")

    except PermissionError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail={"error": "permission_denied", "message": str(e)},
        )


@router.post(
    "/{agent_id}/activate",
    response_model=AgentStatusResponse,
    summary="Activate an agent",
    description="Activate an agent to make it available for tasks.",
    responses={
        200: {"description": "Agent activated successfully"},
        401: {"description": "Authentication required"},
        403: {"model": AgentErrorResponse, "description": "Permission denied"},
        404: {"model": AgentErrorResponse, "description": "Agent not found"},
    },
)
async def activate_agent(
    agent_id: uuid.UUID,
    current_user: dict = Depends(require_permissions(Permission.AGENT_UPDATE.value)),
    agent_service: AgentService = Depends(get_agent_service),
) -> AgentStatusResponse:
    """Activate an agent."""
    try:
        # Get user entity from current_user
        user_entity = current_user["user_entity"]

        # Activate agent
        agent = await agent_service.activate_agent(agent_id, user_entity)

        return AgentStatusResponse(
            id=agent.id,
            status=agent.status,
            message="Agent activated successfully",
            updated_at=agent.updated_at,
        )

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={"error": "not_found", "message": str(e)},
        )
    except PermissionError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail={"error": "permission_denied", "message": str(e)},
        )


@router.post(
    "/{agent_id}/deactivate",
    response_model=AgentStatusResponse,
    summary="Deactivate an agent",
    description="Deactivate an agent to make it unavailable for tasks.",
    responses={
        200: {"description": "Agent deactivated successfully"},
        401: {"description": "Authentication required"},
        403: {"model": AgentErrorResponse, "description": "Permission denied"},
        404: {"model": AgentErrorResponse, "description": "Agent not found"},
    },
)
async def deactivate_agent(
    agent_id: uuid.UUID,
    current_user: dict = Depends(require_permissions(Permission.AGENT_UPDATE.value)),
    agent_service: AgentService = Depends(get_agent_service),
) -> AgentStatusResponse:
    """Deactivate an agent."""
    try:
        # Get user entity from current_user
        user_entity = current_user["user_entity"]

        # Deactivate agent
        agent = await agent_service.deactivate_agent(agent_id, user_entity)

        return AgentStatusResponse(
            id=agent.id,
            status=agent.status,
            message="Agent deactivated successfully",
            updated_at=agent.updated_at,
        )

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={"error": "not_found", "message": str(e)},
        )
    except PermissionError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail={"error": "permission_denied", "message": str(e)},
        )


@router.get(
    "/search",
    response_model=AgentListResponse,
    summary="Search agents",
    description="Search agents by name, description, or tags.",
    responses={
        200: {"description": "Search results retrieved successfully"},
        401: {"description": "Authentication required"},
        403: {"model": AgentErrorResponse, "description": "Permission denied"},
    },
)
async def search_agents(
    query: str = Query(..., min_length=1, description="Search query"),
    include_public: bool = Query(True, description="Include public agents"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of agents"),
    offset: int = Query(0, ge=0, description="Number of agents to skip"),
    current_user: dict = Depends(require_permissions(Permission.AGENT_READ.value)),
    agent_service: AgentService = Depends(get_agent_service),
) -> AgentListResponse:
    """Search agents by name, description, or tags."""
    try:
        # Get user entity from current_user
        user_entity = current_user["user_entity"]

        # Search agents
        agents = await agent_service.search_agents(
            query, user_entity, include_public, limit, offset
        )

        # Convert to response schemas
        agent_responses = [
            AgentResponse.model_validate(agent.to_dict()) for agent in agents
        ]

        return AgentListResponse(
            agents=agent_responses,
            total=len(agent_responses),  # TODO: Get actual total count
            limit=limit,
            offset=offset,
        )

    except PermissionError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail={"error": "permission_denied", "message": str(e)},
        )


@router.get(
    "/metrics/summary",
    response_model=AgentMetricsSummaryResponse,
    summary="Get agent metrics summary",
    description="Get aggregated metrics for all user agents.",
    responses={
        200: {"description": "Metrics retrieved successfully"},
        401: {"description": "Authentication required"},
        403: {"model": AgentErrorResponse, "description": "Permission denied"},
    },
)
async def get_agent_metrics(
    current_user: dict = Depends(require_permissions(Permission.AGENT_READ.value)),
    agent_service: AgentService = Depends(get_agent_service),
) -> AgentMetricsSummaryResponse:
    """Get aggregated metrics for user's agents."""
    try:
        # Get user entity from current_user
        user_entity = current_user["user_entity"]

        # Get metrics
        metrics = await agent_service.get_agent_metrics(user_entity)

        return AgentMetricsSummaryResponse(**metrics)

    except PermissionError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail={"error": "permission_denied", "message": str(e)},
        )


@router.post(
    "/{agent_id}/chat",
    response_model=AgentChatResponse,
    summary="Chat with an agent",
    description="Send a message to an agent and get a response.",
    responses={
        200: {"description": "Chat response received successfully"},
        401: {"description": "Authentication required"},
        403: {"model": AgentErrorResponse, "description": "Permission denied"},
        404: {"model": AgentErrorResponse, "description": "Agent not found"},
    },
)
async def chat_with_agent(
    agent_id: uuid.UUID,
    request: AgentChatRequest,
    current_user: dict = Depends(require_permissions(Permission.AGENT_EXECUTE.value)),
    agent_service: AgentService = Depends(get_agent_service),
) -> AgentChatResponse:
    """Chat with an agent."""
    try:
        # Get user entity from current_user
        user_entity = current_user["user_entity"]

        # Get agent
        agent = await agent_service.get_agent(agent_id, user_entity)
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={"error": "not_found", "message": "Agent not found"},
            )

        # TODO: Implement actual chat functionality with conversation management
        # For now, return a mock response
        import time

        start_time = time.time()

        # Mock response
        response_text = f"Hello! I'm {agent.name}. You said: {request.message}"
        tokens_used = len(request.message.split()) + len(response_text.split())
        cost = tokens_used * 0.001  # Mock cost calculation
        response_time = time.time() - start_time

        return AgentChatResponse(
            conversation_id=request.conversation_id or uuid.uuid4(),
            message_id=uuid.uuid4(),
            response=response_text,
            tokens_used=tokens_used,
            cost=cost,
            response_time=response_time,
        )

    except PermissionError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail={"error": "permission_denied", "message": str(e)},
        )
