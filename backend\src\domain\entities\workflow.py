"""
Workflow domain entities.

This module defines the domain entities for workflow management
including workflow definitions, executions, and related components.
"""

import uuid
from datetime import UTC, datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class WorkflowStatus(str, Enum):
    """Workflow status enumeration."""
    DRAFT = "draft"
    ACTIVE = "active"
    INACTIVE = "inactive"
    ARCHIVED = "archived"


class ExecutionStatus(str, Enum):
    """Workflow execution status enumeration."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    TIMEOUT = "timeout"


class NodeType(str, Enum):
    """Workflow node type enumeration."""
    INPUT = "input"
    OUTPUT = "output"
    AGENT = "agent"
    CONDITION = "condition"
    TRANSFORM = "transform"
    API_CALL = "api_call"
    DELAY = "delay"
    LOOP = "loop"
    PARALLEL = "parallel"
    MERGE = "merge"
    TRIGGER = "trigger"
    ACTION = "action"
    CUSTOM = "custom"


class WorkflowComplexity(str, Enum):
    """Workflow complexity enumeration."""
    SIMPLE = "simple"
    MODERATE = "moderate"
    COMPLEX = "complex"
    ADVANCED = "advanced"


class WorkflowNode(BaseModel):
    """Workflow node entity."""
    
    id: str = Field(..., description="Unique node identifier")
    type: NodeType = Field(..., description="Node type")
    position: Dict[str, float] = Field(..., description="Node position (x, y)")
    data: Dict[str, Any] = Field(default_factory=dict, description="Node configuration data")
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True


class WorkflowEdge(BaseModel):
    """Workflow edge entity."""
    
    id: str = Field(..., description="Unique edge identifier")
    source: str = Field(..., description="Source node ID")
    target: str = Field(..., description="Target node ID")
    source_handle: Optional[str] = Field(None, description="Source handle ID")
    target_handle: Optional[str] = Field(None, description="Target handle ID")
    data: Dict[str, Any] = Field(default_factory=dict, description="Edge configuration data")


class WorkflowMetadata(BaseModel):
    """Workflow metadata entity."""
    
    category: Optional[str] = Field(None, description="Workflow category")
    complexity: WorkflowComplexity = Field(default=WorkflowComplexity.SIMPLE, description="Workflow complexity")
    estimated_duration: Optional[int] = Field(None, description="Estimated execution duration in seconds")
    required_capabilities: List[str] = Field(default_factory=list, description="Required agent capabilities")
    tags: List[str] = Field(default_factory=list, description="Workflow tags")
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True


class WorkflowPerformance(BaseModel):
    """Workflow performance metrics entity."""
    
    total_executions: int = Field(default=0, description="Total number of executions")
    successful_executions: int = Field(default=0, description="Number of successful executions")
    failed_executions: int = Field(default=0, description="Number of failed executions")
    average_execution_time: float = Field(default=0.0, description="Average execution time in seconds")
    last_execution_time: Optional[datetime] = Field(None, description="Last execution timestamp")
    total_cost: float = Field(default=0.0, description="Total execution cost")


class Workflow(BaseModel):
    """Workflow domain entity."""
    
    # Identity
    id: uuid.UUID = Field(default_factory=uuid.uuid4, description="Unique workflow identifier")
    name: str = Field(..., description="Workflow name")
    description: Optional[str] = Field(None, description="Workflow description")
    
    # Structure
    nodes: List[WorkflowNode] = Field(default_factory=list, description="Workflow nodes")
    edges: List[WorkflowEdge] = Field(default_factory=list, description="Workflow edges")
    
    # Configuration
    version: str = Field(default="1.0.0", description="Workflow version")
    status: WorkflowStatus = Field(default=WorkflowStatus.DRAFT, description="Workflow status")
    is_public: bool = Field(default=False, description="Whether workflow is public")
    
    # Ownership
    owner_id: uuid.UUID = Field(..., description="Workflow owner user ID")
    
    # Metadata
    metadata: WorkflowMetadata = Field(default_factory=WorkflowMetadata, description="Workflow metadata")
    performance: WorkflowPerformance = Field(default_factory=WorkflowPerformance, description="Performance metrics")
    
    # Timestamps
    created_at: datetime = Field(default_factory=lambda: datetime.now(UTC), description="Creation timestamp")
    updated_at: datetime = Field(default_factory=lambda: datetime.now(UTC), description="Last update timestamp")
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            uuid.UUID: lambda v: str(v),
        }

    def update_performance(self, execution_time: float, success: bool, cost: float = 0.0) -> None:
        """Update workflow performance metrics."""
        self.performance.total_executions += 1
        
        if success:
            self.performance.successful_executions += 1
        else:
            self.performance.failed_executions += 1
        
        # Update average execution time
        total_time = self.performance.average_execution_time * (self.performance.total_executions - 1)
        self.performance.average_execution_time = (total_time + execution_time) / self.performance.total_executions
        
        self.performance.last_execution_time = datetime.now(UTC)
        self.performance.total_cost += cost
        self.updated_at = datetime.now(UTC)

    def validate_structure(self) -> tuple[bool, List[str]]:
        """Validate workflow structure and return validation results."""
        errors = []
        
        # Check for at least one input and one output node
        input_nodes = [node for node in self.nodes if node.type == NodeType.INPUT]
        output_nodes = [node for node in self.nodes if node.type == NodeType.OUTPUT]
        
        if not input_nodes:
            errors.append("Workflow must have at least one input node")
        
        if not output_nodes:
            errors.append("Workflow must have at least one output node")
        
        # Check for orphaned nodes (nodes without connections)
        connected_nodes = set()
        for edge in self.edges:
            connected_nodes.add(edge.source)
            connected_nodes.add(edge.target)
        
        node_ids = {node.id for node in self.nodes}
        orphaned_nodes = node_ids - connected_nodes
        
        if orphaned_nodes and len(self.nodes) > 1:
            errors.append(f"Found orphaned nodes: {', '.join(orphaned_nodes)}")
        
        # Check for invalid edge references
        for edge in self.edges:
            if edge.source not in node_ids:
                errors.append(f"Edge {edge.id} references non-existent source node: {edge.source}")
            if edge.target not in node_ids:
                errors.append(f"Edge {edge.id} references non-existent target node: {edge.target}")
        
        return len(errors) == 0, errors

    def get_complexity(self) -> WorkflowComplexity:
        """Calculate workflow complexity based on structure."""
        node_count = len(self.nodes)
        edge_count = len(self.edges)
        
        # Count special node types that add complexity
        complex_nodes = [
            node for node in self.nodes 
            if node.type in [NodeType.CONDITION, NodeType.LOOP, NodeType.PARALLEL]
        ]
        
        complexity_score = node_count + edge_count + len(complex_nodes) * 2
        
        if complexity_score <= 5:
            return WorkflowComplexity.SIMPLE
        elif complexity_score <= 15:
            return WorkflowComplexity.MODERATE
        elif complexity_score <= 30:
            return WorkflowComplexity.COMPLEX
        else:
            return WorkflowComplexity.ADVANCED


class WorkflowExecution(BaseModel):
    """Workflow execution entity."""
    
    # Identity
    id: uuid.UUID = Field(default_factory=uuid.uuid4, description="Unique execution identifier")
    workflow_id: uuid.UUID = Field(..., description="Associated workflow ID")
    user_id: uuid.UUID = Field(..., description="User who initiated execution")
    
    # Execution data
    status: ExecutionStatus = Field(default=ExecutionStatus.PENDING, description="Execution status")
    input_data: Dict[str, Any] = Field(default_factory=dict, description="Execution input data")
    output_data: Dict[str, Any] = Field(default_factory=dict, description="Execution output data")
    error_data: Optional[Dict[str, Any]] = Field(None, description="Error information if failed")
    
    # Progress tracking
    current_node_id: Optional[str] = Field(None, description="Currently executing node ID")
    completed_nodes: List[str] = Field(default_factory=list, description="List of completed node IDs")
    execution_log: List[Dict[str, Any]] = Field(default_factory=list, description="Execution log entries")
    
    # Metrics
    execution_time: Optional[float] = Field(None, description="Total execution time in seconds")
    cost: float = Field(default=0.0, description="Execution cost")
    
    # Timestamps
    started_at: Optional[datetime] = Field(None, description="Execution start timestamp")
    completed_at: Optional[datetime] = Field(None, description="Execution completion timestamp")
    created_at: datetime = Field(default_factory=lambda: datetime.now(UTC), description="Creation timestamp")
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            uuid.UUID: lambda v: str(v),
        }

    def start_execution(self) -> None:
        """Mark execution as started."""
        self.status = ExecutionStatus.RUNNING
        self.started_at = datetime.now(UTC)

    def complete_execution(self, output_data: Dict[str, Any], cost: float = 0.0) -> None:
        """Mark execution as completed successfully."""
        self.status = ExecutionStatus.COMPLETED
        self.output_data = output_data
        self.cost = cost
        self.completed_at = datetime.now(UTC)
        
        if self.started_at:
            self.execution_time = (self.completed_at - self.started_at).total_seconds()

    def fail_execution(self, error_data: Dict[str, Any], cost: float = 0.0) -> None:
        """Mark execution as failed."""
        self.status = ExecutionStatus.FAILED
        self.error_data = error_data
        self.cost = cost
        self.completed_at = datetime.now(UTC)
        
        if self.started_at:
            self.execution_time = (self.completed_at - self.started_at).total_seconds()

    def add_log_entry(self, level: str, message: str, node_id: Optional[str] = None, data: Optional[Dict[str, Any]] = None) -> None:
        """Add an entry to the execution log."""
        log_entry = {
            "timestamp": datetime.now(UTC).isoformat(),
            "level": level,
            "message": message,
            "node_id": node_id,
            "data": data or {}
        }
        self.execution_log.append(log_entry)
