'use client';

import React, { use<PERSON>allback, useRef, useState } from 'react';
import {
    addEdge,
    Background,
    BackgroundVariant,
    Connection,
    Controls,
    Edge,
    MiniMap,
    Panel,
    ReactFlow,
    ReactFlowProvider,
    useEdgesState,
    useNodesState,
    useReactFlow
} from 'reactflow';
import 'reactflow/dist/style.css';

import { cn } from '@/shared/lib/utils';
import { Badge } from '@/shared/ui/badge';
import { Button } from '@/shared/ui/button';
import {
    Grid3X3,
    Map,
    Maximize,
    Pause,
    Play,
    RotateCcw,
    Save,
    Square,
    ZoomIn,
    ZoomOut
} from 'lucide-react';

import {
    CanvasState,
    ConnectionEvent,
    EdgeChangeEvent,
    NodeChangeEvent,
    WorkflowEdge,
    WorkflowNode,
    WorkflowStatus
} from '../../types';
import { AgentNode } from '../nodes/agent-node';
import { ConditionNode } from '../nodes/condition-node';
import { InputNode } from '../nodes/input-node';
import { OutputNode } from '../nodes/output-node';
import { ToolNode } from '../nodes/tool-node';

// Custom node types mapping
const nodeTypes = {
  agent: AgentNode,
  tool: ToolNode,
  condition: ConditionNode,
  input: InputNode,
  output: OutputNode,
};

interface WorkflowCanvasProps {
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
  onNodesChange: (changes: NodeChangeEvent[]) => void;
  onEdgesChange: (changes: EdgeChangeEvent[]) => void;
  onConnect: (connection: ConnectionEvent) => void;
  onNodeClick?: (event: React.MouseEvent, node: WorkflowNode) => void;
  onNodeDoubleClick?: (event: React.MouseEvent, node: WorkflowNode) => void;
  onEdgeClick?: (event: React.MouseEvent, edge: WorkflowEdge) => void;
  onCanvasClick?: (event: React.MouseEvent) => void;
  onDrop?: (event: React.DragEvent) => void;
  onDragOver?: (event: React.DragEvent) => void;
  className?: string;
  readonly?: boolean;
  showGrid?: boolean;
  showMinimap?: boolean;
  snapToGrid?: boolean;
  gridSize?: number;
  workflowStatus?: WorkflowStatus;
  onSave?: () => void;
  onExecute?: () => void;
  onPause?: () => void;
  onStop?: () => void;
  onReset?: () => void;
}

const WorkflowCanvasInner: React.FC<WorkflowCanvasProps> = ({
  nodes: initialNodes,
  edges: initialEdges,
  onNodesChange,
  onEdgesChange,
  onConnect,
  onNodeClick,
  onNodeDoubleClick,
  onEdgeClick,
  onCanvasClick,
  onDrop,
  onDragOver,
  className,
  readonly = false,
  showGrid = true,
  showMinimap = true,
  snapToGrid = true,
  gridSize = 20,
  workflowStatus = WorkflowStatus.DRAFT,
  onSave,
  onExecute,
  onPause,
  onStop,
  onReset,
}) => {
  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const [nodes, setNodes, onNodesChangeInternal] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChangeInternal] = useEdgesState(initialEdges);
  const [canvasState, setCanvasState] = useState<CanvasState>({
    viewport: { x: 0, y: 0, zoom: 1 },
    selectedNodes: [],
    selectedEdges: [],
    connectionMode: false,
    gridSize,
    snapToGrid,
    showGrid,
    showMinimap,
    readonly,
  });

  const {
    zoomIn,
    zoomOut,
    fitView,
    getViewport,
    setViewport,
    screenToFlowPosition
  } = useReactFlow();

  // Handle node changes with custom logic
  const handleNodesChange = useCallback((changes: any[]) => {
    onNodesChangeInternal(changes);
    onNodesChange(changes);
  }, [onNodesChange, onNodesChangeInternal]);

  // Handle edge changes with custom logic
  const handleEdgesChange = useCallback((changes: any[]) => {
    onEdgesChangeInternal(changes);
    onEdgesChange(changes);
  }, [onEdgesChange, onEdgesChangeInternal]);

  // Handle new connections
  const handleConnect = useCallback((connection: Connection) => {
    const newEdge: Edge = {
      ...connection,
      id: `edge-${connection.source}-${connection.target}`,
      type: 'smoothstep',
      animated: workflowStatus === WorkflowStatus.ACTIVE,
    };

    setEdges((eds) => addEdge(newEdge, eds));
    onConnect(connection as ConnectionEvent);
  }, [setEdges, onConnect, workflowStatus]);

  // Handle drag over for node dropping
  const handleDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';

    // Add visual feedback for valid drop zone
    const canvas = event.currentTarget as HTMLElement;
    canvas.style.backgroundColor = 'rgba(59, 130, 246, 0.05)';
    canvas.style.borderColor = 'rgba(59, 130, 246, 0.2)';

    onDragOver?.(event);
  }, [onDragOver]);

  // Handle drag leave to reset visual feedback
  const handleDragLeave = useCallback((event: React.DragEvent) => {
    const canvas = event.currentTarget as HTMLElement;
    canvas.style.backgroundColor = '';
    canvas.style.borderColor = '';
  }, []);

  // Handle drop for creating new nodes
  const handleDrop = useCallback((event: React.DragEvent) => {
    event.preventDefault();

    // Reset visual feedback
    const canvas = event.currentTarget as HTMLElement;
    canvas.style.backgroundColor = '';
    canvas.style.borderColor = '';

    if (!reactFlowWrapper.current) return;

    const reactFlowBounds = reactFlowWrapper.current.getBoundingClientRect();
    const position = screenToFlowPosition({
      x: event.clientX - reactFlowBounds.left,
      y: event.clientY - reactFlowBounds.top,
    });

    onDrop?.(event);
  }, [onDrop, screenToFlowPosition]);

  // Canvas controls
  const handleZoomIn = () => zoomIn();
  const handleZoomOut = () => zoomOut();
  const handleFitView = () => fitView();

  const toggleGrid = () => {
    setCanvasState(prev => ({ ...prev, showGrid: !prev.showGrid }));
  };

  const toggleMinimap = () => {
    setCanvasState(prev => ({ ...prev, showMinimap: !prev.showMinimap }));
  };

  const getStatusColor = (status: WorkflowStatus) => {
    switch (status) {
      case WorkflowStatus.ACTIVE:
        return 'bg-green-500';
      case WorkflowStatus.PAUSED:
        return 'bg-yellow-500';
      case WorkflowStatus.FAILED:
        return 'bg-red-500';
      case WorkflowStatus.COMPLETED:
        return 'bg-blue-500';
      default:
        return 'bg-gray-500';
    }
  };

  return (
    <div
      className={cn('h-full w-full relative', className)}
      ref={reactFlowWrapper}
    >
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={handleNodesChange}
        onEdgesChange={handleEdgesChange}
        onConnect={handleConnect}
        onNodeClick={onNodeClick}
        onNodeDoubleClick={onNodeDoubleClick}
        onEdgeClick={onEdgeClick}
        onPaneClick={onCanvasClick}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        nodeTypes={nodeTypes}
        fitView
        snapToGrid={canvasState.snapToGrid}
        snapGrid={[canvasState.gridSize, canvasState.gridSize]}
        defaultEdgeOptions={{
          type: 'smoothstep',
          animated: workflowStatus === WorkflowStatus.ACTIVE,
        }}
        deleteKeyCode={readonly ? null : 'Delete'}
        multiSelectionKeyCode="Shift"
        panOnDrag={!readonly}
        nodesDraggable={!readonly}
        nodesConnectable={!readonly}
        elementsSelectable={!readonly}
        className="workflow-canvas"
      >
        {/* Background with grid */}
        {canvasState.showGrid && (
          <Background
            variant={BackgroundVariant.Dots}
            gap={canvasState.gridSize}
            size={1}
            className="opacity-50"
          />
        )}

        {/* Controls */}
        <Controls
          showZoom={true}
          showFitView={true}
          showInteractive={!readonly}
          position="bottom-right"
        />

        {/* Minimap */}
        {canvasState.showMinimap && (
          <MiniMap
            position="bottom-left"
            className="border border-border rounded-lg"
            nodeColor={(node) => {
              switch (node.type) {
                case 'agent': return '#3b82f6';
                case 'tool': return '#10b981';
                case 'condition': return '#f59e0b';
                case 'input': return '#8b5cf6';
                case 'output': return '#ef4444';
                default: return '#6b7280';
              }
            }}
          />
        )}

        {/* Top Panel - Workflow Controls */}
        <Panel position="top-center" className="flex items-center gap-2">
          <Badge
            variant="secondary"
            className={cn('text-white', getStatusColor(workflowStatus))}
          >
            {workflowStatus}
          </Badge>

          {!readonly && (
            <>
              {onSave && (
                <Button size="sm" variant="outline" onClick={onSave}>
                  <Save className="h-4 w-4 mr-1" />
                  Save
                </Button>
              )}

              {workflowStatus === WorkflowStatus.DRAFT && onExecute && (
                <Button size="sm" onClick={onExecute}>
                  <Play className="h-4 w-4 mr-1" />
                  Execute
                </Button>
              )}

              {workflowStatus === WorkflowStatus.ACTIVE && onPause && (
                <Button size="sm" variant="outline" onClick={onPause}>
                  <Pause className="h-4 w-4 mr-1" />
                  Pause
                </Button>
              )}

              {(workflowStatus === WorkflowStatus.ACTIVE || workflowStatus === WorkflowStatus.PAUSED) && onStop && (
                <Button size="sm" variant="destructive" onClick={onStop}>
                  <Square className="h-4 w-4 mr-1" />
                  Stop
                </Button>
              )}

              {onReset && (
                <Button size="sm" variant="outline" onClick={onReset}>
                  <RotateCcw className="h-4 w-4 mr-1" />
                  Reset
                </Button>
              )}
            </>
          )}
        </Panel>

        {/* Top Right Panel - View Controls */}
        <Panel position="top-right" className="flex items-center gap-1">
          <Button size="sm" variant="ghost" onClick={handleZoomIn}>
            <ZoomIn className="h-4 w-4" />
          </Button>
          <Button size="sm" variant="ghost" onClick={handleZoomOut}>
            <ZoomOut className="h-4 w-4" />
          </Button>
          <Button size="sm" variant="ghost" onClick={handleFitView}>
            <Maximize className="h-4 w-4" />
          </Button>
          <Button
            size="sm"
            variant={canvasState.showGrid ? "default" : "ghost"}
            onClick={toggleGrid}
          >
            <Grid3X3 className="h-4 w-4" />
          </Button>
          <Button
            size="sm"
            variant={canvasState.showMinimap ? "default" : "ghost"}
            onClick={toggleMinimap}
          >
            <Map className="h-4 w-4" />
          </Button>
        </Panel>
      </ReactFlow>
    </div>
  );
};

export const WorkflowCanvas: React.FC<WorkflowCanvasProps> = (props) => {
  return (
    <ReactFlowProvider>
      <WorkflowCanvasInner {...props} />
    </ReactFlowProvider>
  );
};

export default WorkflowCanvas;
