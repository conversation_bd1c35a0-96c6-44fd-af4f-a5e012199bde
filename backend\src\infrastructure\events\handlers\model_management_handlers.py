"""
Model Management Event Handlers.

This module contains event handlers for model management domain events.
"""

import json
from typing import Any, Dict

from src.domain.events.model_management_events import (
    ModelDownloadCompletedEvent,
    ModelDownloadFailedEvent,
    ModelDownloadProgressEvent,
    ModelDownloadStartedEvent,
    ModelHealthCheckEvent,
    ModelInstanceCreatedEvent,
    ModelInstanceStatusChangedEvent,
    ModelServingStartedEvent,
    ModelServingStoppedEvent,
)
from src.infrastructure.events.event_publisher import subscribe_to_event
from src.infrastructure.logging.setup import get_logger

logger = get_logger(__name__)


class ModelManagementEventHandlers:
    """Event handlers for model management events."""

    def __init__(self, websocket_manager=None, notification_service=None):
        """
        Initialize event handlers.

        Args:
            websocket_manager: WebSocket manager for real-time updates
            notification_service: Service for sending notifications
        """
        self.websocket_manager = websocket_manager
        self.notification_service = notification_service
        self._register_handlers()

    def _register_handlers(self) -> None:
        """Register all event handlers."""
        # Model instance events
        subscribe_to_event(
            "model_instance.created",
            self.handle_model_instance_created,
            "model_instance_created_handler"
        )
        
        subscribe_to_event(
            "model_instance.status_changed",
            self.handle_model_instance_status_changed,
            "model_instance_status_changed_handler"
        )

        # Model download events
        subscribe_to_event(
            "model_download.started",
            self.handle_model_download_started,
            "model_download_started_handler"
        )
        
        subscribe_to_event(
            "model_download.progress",
            self.handle_model_download_progress,
            "model_download_progress_handler"
        )
        
        subscribe_to_event(
            "model_download.completed",
            self.handle_model_download_completed,
            "model_download_completed_handler"
        )
        
        subscribe_to_event(
            "model_download.failed",
            self.handle_model_download_failed,
            "model_download_failed_handler"
        )

        # Model serving events
        subscribe_to_event(
            "model_serving.started",
            self.handle_model_serving_started,
            "model_serving_started_handler"
        )
        
        subscribe_to_event(
            "model_serving.stopped",
            self.handle_model_serving_stopped,
            "model_serving_stopped_handler"
        )

        # Model health events
        subscribe_to_event(
            "model_health.checked",
            self.handle_model_health_check,
            "model_health_check_handler"
        )

        logger.info("Registered model management event handlers")

    async def handle_model_instance_created(self, event: ModelInstanceCreatedEvent) -> None:
        """Handle model instance created event."""
        logger.info(f"Model instance created: {event.instance_id}")
        
        # Send WebSocket notification
        if self.websocket_manager:
            await self._send_websocket_update(
                user_id=str(event.user_id),
                event_type="model_instance_created",
                data={
                    "instance_id": str(event.instance_id),
                    "model_id": event.model_id,
                    "configuration": event.configuration,
                }
            )

        # Log user action
        logger.log_user_action(
            user_id=str(event.user_id),
            action="create_model_instance",
            resource_type="model_instance",
            resource_id=str(event.instance_id),
            model_id=event.model_id,
        )

    async def handle_model_instance_status_changed(self, event: ModelInstanceStatusChangedEvent) -> None:
        """Handle model instance status changed event."""
        logger.info(
            f"Model instance {event.instance_id} status changed: {event.old_status} -> {event.new_status}"
        )
        
        # Send WebSocket notification
        if self.websocket_manager:
            await self._send_websocket_update(
                user_id=str(event.user_id),
                event_type="model_instance_status_changed",
                data={
                    "instance_id": str(event.instance_id),
                    "model_id": event.model_id,
                    "old_status": event.old_status.value,
                    "new_status": event.new_status.value,
                }
            )

        # Log performance metrics for status changes
        logger.log_performance(
            operation="model_instance_status_change",
            duration=0.0,  # Instantaneous
            instance_id=str(event.instance_id),
            old_status=event.old_status.value,
            new_status=event.new_status.value,
        )

    async def handle_model_download_started(self, event: ModelDownloadStartedEvent) -> None:
        """Handle model download started event."""
        logger.info(f"Model download started: {event.task_id}")
        
        # Send WebSocket notification
        if self.websocket_manager:
            await self._send_websocket_update(
                user_id=str(event.user_id),
                event_type="model_download_started",
                data={
                    "task_id": str(event.task_id),
                    "instance_id": str(event.instance_id),
                    "model_id": event.model_id,
                    "download_url": event.download_url,
                }
            )

        # Log user action
        logger.log_user_action(
            user_id=str(event.user_id),
            action="start_model_download",
            resource_type="download_task",
            resource_id=str(event.task_id),
            model_id=event.model_id,
        )

    async def handle_model_download_progress(self, event: ModelDownloadProgressEvent) -> None:
        """Handle model download progress event."""
        logger.debug(f"Model download progress: {event.task_id} - {event.progress}%")
        
        # Send WebSocket notification for real-time progress updates
        if self.websocket_manager:
            await self._send_websocket_update(
                user_id=str(event.user_id),
                event_type="model_download_progress",
                data={
                    "task_id": str(event.task_id),
                    "instance_id": str(event.instance_id),
                    "model_id": event.model_id,
                    "progress": event.progress,
                    "downloaded_size": event.downloaded_size,
                    "total_size": event.total_size,
                    "download_speed": event.download_speed,
                }
            )

        # Log progress milestones
        if event.progress % 25 == 0:  # Log every 25%
            logger.info(
                f"Download progress milestone: {event.model_id} - {event.progress}%",
                extra={
                    "task_id": str(event.task_id),
                    "progress": event.progress,
                    "download_speed": event.download_speed,
                }
            )

    async def handle_model_download_completed(self, event: ModelDownloadCompletedEvent) -> None:
        """Handle model download completed event."""
        logger.info(f"Model download completed: {event.task_id}")
        
        # Send WebSocket notification
        if self.websocket_manager:
            await self._send_websocket_update(
                user_id=str(event.user_id),
                event_type="model_download_completed",
                data={
                    "task_id": str(event.task_id),
                    "instance_id": str(event.instance_id),
                    "model_id": event.model_id,
                    "local_path": event.local_path,
                }
            )

        # Send notification
        if self.notification_service:
            await self.notification_service.send_notification(
                user_id=str(event.user_id),
                title="Model Download Complete",
                message=f"Model {event.model_id} has been downloaded successfully",
                type="success"
            )

        # Log user action
        logger.log_user_action(
            user_id=str(event.user_id),
            action="complete_model_download",
            resource_type="download_task",
            resource_id=str(event.task_id),
            model_id=event.model_id,
        )

    async def handle_model_download_failed(self, event: ModelDownloadFailedEvent) -> None:
        """Handle model download failed event."""
        logger.error(f"Model download failed: {event.task_id} - {event.error_message}")
        
        # Send WebSocket notification
        if self.websocket_manager:
            await self._send_websocket_update(
                user_id=str(event.user_id),
                event_type="model_download_failed",
                data={
                    "task_id": str(event.task_id),
                    "instance_id": str(event.instance_id),
                    "model_id": event.model_id,
                    "error_message": event.error_message,
                    "error_details": event.error_details,
                }
            )

        # Send notification
        if self.notification_service:
            await self.notification_service.send_notification(
                user_id=str(event.user_id),
                title="Model Download Failed",
                message=f"Failed to download model {event.model_id}: {event.error_message}",
                type="error"
            )

        # Log error
        logger.error(
            f"Model download failed: {event.model_id}",
            extra={
                "task_id": str(event.task_id),
                "error_message": event.error_message,
                "error_details": event.error_details,
            }
        )

    async def handle_model_serving_started(self, event: ModelServingStartedEvent) -> None:
        """Handle model serving started event."""
        logger.info(f"Model serving started: {event.instance_id}")
        
        # Send WebSocket notification
        if self.websocket_manager:
            await self._send_websocket_update(
                user_id=str(event.user_id),
                event_type="model_serving_started",
                data={
                    "instance_id": str(event.instance_id),
                    "model_id": event.model_id,
                    "serving_port": event.serving_port,
                    "serving_url": event.serving_url,
                    "api_endpoint": event.api_endpoint,
                }
            )

        # Log user action
        logger.log_user_action(
            user_id=str(event.user_id),
            action="start_model_serving",
            resource_type="model_instance",
            resource_id=str(event.instance_id),
            model_id=event.model_id,
            serving_port=event.serving_port,
        )

    async def handle_model_serving_stopped(self, event: ModelServingStoppedEvent) -> None:
        """Handle model serving stopped event."""
        logger.info(f"Model serving stopped: {event.instance_id}")
        
        # Send WebSocket notification
        if self.websocket_manager:
            await self._send_websocket_update(
                user_id=str(event.user_id),
                event_type="model_serving_stopped",
                data={
                    "instance_id": str(event.instance_id),
                    "model_id": event.model_id,
                }
            )

        # Log user action
        logger.log_user_action(
            user_id=str(event.user_id),
            action="stop_model_serving",
            resource_type="model_instance",
            resource_id=str(event.instance_id),
            model_id=event.model_id,
        )

    async def handle_model_health_check(self, event: ModelHealthCheckEvent) -> None:
        """Handle model health check event."""
        logger.debug(f"Model health check: {event.instance_id} - {event.health_status}")
        
        # Send WebSocket notification for health status updates
        if self.websocket_manager:
            await self._send_websocket_update(
                user_id=str(event.user_id),
                event_type="model_health_check",
                data={
                    "instance_id": str(event.instance_id),
                    "model_id": event.model_id,
                    "health_status": event.health_status,
                    "response_time_ms": event.response_time_ms,
                    "memory_usage_mb": event.memory_usage_mb,
                    "gpu_memory_usage_mb": event.gpu_memory_usage_mb,
                }
            )

        # Log performance metrics
        if event.response_time_ms:
            logger.log_performance(
                operation="model_health_check",
                duration=event.response_time_ms / 1000,  # Convert to seconds
                instance_id=str(event.instance_id),
                health_status=event.health_status,
                memory_usage_mb=event.memory_usage_mb,
            )

        # Log warnings for unhealthy models
        if event.health_status in ["unhealthy", "error"]:
            logger.warning(
                f"Model health issue detected: {event.model_id}",
                extra={
                    "instance_id": str(event.instance_id),
                    "health_status": event.health_status,
                    "response_time_ms": event.response_time_ms,
                }
            )

    async def _send_websocket_update(self, user_id: str, event_type: str, data: Dict[str, Any]) -> None:
        """Send WebSocket update to user."""
        if not self.websocket_manager:
            return

        try:
            message = {
                "type": event_type,
                "data": data,
                "timestamp": data.get("occurred_at") or "now",
            }
            
            await self.websocket_manager.send_to_user(user_id, json.dumps(message))
            
        except Exception as e:
            logger.error(f"Failed to send WebSocket update: {e}")


# Global instance
_model_management_handlers: ModelManagementEventHandlers = None


def initialize_model_management_handlers(websocket_manager=None, notification_service=None) -> ModelManagementEventHandlers:
    """
    Initialize model management event handlers.

    Args:
        websocket_manager: WebSocket manager for real-time updates
        notification_service: Service for sending notifications

    Returns:
        ModelManagementEventHandlers instance
    """
    global _model_management_handlers
    _model_management_handlers = ModelManagementEventHandlers(
        websocket_manager=websocket_manager,
        notification_service=notification_service
    )
    return _model_management_handlers


def get_model_management_handlers() -> ModelManagementEventHandlers:
    """Get the global model management event handlers instance."""
    return _model_management_handlers
