"""
Workflow repository interface.

This module defines the repository interface for workflow-related operations.
"""

import uuid
from abc import ABC, abstractmethod
from typing import List, Optional

from src.domain.entities.workflow import Workflow, WorkflowExecution, WorkflowStatus


class WorkflowRepository(ABC):
    """Abstract repository interface for workflow operations."""

    @abstractmethod
    async def get_by_id(self, workflow_id: uuid.UUID) -> Optional[Workflow]:
        """
        Get a workflow by its ID.

        Args:
            workflow_id: The workflow ID

        Returns:
            The workflow if found, None otherwise
        """
        pass

    @abstractmethod
    async def get_by_owner(
        self, 
        owner_id: uuid.UUID, 
        status: Optional[WorkflowStatus] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[Workflow]:
        """
        Get workflows by owner ID with optional filtering.

        Args:
            owner_id: The owner user ID
            status: Optional status filter
            limit: Maximum number of workflows to return
            offset: Number of workflows to skip

        Returns:
            List of workflows
        """
        pass

    @abstractmethod
    async def get_public_workflows(
        self,
        limit: int = 100,
        offset: int = 0
    ) -> List[Workflow]:
        """
        Get public workflows.

        Args:
            limit: Maximum number of workflows to return
            offset: Number of workflows to skip

        Returns:
            List of public workflows
        """
        pass

    @abstractmethod
    async def search_workflows(
        self,
        query: str,
        owner_id: Optional[uuid.UUID] = None,
        include_public: bool = True,
        limit: int = 100,
        offset: int = 0
    ) -> List[Workflow]:
        """
        Search workflows by name or description.

        Args:
            query: Search query string
            owner_id: Optional owner filter
            include_public: Whether to include public workflows
            limit: Maximum number of workflows to return
            offset: Number of workflows to skip

        Returns:
            List of matching workflows
        """
        pass

    @abstractmethod
    async def create(self, workflow: Workflow) -> Workflow:
        """
        Create a new workflow.

        Args:
            workflow: The workflow to create

        Returns:
            The created workflow
        """
        pass

    @abstractmethod
    async def update(self, workflow: Workflow) -> Workflow:
        """
        Update an existing workflow.

        Args:
            workflow: The workflow to update

        Returns:
            The updated workflow
        """
        pass

    @abstractmethod
    async def delete(self, workflow_id: uuid.UUID) -> bool:
        """
        Delete a workflow.

        Args:
            workflow_id: The workflow ID to delete

        Returns:
            True if deleted, False if not found
        """
        pass

    @abstractmethod
    async def count_by_owner(self, owner_id: uuid.UUID) -> int:
        """
        Count workflows by owner.

        Args:
            owner_id: The owner user ID

        Returns:
            Number of workflows owned by the user
        """
        pass


class WorkflowExecutionRepository(ABC):
    """Abstract repository interface for workflow execution operations."""

    @abstractmethod
    async def get_by_id(self, execution_id: uuid.UUID) -> Optional[WorkflowExecution]:
        """
        Get a workflow execution by its ID.

        Args:
            execution_id: The execution ID

        Returns:
            The execution if found, None otherwise
        """
        pass

    @abstractmethod
    async def get_by_workflow(
        self,
        workflow_id: uuid.UUID,
        limit: int = 100,
        offset: int = 0
    ) -> List[WorkflowExecution]:
        """
        Get executions for a specific workflow.

        Args:
            workflow_id: The workflow ID
            limit: Maximum number of executions to return
            offset: Number of executions to skip

        Returns:
            List of executions
        """
        pass

    @abstractmethod
    async def get_by_user(
        self,
        user_id: uuid.UUID,
        limit: int = 100,
        offset: int = 0
    ) -> List[WorkflowExecution]:
        """
        Get executions for a specific user.

        Args:
            user_id: The user ID
            limit: Maximum number of executions to return
            offset: Number of executions to skip

        Returns:
            List of executions
        """
        pass

    @abstractmethod
    async def get_running_executions(self) -> List[WorkflowExecution]:
        """
        Get all currently running executions.

        Returns:
            List of running executions
        """
        pass

    @abstractmethod
    async def create(self, execution: WorkflowExecution) -> WorkflowExecution:
        """
        Create a new workflow execution.

        Args:
            execution: The execution to create

        Returns:
            The created execution
        """
        pass

    @abstractmethod
    async def update(self, execution: WorkflowExecution) -> WorkflowExecution:
        """
        Update an existing workflow execution.

        Args:
            execution: The execution to update

        Returns:
            The updated execution
        """
        pass

    @abstractmethod
    async def delete(self, execution_id: uuid.UUID) -> bool:
        """
        Delete a workflow execution.

        Args:
            execution_id: The execution ID to delete

        Returns:
            True if deleted, False if not found
        """
        pass

    @abstractmethod
    async def count_by_workflow(self, workflow_id: uuid.UUID) -> int:
        """
        Count executions for a workflow.

        Args:
            workflow_id: The workflow ID

        Returns:
            Number of executions for the workflow
        """
        pass

    @abstractmethod
    async def count_by_user(self, user_id: uuid.UUID) -> int:
        """
        Count executions for a user.

        Args:
            user_id: The user ID

        Returns:
            Number of executions by the user
        """
        pass
