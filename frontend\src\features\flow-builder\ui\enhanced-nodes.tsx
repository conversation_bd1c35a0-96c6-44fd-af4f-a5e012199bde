'use client';

import React from 'react';
import { <PERSON><PERSON>, <PERSON>sition, NodeProps } from 'reactflow';
import { motion } from 'framer-motion';
import { 
  Brain, 
  Database, 
  MessageSquare, 
  FileText, 
  Link, 
  Search, 
  Template, 
  Code, 
  Memory, 
  Archive, 
  Tool, 
  Zap,
  Bot,
  HardDrive,
  Cpu,
  Activity
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/ui/card';
import { Badge } from '@/shared/ui/badge';
import { Avatar, AvatarFallback } from '@/shared/ui/avatar';

// Enhanced node data interfaces
export interface AgentNodeData {
  agentId?: string;
  agentName: string;
  agentType: string;
  configuration: Record<string, any>;
  status?: 'active' | 'inactive' | 'error';
}

export interface KnowledgeEntityNodeData {
  entityId?: string;
  entityName: string;
  entityType: string;
  properties: Record<string, any>;
  relationships?: string[];
}

export interface ModelInstanceNodeData {
  instanceId?: string;
  modelId: string;
  modelName: string;
  status: 'available' | 'loading' | 'serving' | 'error';
  servingUrl?: string;
  configuration: Record<string, any>;
}

export interface ChatInterfaceNodeData {
  interfaceType: 'text' | 'voice' | 'multimodal';
  configuration: Record<string, any>;
  contextLength?: number;
}

export interface DocumentProcessorNodeData {
  processorType: 'pdf' | 'text' | 'markdown' | 'html' | 'docx';
  extractionRules: Record<string, any>;
  outputFormat: string;
}

export interface EmbeddingGeneratorNodeData {
  modelName: string;
  dimensions: number;
  batchSize: number;
  configuration: Record<string, any>;
}

export interface VectorSearchNodeData {
  indexName: string;
  searchType: 'similarity' | 'hybrid' | 'keyword';
  topK: number;
  threshold: number;
}

export interface PromptTemplateNodeData {
  templateName: string;
  template: string;
  variables: string[];
  examples?: string[];
}

export interface ResponseParserNodeData {
  parserType: 'json' | 'xml' | 'regex' | 'custom';
  schema?: Record<string, any>;
  rules: Record<string, any>;
}

export interface ContextManagerNodeData {
  contextType: 'conversation' | 'session' | 'global';
  maxTokens: number;
  retentionPolicy: string;
}

export interface MemoryStoreNodeData {
  storeType: 'short_term' | 'long_term' | 'episodic' | 'semantic';
  capacity: number;
  indexing: string[];
}

export interface ToolExecutorNodeData {
  toolName: string;
  toolType: 'api' | 'function' | 'script' | 'external';
  parameters: Record<string, any>;
  timeout: number;
}

// Enhanced Node Components
export function AgentNode({ data, selected }: NodeProps<AgentNodeData>) {
  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'active': return 'green';
      case 'inactive': return 'gray';
      case 'error': return 'red';
      default: return 'blue';
    }
  };

  return (
    <motion.div
      initial={{ scale: 0.8, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      whileHover={{ scale: 1.02 }}
      className={`min-w-[200px] ${selected ? 'ring-2 ring-primary' : ''}`}
    >
      <Card className="shadow-lg">
        <CardHeader className="pb-2">
          <div className="flex items-center space-x-2">
            <Avatar className="h-8 w-8">
              <AvatarFallback className="bg-blue-100">
                <Bot className="h-4 w-4 text-blue-600" />
              </AvatarFallback>
            </Avatar>
            <div>
              <CardTitle className="text-sm">{data.agentName}</CardTitle>
              <p className="text-xs text-muted-foreground">{data.agentType}</p>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="flex items-center justify-between">
            <Badge variant="outline" className={`text-${getStatusColor(data.status)}-600`}>
              {data.status || 'ready'}
            </Badge>
            <div className="text-xs text-muted-foreground">
              Agent
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Handle type="target" position={Position.Left} className="w-3 h-3" />
      <Handle type="source" position={Position.Right} className="w-3 h-3" />
    </motion.div>
  );
}

export function KnowledgeEntityNode({ data, selected }: NodeProps<KnowledgeEntityNodeData>) {
  return (
    <motion.div
      initial={{ scale: 0.8, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      whileHover={{ scale: 1.02 }}
      className={`min-w-[180px] ${selected ? 'ring-2 ring-primary' : ''}`}
    >
      <Card className="shadow-lg">
        <CardHeader className="pb-2">
          <div className="flex items-center space-x-2">
            <Database className="h-5 w-5 text-purple-600" />
            <div>
              <CardTitle className="text-sm">{data.entityName}</CardTitle>
              <p className="text-xs text-muted-foreground">{data.entityType}</p>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="flex items-center justify-between">
            <Badge variant="outline" className="text-purple-600">
              Entity
            </Badge>
            {data.relationships && (
              <div className="text-xs text-muted-foreground">
                {data.relationships.length} links
              </div>
            )}
          </div>
        </CardContent>
      </Card>
      
      <Handle type="target" position={Position.Left} className="w-3 h-3" />
      <Handle type="source" position={Position.Right} className="w-3 h-3" />
    </motion.div>
  );
}

export function ModelInstanceNode({ data, selected }: NodeProps<ModelInstanceNodeData>) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'serving': return 'green';
      case 'loading': return 'yellow';
      case 'available': return 'blue';
      case 'error': return 'red';
      default: return 'gray';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'serving': return <Activity className="h-3 w-3" />;
      case 'loading': return <Cpu className="h-3 w-3 animate-pulse" />;
      case 'available': return <HardDrive className="h-3 w-3" />;
      case 'error': return <Zap className="h-3 w-3" />;
      default: return <Brain className="h-3 w-3" />;
    }
  };

  return (
    <motion.div
      initial={{ scale: 0.8, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      whileHover={{ scale: 1.02 }}
      className={`min-w-[200px] ${selected ? 'ring-2 ring-primary' : ''}`}
    >
      <Card className="shadow-lg">
        <CardHeader className="pb-2">
          <div className="flex items-center space-x-2">
            <Brain className="h-5 w-5 text-green-600" />
            <div>
              <CardTitle className="text-sm">{data.modelName}</CardTitle>
              <p className="text-xs text-muted-foreground">{data.modelId}</p>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="flex items-center justify-between">
            <Badge variant="outline" className={`text-${getStatusColor(data.status)}-600`}>
              <div className="flex items-center gap-1">
                {getStatusIcon(data.status)}
                {data.status}
              </div>
            </Badge>
            <div className="text-xs text-muted-foreground">
              Model
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Handle type="target" position={Position.Left} className="w-3 h-3" />
      <Handle type="source" position={Position.Right} className="w-3 h-3" />
    </motion.div>
  );
}

export function ChatInterfaceNode({ data, selected }: NodeProps<ChatInterfaceNodeData>) {
  const getInterfaceIcon = (type: string) => {
    switch (type) {
      case 'voice': return '🎤';
      case 'multimodal': return '🎭';
      default: return '💬';
    }
  };

  return (
    <motion.div
      initial={{ scale: 0.8, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      whileHover={{ scale: 1.02 }}
      className={`min-w-[180px] ${selected ? 'ring-2 ring-primary' : ''}`}
    >
      <Card className="shadow-lg">
        <CardHeader className="pb-2">
          <div className="flex items-center space-x-2">
            <MessageSquare className="h-5 w-5 text-blue-600" />
            <div>
              <CardTitle className="text-sm">Chat Interface</CardTitle>
              <p className="text-xs text-muted-foreground">{data.interfaceType}</p>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="flex items-center justify-between">
            <Badge variant="outline" className="text-blue-600">
              {getInterfaceIcon(data.interfaceType)} {data.interfaceType}
            </Badge>
            {data.contextLength && (
              <div className="text-xs text-muted-foreground">
                {data.contextLength} tokens
              </div>
            )}
          </div>
        </CardContent>
      </Card>
      
      <Handle type="target" position={Position.Left} className="w-3 h-3" />
      <Handle type="source" position={Position.Right} className="w-3 h-3" />
    </motion.div>
  );
}

export function DocumentProcessorNode({ data, selected }: NodeProps<DocumentProcessorNodeData>) {
  const getProcessorIcon = (type: string) => {
    switch (type) {
      case 'pdf': return '📄';
      case 'text': return '📝';
      case 'markdown': return '📋';
      case 'html': return '🌐';
      case 'docx': return '📄';
      default: return '📄';
    }
  };

  return (
    <motion.div
      initial={{ scale: 0.8, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      whileHover={{ scale: 1.02 }}
      className={`min-w-[180px] ${selected ? 'ring-2 ring-primary' : ''}`}
    >
      <Card className="shadow-lg">
        <CardHeader className="pb-2">
          <div className="flex items-center space-x-2">
            <FileText className="h-5 w-5 text-orange-600" />
            <div>
              <CardTitle className="text-sm">Document Processor</CardTitle>
              <p className="text-xs text-muted-foreground">{data.processorType}</p>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="flex items-center justify-between">
            <Badge variant="outline" className="text-orange-600">
              {getProcessorIcon(data.processorType)} {data.processorType}
            </Badge>
            <div className="text-xs text-muted-foreground">
              → {data.outputFormat}
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Handle type="target" position={Position.Left} className="w-3 h-3" />
      <Handle type="source" position={Position.Right} className="w-3 h-3" />
    </motion.div>
  );
}

export function VectorSearchNode({ data, selected }: NodeProps<VectorSearchNodeData>) {
  return (
    <motion.div
      initial={{ scale: 0.8, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      whileHover={{ scale: 1.02 }}
      className={`min-w-[180px] ${selected ? 'ring-2 ring-primary' : ''}`}
    >
      <Card className="shadow-lg">
        <CardHeader className="pb-2">
          <div className="flex items-center space-x-2">
            <Search className="h-5 w-5 text-indigo-600" />
            <div>
              <CardTitle className="text-sm">Vector Search</CardTitle>
              <p className="text-xs text-muted-foreground">{data.searchType}</p>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="flex items-center justify-between">
            <Badge variant="outline" className="text-indigo-600">
              Top {data.topK}
            </Badge>
            <div className="text-xs text-muted-foreground">
              {data.threshold}% threshold
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Handle type="target" position={Position.Left} className="w-3 h-3" />
      <Handle type="source" position={Position.Right} className="w-3 h-3" />
    </motion.div>
  );
}

export function PromptTemplateNode({ data, selected }: NodeProps<PromptTemplateNodeData>) {
  return (
    <motion.div
      initial={{ scale: 0.8, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      whileHover={{ scale: 1.02 }}
      className={`min-w-[180px] ${selected ? 'ring-2 ring-primary' : ''}`}
    >
      <Card className="shadow-lg">
        <CardHeader className="pb-2">
          <div className="flex items-center space-x-2">
            <Template className="h-5 w-5 text-pink-600" />
            <div>
              <CardTitle className="text-sm">{data.templateName}</CardTitle>
              <p className="text-xs text-muted-foreground">Prompt Template</p>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="flex items-center justify-between">
            <Badge variant="outline" className="text-pink-600">
              {data.variables.length} vars
            </Badge>
            {data.examples && (
              <div className="text-xs text-muted-foreground">
                {data.examples.length} examples
              </div>
            )}
          </div>
        </CardContent>
      </Card>
      
      <Handle type="target" position={Position.Left} className="w-3 h-3" />
      <Handle type="source" position={Position.Right} className="w-3 h-3" />
    </motion.div>
  );
}

export function ToolExecutorNode({ data, selected }: NodeProps<ToolExecutorNodeData>) {
  const getToolIcon = (type: string) => {
    switch (type) {
      case 'api': return '🌐';
      case 'function': return '⚡';
      case 'script': return '📜';
      case 'external': return '🔗';
      default: return '🔧';
    }
  };

  return (
    <motion.div
      initial={{ scale: 0.8, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      whileHover={{ scale: 1.02 }}
      className={`min-w-[180px] ${selected ? 'ring-2 ring-primary' : ''}`}
    >
      <Card className="shadow-lg">
        <CardHeader className="pb-2">
          <div className="flex items-center space-x-2">
            <Tool className="h-5 w-5 text-teal-600" />
            <div>
              <CardTitle className="text-sm">{data.toolName}</CardTitle>
              <p className="text-xs text-muted-foreground">{data.toolType}</p>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="flex items-center justify-between">
            <Badge variant="outline" className="text-teal-600">
              {getToolIcon(data.toolType)} {data.toolType}
            </Badge>
            <div className="text-xs text-muted-foreground">
              {data.timeout}s timeout
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Handle type="target" position={Position.Left} className="w-3 h-3" />
      <Handle type="source" position={Position.Right} className="w-3 h-3" />
    </motion.div>
  );
}

// Node type mapping for the flow builder
export const enhancedNodeTypes = {
  agent: AgentNode,
  knowledge_entity: KnowledgeEntityNode,
  model_instance: ModelInstanceNode,
  chat_interface: ChatInterfaceNode,
  document_processor: DocumentProcessorNode,
  vector_search: VectorSearchNode,
  prompt_template: PromptTemplateNode,
  tool_executor: ToolExecutorNode,
};

// Node configuration templates
export const nodeTemplates = {
  agent: {
    type: 'agent',
    data: {
      agentName: 'New Agent',
      agentType: 'conversational',
      configuration: {},
      status: 'inactive'
    }
  },
  knowledge_entity: {
    type: 'knowledge_entity',
    data: {
      entityName: 'New Entity',
      entityType: 'concept',
      properties: {},
      relationships: []
    }
  },
  model_instance: {
    type: 'model_instance',
    data: {
      modelId: '',
      modelName: 'Select Model',
      status: 'available',
      configuration: {}
    }
  },
  chat_interface: {
    type: 'chat_interface',
    data: {
      interfaceType: 'text',
      configuration: {},
      contextLength: 4096
    }
  },
  document_processor: {
    type: 'document_processor',
    data: {
      processorType: 'text',
      extractionRules: {},
      outputFormat: 'json'
    }
  },
  vector_search: {
    type: 'vector_search',
    data: {
      indexName: 'default',
      searchType: 'similarity',
      topK: 10,
      threshold: 0.7
    }
  },
  prompt_template: {
    type: 'prompt_template',
    data: {
      templateName: 'New Template',
      template: 'Hello {name}!',
      variables: ['name'],
      examples: []
    }
  },
  tool_executor: {
    type: 'tool_executor',
    data: {
      toolName: 'New Tool',
      toolType: 'function',
      parameters: {},
      timeout: 30
    }
  }
};
