'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Network, Database, Search, Plus, BarChart3, Settings } from 'lucide-react';
import { GraphVisualization } from '@/features/knowledge-graph/ui/graph-visualization';
import { EntityManagement } from '@/features/knowledge-graph/ui/entity-management';
import { useSubgraph, useKnowledgeEntities } from '@/entities/knowledge-graph/api';
import { KnowledgeEntity, KnowledgeRelationship } from '@/shared/types';
import { Button } from '@/shared/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/shared/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/shared/ui/dialog';
import { Badge } from '@/shared/ui/badge';
import { Input } from '@/shared/ui/input';
import { Label } from '@/shared/ui/label';
import { Textarea } from '@/shared/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/shared/ui/select';
import { useToast } from '@/shared/hooks/use-toast';

type ViewMode = 'visualization' | 'entities' | 'analytics';

export default function KnowledgeGraphPage() {
  const [viewMode, setViewMode] = useState<ViewMode>('visualization');
  const [selectedEntities, setSelectedEntities] = useState<string[]>([]);
  const [selectedEntity, setSelectedEntity] = useState<KnowledgeEntity | null>(null);
  const [selectedRelationship, setSelectedRelationship] = useState<KnowledgeRelationship | null>(null);
  const [showCreateEntity, setShowCreateEntity] = useState(false);
  const [showCreateRelationship, setShowCreateRelationship] = useState(false);
  const { toast } = useToast();

  // Fetch entities for the entity selector
  const { entities } = useKnowledgeEntities({ limit: 1000 });

  // Fetch subgraph for visualization
  const { graph, loading: graphLoading, error: graphError } = useSubgraph(
    selectedEntities.length > 0 ? selectedEntities : entities.slice(0, 20).map(e => e.id),
    2
  );

  const handleEntitySelect = (entity: KnowledgeEntity) => {
    setSelectedEntity(entity);
    
    // Add to selected entities if not already selected
    if (!selectedEntities.includes(entity.id)) {
      setSelectedEntities(prev => [...prev, entity.id]);
    }
  };

  const handleRelationshipSelect = (relationship: KnowledgeRelationship) => {
    setSelectedRelationship(relationship);
  };

  const handleCreateEntity = () => {
    setShowCreateEntity(true);
  };

  const handleCreateRelationship = () => {
    setShowCreateRelationship(true);
  };

  const clearSelection = () => {
    setSelectedEntities([]);
    setSelectedEntity(null);
    setSelectedRelationship(null);
  };

  // Calculate analytics
  const analytics = {
    totalEntities: entities.length,
    totalRelationships: graph?.relationships.length || 0,
    entityTypes: entities.reduce((acc, entity) => {
      acc[entity.entity_type] = (acc[entity.entity_type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
    avgConfidence: entities.length > 0 
      ? entities.reduce((sum, e) => sum + e.confidence_score, 0) / entities.length 
      : 0,
    publicEntities: entities.filter(e => e.is_public).length,
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-4xl font-bold tracking-tight">Knowledge Graph</h1>
          <p className="text-muted-foreground text-lg">
            Explore and visualize your knowledge network
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={handleCreateRelationship}>
            <Network className="h-4 w-4 mr-2" />
            Add Relationship
          </Button>
          <Button onClick={handleCreateEntity}>
            <Plus className="h-4 w-4 mr-2" />
            Create Entity
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Entities</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.totalEntities}</div>
            <p className="text-xs text-muted-foreground">
              {analytics.publicEntities} public
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Relationships</CardTitle>
            <Network className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.totalRelationships}</div>
            <p className="text-xs text-muted-foreground">
              In current view
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Confidence</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{(analytics.avgConfidence * 100).toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              Across all entities
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Entity Types</CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{Object.keys(analytics.entityTypes).length}</div>
            <p className="text-xs text-muted-foreground">
              Different types used
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Navigation Tabs */}
      <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as ViewMode)}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="visualization" className="flex items-center gap-2">
            <Network className="h-4 w-4" />
            Graph View
          </TabsTrigger>
          <TabsTrigger value="entities" className="flex items-center gap-2">
            <Database className="h-4 w-4" />
            Entity Management
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Analytics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="visualization" className="mt-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="h-[800px]"
          >
            {graph && !graphLoading ? (
              <GraphVisualization
                graph={graph}
                onEntitySelect={handleEntitySelect}
                onRelationshipSelect={handleRelationshipSelect}
                className="h-full"
              />
            ) : (
              <Card className="h-full flex items-center justify-center">
                <CardContent>
                  <div className="text-center">
                    {graphLoading ? (
                      <>
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                        <p>Loading knowledge graph...</p>
                      </>
                    ) : graphError ? (
                      <>
                        <p className="text-red-500 mb-4">Failed to load graph</p>
                        <Button onClick={() => window.location.reload()}>Retry</Button>
                      </>
                    ) : (
                      <>
                        <Network className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                        <h3 className="text-lg font-semibold mb-2">No entities to visualize</h3>
                        <p className="text-muted-foreground mb-4">Create some entities to see your knowledge graph</p>
                        <Button onClick={handleCreateEntity}>
                          <Plus className="h-4 w-4 mr-2" />
                          Create First Entity
                        </Button>
                      </>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}
          </motion.div>
        </TabsContent>

        <TabsContent value="entities" className="mt-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <EntityManagement
              onEntitySelect={handleEntitySelect}
              onCreateEntity={handleCreateEntity}
            />
          </motion.div>
        </TabsContent>

        <TabsContent value="analytics" className="mt-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <KnowledgeGraphAnalytics analytics={analytics} />
          </motion.div>
        </TabsContent>
      </Tabs>

      {/* Selection Info */}
      <AnimatePresence>
        {(selectedEntity || selectedRelationship || selectedEntities.length > 0) && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="fixed bottom-4 right-4 max-w-sm"
          >
            <Card>
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm">Selection</CardTitle>
                  <Button variant="ghost" size="sm" onClick={clearSelection}>
                    Clear
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-2">
                {selectedEntities.length > 0 && (
                  <div>
                    <Label className="text-xs">Selected Entities ({selectedEntities.length})</Label>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {selectedEntities.slice(0, 3).map(id => {
                        const entity = entities.find(e => e.id === id);
                        return entity ? (
                          <Badge key={id} variant="outline" className="text-xs">
                            {entity.name}
                          </Badge>
                        ) : null;
                      })}
                      {selectedEntities.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{selectedEntities.length - 3}
                        </Badge>
                      )}
                    </div>
                  </div>
                )}
                
                {selectedEntity && (
                  <div>
                    <Label className="text-xs">Active Entity</Label>
                    <div className="mt-1">
                      <Badge variant="default" className="text-xs">
                        {selectedEntity.name}
                      </Badge>
                    </div>
                  </div>
                )}
                
                {selectedRelationship && (
                  <div>
                    <Label className="text-xs">Active Relationship</Label>
                    <div className="mt-1">
                      <Badge variant="secondary" className="text-xs">
                        {selectedRelationship.relation_type}
                      </Badge>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Create Entity Dialog */}
      <Dialog open={showCreateEntity} onOpenChange={setShowCreateEntity}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Create Knowledge Entity</DialogTitle>
          </DialogHeader>
          <CreateEntityForm onClose={() => setShowCreateEntity(false)} />
        </DialogContent>
      </Dialog>

      {/* Create Relationship Dialog */}
      <Dialog open={showCreateRelationship} onOpenChange={setShowCreateRelationship}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Create Relationship</DialogTitle>
          </DialogHeader>
          <CreateRelationshipForm 
            entities={entities}
            onClose={() => setShowCreateRelationship(false)} 
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}

interface KnowledgeGraphAnalyticsProps {
  analytics: {
    totalEntities: number;
    totalRelationships: number;
    entityTypes: Record<string, number>;
    avgConfidence: number;
    publicEntities: number;
  };
}

function KnowledgeGraphAnalytics({ analytics }: KnowledgeGraphAnalyticsProps) {
  return (
    <div className="space-y-6">
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Entity Type Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(analytics.entityTypes).map(([type, count]) => (
                <div key={type} className="flex items-center justify-between">
                  <span className="text-sm font-medium">
                    {type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </span>
                  <div className="flex items-center space-x-2">
                    <div className="w-24 bg-muted rounded-full h-2">
                      <div 
                        className="bg-primary h-2 rounded-full" 
                        style={{ width: `${(count / analytics.totalEntities) * 100}%` }}
                      />
                    </div>
                    <span className="text-sm text-muted-foreground">{count}</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Graph Metrics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between">
                <span>Total Entities</span>
                <span className="font-medium">{analytics.totalEntities}</span>
              </div>
              <div className="flex justify-between">
                <span>Total Relationships</span>
                <span className="font-medium">{analytics.totalRelationships}</span>
              </div>
              <div className="flex justify-between">
                <span>Public Entities</span>
                <span className="font-medium">{analytics.publicEntities}</span>
              </div>
              <div className="flex justify-between">
                <span>Average Confidence</span>
                <span className="font-medium">{(analytics.avgConfidence * 100).toFixed(1)}%</span>
              </div>
              <div className="flex justify-between">
                <span>Graph Density</span>
                <span className="font-medium">
                  {analytics.totalEntities > 1 
                    ? ((analytics.totalRelationships / (analytics.totalEntities * (analytics.totalEntities - 1))) * 100).toFixed(2)
                    : 0}%
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            Activity tracking coming soon...
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Placeholder components for create forms
function CreateEntityForm({ onClose }: { onClose: () => void }) {
  return (
    <div className="space-y-4">
      <div>
        <Label>Entity Name</Label>
        <Input placeholder="Enter entity name" />
      </div>
      <div>
        <Label>Description</Label>
        <Textarea placeholder="Enter description" />
      </div>
      <div>
        <Label>Entity Type</Label>
        <Select>
          <SelectTrigger>
            <SelectValue placeholder="Select type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="concept">Concept</SelectItem>
            <SelectItem value="person">Person</SelectItem>
            <SelectItem value="organization">Organization</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div className="flex justify-end space-x-2">
        <Button variant="outline" onClick={onClose}>Cancel</Button>
        <Button onClick={onClose}>Create Entity</Button>
      </div>
    </div>
  );
}

function CreateRelationshipForm({ entities, onClose }: { entities: KnowledgeEntity[]; onClose: () => void }) {
  return (
    <div className="space-y-4">
      <div>
        <Label>Source Entity</Label>
        <Select>
          <SelectTrigger>
            <SelectValue placeholder="Select source entity" />
          </SelectTrigger>
          <SelectContent>
            {entities.slice(0, 10).map(entity => (
              <SelectItem key={entity.id} value={entity.id}>
                {entity.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      <div>
        <Label>Target Entity</Label>
        <Select>
          <SelectTrigger>
            <SelectValue placeholder="Select target entity" />
          </SelectTrigger>
          <SelectContent>
            {entities.slice(0, 10).map(entity => (
              <SelectItem key={entity.id} value={entity.id}>
                {entity.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      <div>
        <Label>Relationship Type</Label>
        <Select>
          <SelectTrigger>
            <SelectValue placeholder="Select relationship type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="related_to">Related To</SelectItem>
            <SelectItem value="part_of">Part Of</SelectItem>
            <SelectItem value="depends_on">Depends On</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div className="flex justify-end space-x-2">
        <Button variant="outline" onClick={onClose}>Cancel</Button>
        <Button onClick={onClose}>Create Relationship</Button>
      </div>
    </div>
  );
}
