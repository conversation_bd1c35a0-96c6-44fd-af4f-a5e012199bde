"""
Agent repository interface.

This module defines the repository interface for agent persistence operations.
"""

import uuid
from abc import ABC, abstractmethod
from typing import List, Optional

from src.domain.entities.agent import Agent, AgentStatus, AgentType


class AgentRepository(ABC):
    """Abstract repository interface for agent operations."""

    @abstractmethod
    async def create(self, agent: Agent) -> Agent:
        """
        Create a new agent.

        Args:
            agent: The agent to create

        Returns:
            The created agent with assigned ID

        Raises:
            RepositoryError: If creation fails
        """
        pass

    @abstractmethod
    async def get_by_id(self, agent_id: uuid.UUID) -> Optional[Agent]:
        """
        Get an agent by ID.

        Args:
            agent_id: The agent ID

        Returns:
            The agent if found, None otherwise

        Raises:
            RepositoryError: If retrieval fails
        """
        pass

    @abstractmethod
    async def get_by_name(self, name: str, user_id: uuid.UUID) -> Optional[Agent]:
        """
        Get an agent by name for a specific user.

        Args:
            name: The agent name
            user_id: The user ID

        Returns:
            The agent if found, None otherwise

        Raises:
            RepositoryError: If retrieval fails
        """
        pass

    @abstractmethod
    async def get_by_user(
        self,
        user_id: uuid.UUID,
        status: Optional[AgentStatus] = None,
        agent_type: Optional[AgentType] = None,
        limit: int = 100,
        offset: int = 0,
    ) -> List[Agent]:
        """
        Get agents by user ID with optional filtering.

        Args:
            user_id: The user ID
            status: Optional status filter
            agent_type: Optional type filter
            limit: Maximum number of agents to return
            offset: Number of agents to skip

        Returns:
            List of agents

        Raises:
            RepositoryError: If retrieval fails
        """
        pass

    @abstractmethod
    async def get_public_agents(
        self,
        agent_type: Optional[AgentType] = None,
        limit: int = 100,
        offset: int = 0,
    ) -> List[Agent]:
        """
        Get public agents with optional filtering.

        Args:
            agent_type: Optional type filter
            limit: Maximum number of agents to return
            offset: Number of agents to skip

        Returns:
            List of public agents

        Raises:
            RepositoryError: If retrieval fails
        """
        pass

    @abstractmethod
    async def get_by_capability(
        self,
        capability_name: str,
        user_id: Optional[uuid.UUID] = None,
        limit: int = 100,
        offset: int = 0,
    ) -> List[Agent]:
        """
        Get agents by capability.

        Args:
            capability_name: The capability name to search for
            user_id: Optional user ID filter
            limit: Maximum number of agents to return
            offset: Number of agents to skip

        Returns:
            List of agents with the specified capability

        Raises:
            RepositoryError: If retrieval fails
        """
        pass

    @abstractmethod
    async def search(
        self,
        query: str,
        user_id: Optional[uuid.UUID] = None,
        include_public: bool = True,
        limit: int = 100,
        offset: int = 0,
    ) -> List[Agent]:
        """
        Search agents by name, description, or tags.

        Args:
            query: Search query string
            user_id: Optional user ID filter
            include_public: Whether to include public agents
            limit: Maximum number of agents to return
            offset: Number of agents to skip

        Returns:
            List of matching agents

        Raises:
            RepositoryError: If search fails
        """
        pass

    @abstractmethod
    async def update(self, agent: Agent) -> Agent:
        """
        Update an existing agent.

        Args:
            agent: The agent to update

        Returns:
            The updated agent

        Raises:
            RepositoryError: If update fails
            NotFoundError: If agent not found
        """
        pass

    @abstractmethod
    async def delete(self, agent_id: uuid.UUID) -> bool:
        """
        Delete an agent.

        Args:
            agent_id: The agent ID to delete

        Returns:
            True if deleted, False if not found

        Raises:
            RepositoryError: If deletion fails
        """
        pass

    @abstractmethod
    async def get_active_agents(
        self,
        user_id: Optional[uuid.UUID] = None,
        limit: int = 100,
        offset: int = 0,
    ) -> List[Agent]:
        """
        Get all active agents.

        Args:
            user_id: Optional user ID filter
            limit: Maximum number of agents to return
            offset: Number of agents to skip

        Returns:
            List of active agents

        Raises:
            RepositoryError: If retrieval fails
        """
        pass

    @abstractmethod
    async def get_agents_by_parent(
        self,
        parent_agent_id: uuid.UUID,
        limit: int = 100,
        offset: int = 0,
    ) -> List[Agent]:
        """
        Get child agents by parent agent ID.

        Args:
            parent_agent_id: The parent agent ID
            limit: Maximum number of agents to return
            offset: Number of agents to skip

        Returns:
            List of child agents

        Raises:
            RepositoryError: If retrieval fails
        """
        pass

    @abstractmethod
    async def count_by_user(
        self,
        user_id: uuid.UUID,
        status: Optional[AgentStatus] = None,
        agent_type: Optional[AgentType] = None,
    ) -> int:
        """
        Count agents by user ID with optional filtering.

        Args:
            user_id: The user ID
            status: Optional status filter
            agent_type: Optional type filter

        Returns:
            Number of matching agents

        Raises:
            RepositoryError: If count fails
        """
        pass

    @abstractmethod
    async def get_agent_metrics_summary(
        self, user_id: uuid.UUID
    ) -> dict:
        """
        Get aggregated metrics for all user agents.

        Args:
            user_id: The user ID

        Returns:
            Dictionary with aggregated metrics

        Raises:
            RepositoryError: If retrieval fails
        """
        pass
