'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Search, Filter, Download, ExternalLink, Info, Cpu, HardDrive, Zap, Globe } from 'lucide-react';
import { useModelDiscovery, useModelSearch, getModelTypeIcon, getProviderIcon, formatBytes } from '@/entities/model-management/api';
import { ModelInfo, ModelType, ModelProvider } from '@/shared/types';
import { Button } from '@/shared/ui/button';
import { Input } from '@/shared/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/ui/card';
import { Badge } from '@/shared/ui/badge';
import { Avatar, AvatarFallback } from '@/shared/ui/avatar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/shared/ui/select';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/shared/ui/tabs';
import { Progress } from '@/shared/ui/progress';
import { Skeleton } from '@/shared/ui/skeleton';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/shared/ui/dialog';
import { useToast } from '@/shared/hooks/use-toast';

interface ModelDiscoveryProps {
  onModelSelect?: (model: ModelInfo) => void;
  onDownloadModel?: (model: ModelInfo) => void;
  className?: string;
}

export function ModelDiscovery({ onModelSelect, onDownloadModel, className }: ModelDiscoveryProps) {
  const [activeTab, setActiveTab] = useState<'huggingface' | 'ollama' | 'search'>('huggingface');
  const [searchQuery, setSearchQuery] = useState('');
  const [modelTypeFilter, setModelTypeFilter] = useState<ModelType | 'all'>('all');
  const [providerFilter, setProviderFilter] = useState<ModelProvider | 'all'>('all');
  const [selectedModel, setSelectedModel] = useState<ModelInfo | null>(null);
  const [showModelDetails, setShowModelDetails] = useState(false);
  const { toast } = useToast();

  // Fetch models from different providers
  const { models: hfModels, loading: hfLoading, error: hfError, refetch: refetchHf } = useModelDiscovery('huggingface', {
    limit: 20,
    model_type: modelTypeFilter === 'all' ? undefined : modelTypeFilter,
  });

  const { models: ollamaModels, loading: ollamaLoading, error: ollamaError, refetch: refetchOllama } = useModelDiscovery('ollama');

  const { models: searchResults, loading: searchLoading, error: searchError, search } = useModelSearch({
    query: searchQuery,
    model_type: modelTypeFilter === 'all' ? undefined : modelTypeFilter,
    provider: providerFilter === 'all' ? undefined : providerFilter,
    limit: 50,
  });

  const handleModelAction = (model: ModelInfo, action: 'view' | 'download') => {
    switch (action) {
      case 'view':
        setSelectedModel(model);
        setShowModelDetails(true);
        onModelSelect?.(model);
        break;
      case 'download':
        onDownloadModel?.(model);
        toast({
          title: 'Download Started',
          description: `Starting download for ${model.display_name}`,
        });
        break;
    }
  };

  const handleSearch = () => {
    if (searchQuery.trim()) {
      setActiveTab('search');
      search();
    }
  };

  const getCurrentModels = () => {
    switch (activeTab) {
      case 'huggingface':
        return hfModels;
      case 'ollama':
        return ollamaModels;
      case 'search':
        return searchResults;
      default:
        return [];
    }
  };

  const getCurrentLoading = () => {
    switch (activeTab) {
      case 'huggingface':
        return hfLoading;
      case 'ollama':
        return ollamaLoading;
      case 'search':
        return searchLoading;
      default:
        return false;
    }
  };

  const getCurrentError = () => {
    switch (activeTab) {
      case 'huggingface':
        return hfError;
      case 'ollama':
        return ollamaError;
      case 'search':
        return searchError;
      default:
        return null;
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Model Discovery</h1>
          <p className="text-muted-foreground">
            Discover and download AI models from various providers
          </p>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search models..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            className="pl-10"
          />
        </div>
        
        <Select value={modelTypeFilter} onValueChange={(value) => setModelTypeFilter(value as ModelType | 'all')}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Model Type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            {Object.values(ModelType).map(type => (
              <SelectItem key={type} value={type}>
                {getModelTypeIcon(type)} {type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={providerFilter} onValueChange={(value) => setProviderFilter(value as ModelProvider | 'all')}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Provider" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Providers</SelectItem>
            {Object.values(ModelProvider).map(provider => (
              <SelectItem key={provider} value={provider}>
                {getProviderIcon(provider)} {provider.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Button onClick={handleSearch} disabled={!searchQuery.trim()}>
          <Search className="h-4 w-4 mr-2" />
          Search
        </Button>
      </div>

      {/* Provider Tabs */}
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as typeof activeTab)}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="huggingface" className="flex items-center gap-2">
            🤗 HuggingFace
          </TabsTrigger>
          <TabsTrigger value="ollama" className="flex items-center gap-2">
            🦙 Ollama
          </TabsTrigger>
          <TabsTrigger value="search" className="flex items-center gap-2">
            <Search className="h-4 w-4" />
            Search Results
          </TabsTrigger>
        </TabsList>

        <TabsContent value="huggingface" className="mt-6">
          <ModelGrid
            models={hfModels}
            loading={hfLoading}
            error={hfError}
            onModelAction={handleModelAction}
            onRefresh={refetchHf}
          />
        </TabsContent>

        <TabsContent value="ollama" className="mt-6">
          <ModelGrid
            models={ollamaModels}
            loading={ollamaLoading}
            error={ollamaError}
            onModelAction={handleModelAction}
            onRefresh={refetchOllama}
          />
        </TabsContent>

        <TabsContent value="search" className="mt-6">
          <ModelGrid
            models={searchResults}
            loading={searchLoading}
            error={searchError}
            onModelAction={handleModelAction}
            onRefresh={search}
            emptyMessage={searchQuery ? `No models found for "${searchQuery}"` : 'Enter a search query to find models'}
          />
        </TabsContent>
      </Tabs>

      {/* Model Details Dialog */}
      <Dialog open={showModelDetails} onOpenChange={setShowModelDetails}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Model Details</DialogTitle>
          </DialogHeader>
          {selectedModel && (
            <ModelDetails model={selectedModel} onDownload={() => handleModelAction(selectedModel, 'download')} />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}

interface ModelGridProps {
  models: ModelInfo[];
  loading: boolean;
  error: string | null;
  onModelAction: (model: ModelInfo, action: 'view' | 'download') => void;
  onRefresh: () => void;
  emptyMessage?: string;
}

function ModelGrid({ models, loading, error, onModelAction, onRefresh, emptyMessage }: ModelGridProps) {
  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-red-500 mb-4">{error}</p>
          <Button onClick={onRefresh}>Retry</Button>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {Array.from({ length: 6 }).map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <div className="flex items-center space-x-4">
                <Skeleton className="h-12 w-12 rounded-full" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-[200px]" />
                  <Skeleton className="h-4 w-[160px]" />
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (models.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="mx-auto w-24 h-24 bg-muted rounded-full flex items-center justify-center mb-4">
          <Search className="h-8 w-8 text-muted-foreground" />
        </div>
        <h3 className="text-lg font-semibold mb-2">No models found</h3>
        <p className="text-muted-foreground mb-4">
          {emptyMessage || 'No models available from this provider'}
        </p>
        <Button onClick={onRefresh}>Refresh</Button>
      </div>
    );
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      <AnimatePresence>
        {models.map((model) => (
          <motion.div
            key={model.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.2 }}
          >
            <ModelCard model={model} onAction={onModelAction} />
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
}

interface ModelCardProps {
  model: ModelInfo;
  onAction: (model: ModelInfo, action: 'view' | 'download') => void;
}

function ModelCard({ model, onAction }: ModelCardProps) {
  return (
    <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => onAction(model, 'view')}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Avatar>
              <AvatarFallback className="bg-primary/10 text-lg">
                {getModelTypeIcon(model.model_type)}
              </AvatarFallback>
            </Avatar>
            <div>
              <CardTitle className="text-base">{model.display_name}</CardTitle>
              <p className="text-sm text-muted-foreground flex items-center gap-1">
                {getProviderIcon(model.provider)} {model.provider}
              </p>
            </div>
          </div>
          
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={(e) => {
              e.stopPropagation();
              onAction(model, 'download');
            }}
          >
            <Download className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Badge variant="outline">
              {model.model_type.replace('_', ' ')}
            </Badge>
            <span className="text-sm text-muted-foreground">
              v{model.version}
            </span>
          </div>
          
          {model.description && (
            <p className="text-sm text-muted-foreground line-clamp-2">
              {model.description}
            </p>
          )}
          
          <div className="flex items-center justify-between text-sm">
            {model.size_bytes && (
              <div className="flex items-center gap-1">
                <HardDrive className="h-3 w-3" />
                {model.size_display}
              </div>
            )}
            {model.parameter_count && (
              <div className="flex items-center gap-1">
                <Cpu className="h-3 w-3" />
                {model.parameter_display}
              </div>
            )}
            {model.gpu_required && (
              <div className="flex items-center gap-1">
                <Zap className="h-3 w-3 text-yellow-500" />
                GPU
              </div>
            )}
          </div>
          
          <div className="flex flex-wrap gap-1">
            {model.tags.slice(0, 3).map((tag) => (
              <Badge key={tag} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
            {model.tags.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{model.tags.length - 3}
              </Badge>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

interface ModelDetailsProps {
  model: ModelInfo;
  onDownload: () => void;
}

function ModelDetails({ model, onDownload }: ModelDetailsProps) {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Avatar className="h-16 w-16">
            <AvatarFallback className="bg-primary/10 text-2xl">
              {getModelTypeIcon(model.model_type)}
            </AvatarFallback>
          </Avatar>
          <div>
            <h2 className="text-2xl font-bold">{model.display_name}</h2>
            <p className="text-muted-foreground flex items-center gap-2">
              {getProviderIcon(model.provider)} {model.provider} • v{model.version}
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {model.homepage_url && (
            <Button variant="outline" asChild>
              <a href={model.homepage_url} target="_blank" rel="noopener noreferrer">
                <ExternalLink className="h-4 w-4 mr-2" />
                Homepage
              </a>
            </Button>
          )}
          <Button onClick={onDownload}>
            <Download className="h-4 w-4 mr-2" />
            Download
          </Button>
        </div>
      </div>

      {model.description && (
        <div>
          <h3 className="text-lg font-semibold mb-2">Description</h3>
          <p className="text-muted-foreground">{model.description}</p>
        </div>
      )}

      <div className="grid grid-cols-2 gap-6">
        <div>
          <h3 className="text-lg font-semibold mb-3">Specifications</h3>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>Model Type:</span>
              <Badge variant="outline">{model.model_type.replace('_', ' ')}</Badge>
            </div>
            {model.size_bytes && (
              <div className="flex justify-between">
                <span>Size:</span>
                <span>{model.size_display}</span>
              </div>
            )}
            {model.parameter_count && (
              <div className="flex justify-between">
                <span>Parameters:</span>
                <span>{model.parameter_display}</span>
              </div>
            )}
            {model.context_length && (
              <div className="flex justify-between">
                <span>Context Length:</span>
                <span>{model.context_length.toLocaleString()}</span>
              </div>
            )}
          </div>
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-3">Requirements</h3>
          <div className="space-y-2">
            {model.min_ram_gb && (
              <div className="flex justify-between">
                <span>Min RAM:</span>
                <span>{model.min_ram_gb} GB</span>
              </div>
            )}
            {model.min_vram_gb && (
              <div className="flex justify-between">
                <span>Min VRAM:</span>
                <span>{model.min_vram_gb} GB</span>
              </div>
            )}
            <div className="flex justify-between">
              <span>GPU Required:</span>
              <span>{model.gpu_required ? 'Yes' : 'No'}</span>
            </div>
            {model.license && (
              <div className="flex justify-between">
                <span>License:</span>
                <span>{model.license}</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {model.capabilities.length > 0 && (
        <div>
          <h3 className="text-lg font-semibold mb-3">Capabilities</h3>
          <div className="flex flex-wrap gap-2">
            {model.capabilities.map((capability) => (
              <Badge key={capability} variant="secondary">
                {capability}
              </Badge>
            ))}
          </div>
        </div>
      )}

      {model.languages.length > 0 && (
        <div>
          <h3 className="text-lg font-semibold mb-3">Supported Languages</h3>
          <div className="flex flex-wrap gap-2">
            {model.languages.map((language) => (
              <Badge key={language} variant="outline">
                <Globe className="h-3 w-3 mr-1" />
                {language}
              </Badge>
            ))}
          </div>
        </div>
      )}

      {model.tags.length > 0 && (
        <div>
          <h3 className="text-lg font-semibold mb-3">Tags</h3>
          <div className="flex flex-wrap gap-2">
            {model.tags.map((tag) => (
              <Badge key={tag} variant="outline">
                {tag}
              </Badge>
            ))}
          </div>
        </div>
      )}

      <div className="text-sm text-muted-foreground">
        <div>Created: {new Date(model.createdAt).toLocaleDateString()}</div>
        <div>Updated: {new Date(model.updatedAt).toLocaleDateString()}</div>
      </div>
    </div>
  );
}
