'use client';

import React, { useState, useCallback, useRef, useMemo } from 'react';
import React<PERSON>low, {
  Node,
  Edge,
  addEdge,
  useNodesState,
  useEdgesState,
  Controls,
  MiniMap,
  Background,
  BackgroundVariant,
  Connection,
  ReactFlowProvider,
  Panel,
  useReactFlow,
} from 'reactflow';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Play, 
  Save, 
  Download, 
  Upload, 
  Settings, 
  Zap, 
  Eye, 
  EyeOff,
  Maximize2,
  Minimize2,
  RotateCcw,
  CheckCircle,
  AlertCircle,
  Clock
} from 'lucide-react';
import { enhancedNodeTypes } from './enhanced-nodes';
import { EnhancedNodeLibrary } from './enhanced-node-library';
import { Button } from '@/shared/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/ui/card';
import { Badge } from '@/shared/ui/badge';
import { Separator } from '@/shared/ui/separator';
import { useToast } from '@/shared/hooks/use-toast';

import 'reactflow/dist/style.css';

interface EnhancedFlowBuilderProps {
  workflowId?: string;
  initialNodes?: Node[];
  initialEdges?: Edge[];
  onSave?: (nodes: Node[], edges: Edge[]) => void;
  onExecute?: (nodes: Node[], edges: Edge[]) => void;
  className?: string;
}

interface ValidationError {
  nodeId?: string;
  edgeId?: string;
  type: 'error' | 'warning';
  message: string;
}

export function EnhancedFlowBuilder({
  workflowId,
  initialNodes = [],
  initialEdges = [],
  onSave,
  onExecute,
  className
}: EnhancedFlowBuilderProps) {
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [isLibraryVisible, setIsLibraryVisible] = useState(true);
  const [isValidating, setIsValidating] = useState(false);
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>([]);
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionStatus, setExecutionStatus] = useState<'idle' | 'running' | 'completed' | 'error'>('idle');
  
  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const { toast } = useToast();

  // Combine standard and enhanced node types
  const allNodeTypes = useMemo(() => ({
    ...enhancedNodeTypes,
  }), []);

  const onConnect = useCallback(
    (params: Connection) => {
      const newEdge = {
        ...params,
        id: `edge-${Date.now()}`,
        type: 'smoothstep',
        animated: true,
      };
      setEdges((eds) => addEdge(newEdge, eds));
    },
    [setEdges]
  );

  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();

      const reactFlowBounds = reactFlowWrapper.current?.getBoundingClientRect();
      if (!reactFlowBounds) return;

      const type = event.dataTransfer.getData('application/reactflow/type');
      const nodeData = JSON.parse(event.dataTransfer.getData('application/reactflow/data') || '{}');

      if (!type) return;

      const position = {
        x: event.clientX - reactFlowBounds.left - 100,
        y: event.clientY - reactFlowBounds.top - 50,
      };

      const newNode: Node = {
        id: `${type}-${Date.now()}`,
        type,
        position,
        data: nodeData,
      };

      setNodes((nds) => nds.concat(newNode));
    },
    [setNodes]
  );

  const onNodeDragStart = useCallback((event: React.DragEvent, nodeType: string, nodeData: any) => {
    event.dataTransfer.setData('application/reactflow/type', nodeType);
    event.dataTransfer.setData('application/reactflow/data', JSON.stringify(nodeData));
    event.dataTransfer.effectAllowed = 'move';
  }, []);

  const validateWorkflow = useCallback(() => {
    setIsValidating(true);
    const errors: ValidationError[] = [];

    // Check for input nodes
    const inputNodes = nodes.filter(node => node.type === 'input' || node.data.isInput);
    if (inputNodes.length === 0) {
      errors.push({
        type: 'error',
        message: 'Workflow must have at least one input node'
      });
    }

    // Check for output nodes
    const outputNodes = nodes.filter(node => node.type === 'output' || node.data.isOutput);
    if (outputNodes.length === 0) {
      errors.push({
        type: 'error',
        message: 'Workflow must have at least one output node'
      });
    }

    // Check for orphaned nodes
    const connectedNodeIds = new Set<string>();
    edges.forEach(edge => {
      connectedNodeIds.add(edge.source);
      connectedNodeIds.add(edge.target);
    });

    const orphanedNodes = nodes.filter(node => 
      !connectedNodeIds.has(node.id) && nodes.length > 1
    );

    orphanedNodes.forEach(node => {
      errors.push({
        nodeId: node.id,
        type: 'warning',
        message: `Node "${node.data.label || node.id}" is not connected`
      });
    });

    // Check for invalid edge connections
    edges.forEach(edge => {
      const sourceNode = nodes.find(n => n.id === edge.source);
      const targetNode = nodes.find(n => n.id === edge.target);

      if (!sourceNode || !targetNode) {
        errors.push({
          edgeId: edge.id,
          type: 'error',
          message: 'Edge connects to non-existent node'
        });
      }
    });

    // Check for model instance configurations
    const modelNodes = nodes.filter(node => node.type === 'model_instance');
    modelNodes.forEach(node => {
      if (!node.data.modelId) {
        errors.push({
          nodeId: node.id,
          type: 'error',
          message: `Model instance "${node.data.modelName || node.id}" requires a model selection`
        });
      }
    });

    // Check for agent configurations
    const agentNodes = nodes.filter(node => node.type === 'agent');
    agentNodes.forEach(node => {
      if (!node.data.agentId && !node.data.agentName) {
        errors.push({
          nodeId: node.id,
          type: 'error',
          message: `Agent node requires configuration`
        });
      }
    });

    setValidationErrors(errors);
    setIsValidating(false);

    return errors.filter(e => e.type === 'error').length === 0;
  }, [nodes, edges]);

  const handleSave = useCallback(async () => {
    try {
      if (validateWorkflow()) {
        await onSave?.(nodes, edges);
        toast({
          title: 'Workflow Saved',
          description: 'Your workflow has been saved successfully.',
        });
      } else {
        toast({
          title: 'Validation Failed',
          description: 'Please fix the validation errors before saving.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Save Failed',
        description: 'Failed to save workflow. Please try again.',
        variant: 'destructive',
      });
    }
  }, [nodes, edges, onSave, validateWorkflow, toast]);

  const handleExecute = useCallback(async () => {
    if (!validateWorkflow()) {
      toast({
        title: 'Validation Failed',
        description: 'Please fix the validation errors before executing.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsExecuting(true);
      setExecutionStatus('running');
      
      await onExecute?.(nodes, edges);
      
      setExecutionStatus('completed');
      toast({
        title: 'Execution Started',
        description: 'Your workflow is now executing.',
      });
    } catch (error) {
      setExecutionStatus('error');
      toast({
        title: 'Execution Failed',
        description: 'Failed to execute workflow. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsExecuting(false);
    }
  }, [nodes, edges, onExecute, validateWorkflow, toast]);

  const getExecutionStatusIcon = () => {
    switch (executionStatus) {
      case 'running':
        return <Clock className="h-4 w-4 animate-pulse text-blue-500" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  const getExecutionStatusColor = () => {
    switch (executionStatus) {
      case 'running': return 'blue';
      case 'completed': return 'green';
      case 'error': return 'red';
      default: return 'gray';
    }
  };

  return (
    <div className={`h-full flex ${className}`}>
      {/* Node Library Sidebar */}
      <AnimatePresence>
        {isLibraryVisible && (
          <motion.div
            initial={{ width: 0, opacity: 0 }}
            animate={{ width: 320, opacity: 1 }}
            exit={{ width: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="border-r bg-background overflow-hidden"
          >
            <EnhancedNodeLibrary onNodeDragStart={onNodeDragStart} />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Flow Canvas */}
      <div className="flex-1 flex flex-col">
        {/* Toolbar */}
        <div className="border-b p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsLibraryVisible(!isLibraryVisible)}
              >
                {isLibraryVisible ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                {isLibraryVisible ? 'Hide' : 'Show'} Library
              </Button>
              
              <Separator orientation="vertical" className="h-6" />
              
              <Button variant="outline" size="sm" onClick={validateWorkflow}>
                <CheckCircle className="h-4 w-4 mr-2" />
                Validate
              </Button>
              
              <Button variant="outline" size="sm" onClick={handleSave}>
                <Save className="h-4 w-4 mr-2" />
                Save
              </Button>
            </div>

            <div className="flex items-center space-x-2">
              {/* Execution Status */}
              {executionStatus !== 'idle' && (
                <Badge variant="outline" className={`text-${getExecutionStatusColor()}-600`}>
                  <div className="flex items-center gap-1">
                    {getExecutionStatusIcon()}
                    {executionStatus}
                  </div>
                </Badge>
              )}

              {/* Validation Status */}
              {validationErrors.length > 0 && (
                <Badge variant="outline" className="text-red-600">
                  {validationErrors.filter(e => e.type === 'error').length} errors,{' '}
                  {validationErrors.filter(e => e.type === 'warning').length} warnings
                </Badge>
              )}

              <Button 
                onClick={handleExecute} 
                disabled={isExecuting || validationErrors.some(e => e.type === 'error')}
                className="bg-green-600 hover:bg-green-700"
              >
                <Play className="h-4 w-4 mr-2" />
                {isExecuting ? 'Executing...' : 'Execute'}
              </Button>
            </div>
          </div>
        </div>

        {/* Flow Canvas */}
        <div className="flex-1" ref={reactFlowWrapper}>
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            onDrop={onDrop}
            onDragOver={onDragOver}
            nodeTypes={allNodeTypes}
            fitView
            attributionPosition="bottom-left"
          >
            <Controls />
            <MiniMap />
            <Background variant={BackgroundVariant.Dots} gap={12} size={1} />
            
            {/* Validation Panel */}
            {validationErrors.length > 0 && (
              <Panel position="top-right" className="bg-background border rounded-lg shadow-lg p-4 max-w-sm">
                <h3 className="font-semibold mb-2">Validation Results</h3>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {validationErrors.map((error, index) => (
                    <div key={index} className={`text-sm p-2 rounded ${
                      error.type === 'error' ? 'bg-red-50 text-red-700' : 'bg-yellow-50 text-yellow-700'
                    }`}>
                      <div className="flex items-center gap-1">
                        {error.type === 'error' ? 
                          <AlertCircle className="h-3 w-3" /> : 
                          <Clock className="h-3 w-3" />
                        }
                        {error.message}
                      </div>
                    </div>
                  ))}
                </div>
              </Panel>
            )}
          </ReactFlow>
        </div>
      </div>
    </div>
  );
}

// Wrapper component with ReactFlowProvider
export function EnhancedFlowBuilderWrapper(props: EnhancedFlowBuilderProps) {
  return (
    <ReactFlowProvider>
      <EnhancedFlowBuilder {...props} />
    </ReactFlowProvider>
  );
}
