'use client';

import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Search, 
  Bot, 
  Database, 
  MessageSquare, 
  FileText, 
  Brain, 
  Link, 
  Template, 
  Tool, 
  Zap,
  Filter,
  Plus,
  Grid3X3,
  List
} from 'lucide-react';
import { Input } from '@/shared/ui/input';
import { Button } from '@/shared/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/ui/card';
import { Badge } from '@/shared/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/shared/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/shared/ui/select';
import { nodeTemplates } from './enhanced-nodes';

interface NodeLibraryProps {
  onNodeDragStart: (event: React.DragEvent, nodeType: string, nodeData: any) => void;
  className?: string;
}

interface NodeCategory {
  id: string;
  name: string;
  icon: React.ReactNode;
  description: string;
  nodes: NodeDefinition[];
}

interface NodeDefinition {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  category: string;
  tags: string[];
  template: any;
  complexity: 'simple' | 'moderate' | 'complex';
  isNew?: boolean;
  isPopular?: boolean;
}

const nodeDefinitions: NodeDefinition[] = [
  // AI Agents
  {
    id: 'agent',
    name: 'AI Agent',
    description: 'Intelligent agent that can perform tasks and make decisions',
    icon: <Bot className="h-4 w-4" />,
    category: 'agents',
    tags: ['ai', 'agent', 'intelligence', 'automation'],
    template: nodeTemplates.agent,
    complexity: 'moderate',
    isPopular: true,
  },
  {
    id: 'chat_interface',
    name: 'Chat Interface',
    description: 'Interactive chat interface for user communication',
    icon: <MessageSquare className="h-4 w-4" />,
    category: 'interfaces',
    tags: ['chat', 'interface', 'communication', 'user'],
    template: nodeTemplates.chat_interface,
    complexity: 'simple',
    isPopular: true,
  },
  
  // Knowledge Management
  {
    id: 'knowledge_entity',
    name: 'Knowledge Entity',
    description: 'Represents a knowledge graph entity with properties and relationships',
    icon: <Database className="h-4 w-4" />,
    category: 'knowledge',
    tags: ['knowledge', 'entity', 'graph', 'data'],
    template: nodeTemplates.knowledge_entity,
    complexity: 'moderate',
  },
  {
    id: 'vector_search',
    name: 'Vector Search',
    description: 'Semantic search using vector embeddings',
    icon: <Search className="h-4 w-4" />,
    category: 'knowledge',
    tags: ['search', 'vector', 'semantic', 'embeddings'],
    template: nodeTemplates.vector_search,
    complexity: 'complex',
    isNew: true,
  },
  
  // Models
  {
    id: 'model_instance',
    name: 'Model Instance',
    description: 'AI model instance for inference and generation',
    icon: <Brain className="h-4 w-4" />,
    category: 'models',
    tags: ['model', 'ai', 'inference', 'generation'],
    template: nodeTemplates.model_instance,
    complexity: 'moderate',
    isPopular: true,
  },
  {
    id: 'prompt_template',
    name: 'Prompt Template',
    description: 'Reusable prompt template with variables',
    icon: <Template className="h-4 w-4" />,
    category: 'models',
    tags: ['prompt', 'template', 'variables', 'generation'],
    template: nodeTemplates.prompt_template,
    complexity: 'simple',
  },
  
  // Processing
  {
    id: 'document_processor',
    name: 'Document Processor',
    description: 'Process and extract content from documents',
    icon: <FileText className="h-4 w-4" />,
    category: 'processing',
    tags: ['document', 'processing', 'extraction', 'content'],
    template: nodeTemplates.document_processor,
    complexity: 'moderate',
  },
  {
    id: 'tool_executor',
    name: 'Tool Executor',
    description: 'Execute external tools and functions',
    icon: <Tool className="h-4 w-4" />,
    category: 'processing',
    tags: ['tool', 'executor', 'function', 'external'],
    template: nodeTemplates.tool_executor,
    complexity: 'complex',
  },
];

const categories: NodeCategory[] = [
  {
    id: 'all',
    name: 'All Nodes',
    icon: <Grid3X3 className="h-4 w-4" />,
    description: 'All available nodes',
    nodes: nodeDefinitions,
  },
  {
    id: 'agents',
    name: 'AI Agents',
    icon: <Bot className="h-4 w-4" />,
    description: 'Intelligent agents and automation',
    nodes: nodeDefinitions.filter(node => node.category === 'agents'),
  },
  {
    id: 'knowledge',
    name: 'Knowledge',
    icon: <Database className="h-4 w-4" />,
    description: 'Knowledge management and search',
    nodes: nodeDefinitions.filter(node => node.category === 'knowledge'),
  },
  {
    id: 'models',
    name: 'AI Models',
    icon: <Brain className="h-4 w-4" />,
    description: 'AI models and templates',
    nodes: nodeDefinitions.filter(node => node.category === 'models'),
  },
  {
    id: 'interfaces',
    name: 'Interfaces',
    icon: <MessageSquare className="h-4 w-4" />,
    description: 'User interfaces and communication',
    nodes: nodeDefinitions.filter(node => node.category === 'interfaces'),
  },
  {
    id: 'processing',
    name: 'Processing',
    icon: <FileText className="h-4 w-4" />,
    description: 'Data processing and transformation',
    nodes: nodeDefinitions.filter(node => node.category === 'processing'),
  },
];

export function EnhancedNodeLibrary({ onNodeDragStart, className }: NodeLibraryProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [complexityFilter, setComplexityFilter] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  const filteredNodes = useMemo(() => {
    const category = categories.find(cat => cat.id === selectedCategory);
    if (!category) return [];

    let nodes = category.nodes;

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      nodes = nodes.filter(node => 
        node.name.toLowerCase().includes(query) ||
        node.description.toLowerCase().includes(query) ||
        node.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    // Apply complexity filter
    if (complexityFilter !== 'all') {
      nodes = nodes.filter(node => node.complexity === complexityFilter);
    }

    return nodes;
  }, [selectedCategory, searchQuery, complexityFilter]);

  const handleDragStart = (event: React.DragEvent, node: NodeDefinition) => {
    onNodeDragStart(event, node.id, node.template.data);
  };

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'simple': return 'green';
      case 'moderate': return 'yellow';
      case 'complex': return 'red';
      default: return 'gray';
    }
  };

  return (
    <div className={`h-full flex flex-col ${className}`}>
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold">Node Library</h2>
          <div className="flex items-center space-x-2">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('grid')}
            >
              <Grid3X3 className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Search */}
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search nodes..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Filters */}
        <div className="flex items-center space-x-2">
          <Select value={complexityFilter} onValueChange={setComplexityFilter}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="Complexity" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Levels</SelectItem>
              <SelectItem value="simple">Simple</SelectItem>
              <SelectItem value="moderate">Moderate</SelectItem>
              <SelectItem value="complex">Complex</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Categories */}
      <Tabs value={selectedCategory} onValueChange={setSelectedCategory} className="flex-1 flex flex-col">
        <TabsList className="grid grid-cols-3 lg:grid-cols-6 mx-4 mt-2">
          {categories.map((category) => (
            <TabsTrigger key={category.id} value={category.id} className="text-xs">
              <div className="flex items-center space-x-1">
                {category.icon}
                <span className="hidden sm:inline">{category.name}</span>
              </div>
            </TabsTrigger>
          ))}
        </TabsList>

        {categories.map((category) => (
          <TabsContent key={category.id} value={category.id} className="flex-1 p-4">
            <div className="mb-4">
              <h3 className="font-medium">{category.name}</h3>
              <p className="text-sm text-muted-foreground">{category.description}</p>
              <div className="flex items-center space-x-2 mt-2">
                <Badge variant="outline">{filteredNodes.length} nodes</Badge>
                {filteredNodes.some(node => node.isNew) && (
                  <Badge variant="secondary">New</Badge>
                )}
                {filteredNodes.some(node => node.isPopular) && (
                  <Badge variant="secondary">Popular</Badge>
                )}
              </div>
            </div>

            {/* Node Grid/List */}
            <div className="flex-1 overflow-y-auto">
              {filteredNodes.length === 0 ? (
                <div className="text-center py-8">
                  <div className="text-muted-foreground mb-2">No nodes found</div>
                  <p className="text-sm text-muted-foreground">
                    Try adjusting your search or filters
                  </p>
                </div>
              ) : (
                <div className={
                  viewMode === 'grid' 
                    ? 'grid grid-cols-1 lg:grid-cols-2 gap-3'
                    : 'space-y-2'
                }>
                  <AnimatePresence>
                    {filteredNodes.map((node) => (
                      <motion.div
                        key={node.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        transition={{ duration: 0.2 }}
                      >
                        {viewMode === 'grid' ? (
                          <NodeCard node={node} onDragStart={handleDragStart} />
                        ) : (
                          <NodeListItem node={node} onDragStart={handleDragStart} />
                        )}
                      </motion.div>
                    ))}
                  </AnimatePresence>
                </div>
              )}
            </div>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
}

interface NodeCardProps {
  node: NodeDefinition;
  onDragStart: (event: React.DragEvent, node: NodeDefinition) => void;
}

function NodeCard({ node, onDragStart }: NodeCardProps) {
  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'simple': return 'green';
      case 'moderate': return 'yellow';
      case 'complex': return 'red';
      default: return 'gray';
    }
  };

  return (
    <Card 
      className="cursor-grab hover:shadow-md transition-shadow active:cursor-grabbing"
      draggable
      onDragStart={(e) => onDragStart(e, node)}
    >
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="p-1 rounded bg-primary/10">
              {node.icon}
            </div>
            <div>
              <CardTitle className="text-sm">{node.name}</CardTitle>
              <div className="flex items-center space-x-1">
                <Badge 
                  variant="outline" 
                  className={`text-${getComplexityColor(node.complexity)}-600 text-xs`}
                >
                  {node.complexity}
                </Badge>
                {node.isNew && <Badge variant="secondary" className="text-xs">New</Badge>}
                {node.isPopular && <Badge variant="secondary" className="text-xs">Popular</Badge>}
              </div>
            </div>
          </div>
          <Plus className="h-4 w-4 text-muted-foreground" />
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <p className="text-xs text-muted-foreground line-clamp-2">
          {node.description}
        </p>
        <div className="flex flex-wrap gap-1 mt-2">
          {node.tags.slice(0, 3).map((tag) => (
            <Badge key={tag} variant="outline" className="text-xs">
              {tag}
            </Badge>
          ))}
          {node.tags.length > 3 && (
            <Badge variant="outline" className="text-xs">
              +{node.tags.length - 3}
            </Badge>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

function NodeListItem({ node, onDragStart }: NodeCardProps) {
  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'simple': return 'green';
      case 'moderate': return 'yellow';
      case 'complex': return 'red';
      default: return 'gray';
    }
  };

  return (
    <div 
      className="flex items-center space-x-3 p-3 rounded-lg border hover:bg-muted/50 cursor-grab active:cursor-grabbing"
      draggable
      onDragStart={(e) => onDragStart(e, node)}
    >
      <div className="p-2 rounded bg-primary/10">
        {node.icon}
      </div>
      <div className="flex-1 min-w-0">
        <div className="flex items-center space-x-2">
          <h4 className="font-medium text-sm">{node.name}</h4>
          <Badge 
            variant="outline" 
            className={`text-${getComplexityColor(node.complexity)}-600 text-xs`}
          >
            {node.complexity}
          </Badge>
          {node.isNew && <Badge variant="secondary" className="text-xs">New</Badge>}
          {node.isPopular && <Badge variant="secondary" className="text-xs">Popular</Badge>}
        </div>
        <p className="text-xs text-muted-foreground truncate">
          {node.description}
        </p>
      </div>
      <Plus className="h-4 w-4 text-muted-foreground flex-shrink-0" />
    </div>
  );
}
