"""
Enhanced Authentication Dependencies Tests

Comprehensive test suite for the enhanced authentication system
with permission and role checking capabilities.
"""

import uuid
from unittest.mock import AsyncMock, patch

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from src.domain.entities.user import Permission, User, UserRole, UserStatus
from src.domain.value_objects.email import Email
from src.domain.value_objects.username import Username
from src.presentation.dependencies.auth import (
    AuthorizationError,
    require_permissions,
    require_roles,
)


@pytest.fixture
def mock_db_session():
    """Mock database session."""
    return AsyncMock(spec=AsyncSession)


@pytest.fixture
def sample_user_id():
    """Sample user ID for testing."""
    return uuid.uuid4()


@pytest.fixture
def sample_current_user(sample_user_id):
    """Sample current user dict from JWT."""
    return {
        "user_id": str(sample_user_id),
        "subject": "<EMAIL>",
        "token_id": "test-token-id",
    }


@pytest.fixture
def admin_user(sample_user_id):
    """Create admin user entity."""
    return User(
        id=str(sample_user_id),
        email=Email("<EMAIL>"),
        username=Username("admin"),
        full_name="Admin User",
        hashed_password="hashed_password",
        role=UserRole.ADMIN,
        status=UserStatus.ACTIVE,
        is_active=True,
        is_verified=True,
    )


@pytest.fixture
def regular_user(sample_user_id):
    """Create regular user entity."""
    return User(
        id=str(sample_user_id),
        email=Email("<EMAIL>"),
        username=Username("user"),
        full_name="Regular User",
        hashed_password="hashed_password",
        role=UserRole.USER,
        status=UserStatus.ACTIVE,
        is_active=True,
        is_verified=True,
    )


@pytest.fixture
def viewer_user(sample_user_id):
    """Create viewer user entity."""
    return User(
        id=str(sample_user_id),
        email=Email("<EMAIL>"),
        username=Username("viewer"),
        full_name="Viewer User",
        hashed_password="hashed_password",
        role=UserRole.VIEWER,
        status=UserStatus.ACTIVE,
        is_active=True,
        is_verified=True,
    )


@pytest.fixture
def inactive_user(sample_user_id):
    """Create inactive user entity."""
    return User(
        id=str(sample_user_id),
        email=Email("<EMAIL>"),
        username=Username("inactive"),
        full_name="Inactive User",
        hashed_password="hashed_password",
        role=UserRole.USER,
        status=UserStatus.INACTIVE,
        is_active=False,
        is_verified=True,
    )


class TestPermissionChecking:
    """Test suite for permission checking functionality."""

    @pytest.mark.asyncio
    @patch("src.infrastructure.database.repositories.user_repository.UserRepository")
    async def test_permission_check_success_admin(
        self, mock_user_repo_class, mock_db_session, sample_current_user, admin_user
    ):
        """Test successful permission check for admin user."""
        # Setup mock
        mock_user_repo = AsyncMock()
        mock_user_repo.get_by_id.return_value = admin_user
        mock_user_repo_class.return_value = mock_user_repo

        # Create permission dependency
        permission_dep = require_permissions(Permission.WORKFLOW_CREATE.value)

        # Execute
        result = await permission_dep(sample_current_user, mock_db_session)

        # Verify
        assert result["user_id"] == sample_current_user["user_id"]
        assert result["user_entity"] == admin_user
        mock_user_repo.get_by_id.assert_called_once()

    @pytest.mark.asyncio
    @patch("src.infrastructure.database.repositories.user_repository.UserRepository")
    async def test_permission_check_success_regular_user(
        self, mock_user_repo_class, mock_db_session, sample_current_user, regular_user
    ):
        """Test successful permission check for regular user with valid permission."""
        # Setup mock
        mock_user_repo = AsyncMock()
        mock_user_repo.get_by_id.return_value = regular_user
        mock_user_repo_class.return_value = mock_user_repo

        # Create permission dependency for permission that regular user has
        permission_dep = require_permissions(Permission.WORKFLOW_READ.value)

        # Execute
        result = await permission_dep(sample_current_user, mock_db_session)

        # Verify
        assert result["user_id"] == sample_current_user["user_id"]
        assert result["user_entity"] == regular_user

    @pytest.mark.asyncio
    @patch("src.infrastructure.database.repositories.user_repository.UserRepository")
    async def test_permission_check_failure_insufficient_permissions(
        self, mock_user_repo_class, mock_db_session, sample_current_user, viewer_user
    ):
        """Test permission check failure for user with insufficient permissions."""
        # Setup mock
        mock_user_repo = AsyncMock()
        mock_user_repo.get_by_id.return_value = viewer_user
        mock_user_repo_class.return_value = mock_user_repo

        # Create permission dependency for permission that viewer doesn't have
        permission_dep = require_permissions(Permission.WORKFLOW_CREATE.value)

        # Execute and verify exception
        with pytest.raises(AuthorizationError) as exc_info:
            await permission_dep(sample_current_user, mock_db_session)

        assert "Insufficient permissions" in str(exc_info.value.detail)

    @pytest.mark.asyncio
    @patch("src.infrastructure.database.repositories.user_repository.UserRepository")
    async def test_permission_check_failure_user_not_found(
        self, mock_user_repo_class, mock_db_session, sample_current_user
    ):
        """Test permission check failure when user not found in database."""
        # Setup mock
        mock_user_repo = AsyncMock()
        mock_user_repo.get_by_id.return_value = None
        mock_user_repo_class.return_value = mock_user_repo

        # Create permission dependency
        permission_dep = require_permissions(Permission.WORKFLOW_READ.value)

        # Execute and verify exception
        with pytest.raises(AuthorizationError) as exc_info:
            await permission_dep(sample_current_user, mock_db_session)

        assert "User not found" in str(exc_info.value.detail)

    @pytest.mark.asyncio
    @patch("src.infrastructure.database.repositories.user_repository.UserRepository")
    async def test_permission_check_failure_inactive_user(
        self, mock_user_repo_class, mock_db_session, sample_current_user, inactive_user
    ):
        """Test permission check failure for inactive user."""
        # Setup mock
        mock_user_repo = AsyncMock()
        mock_user_repo.get_by_id.return_value = inactive_user
        mock_user_repo_class.return_value = mock_user_repo

        # Create permission dependency
        permission_dep = require_permissions(Permission.WORKFLOW_READ.value)

        # Execute and verify exception
        with pytest.raises(AuthorizationError) as exc_info:
            await permission_dep(sample_current_user, mock_db_session)

        assert "User account is inactive" in str(exc_info.value.detail)

    @pytest.mark.asyncio
    @patch("src.infrastructure.database.repositories.user_repository.UserRepository")
    async def test_permission_check_invalid_permission_string(
        self, mock_user_repo_class, mock_db_session, sample_current_user, admin_user
    ):
        """Test permission check with invalid permission string."""
        # Setup mock
        mock_user_repo = AsyncMock()
        mock_user_repo.get_by_id.return_value = admin_user
        mock_user_repo_class.return_value = mock_user_repo

        # Create permission dependency with invalid permission
        permission_dep = require_permissions("invalid:permission")

        # Execute and verify exception
        with pytest.raises(AuthorizationError) as exc_info:
            await permission_dep(sample_current_user, mock_db_session)

        assert "Invalid permission" in str(exc_info.value.detail)

    @pytest.mark.asyncio
    @patch("src.infrastructure.database.repositories.user_repository.UserRepository")
    async def test_permission_check_multiple_permissions_success(
        self, mock_user_repo_class, mock_db_session, sample_current_user, admin_user
    ):
        """Test permission check with multiple permissions for admin user."""
        # Setup mock
        mock_user_repo = AsyncMock()
        mock_user_repo.get_by_id.return_value = admin_user
        mock_user_repo_class.return_value = mock_user_repo

        # Create permission dependency with multiple permissions
        permission_dep = require_permissions(
            Permission.WORKFLOW_CREATE.value,
            Permission.WORKFLOW_DELETE.value,
            Permission.USER_MANAGE.value,
        )

        # Execute
        result = await permission_dep(sample_current_user, mock_db_session)

        # Verify
        assert result["user_id"] == sample_current_user["user_id"]
        assert result["user_entity"] == admin_user

    @pytest.mark.asyncio
    @patch("src.infrastructure.database.repositories.user_repository.UserRepository")
    async def test_permission_check_multiple_permissions_failure(
        self, mock_user_repo_class, mock_db_session, sample_current_user, regular_user
    ):
        """Test permission check failure with multiple permissions for regular user."""
        # Setup mock
        mock_user_repo = AsyncMock()
        mock_user_repo.get_by_id.return_value = regular_user
        mock_user_repo_class.return_value = mock_user_repo

        # Create permission dependency with permissions regular user doesn't have
        permission_dep = require_permissions(
            Permission.WORKFLOW_CREATE.value,
            Permission.USER_MANAGE.value,  # Regular user doesn't have this
        )

        # Execute and verify exception
        with pytest.raises(AuthorizationError) as exc_info:
            await permission_dep(sample_current_user, mock_db_session)

        assert "Insufficient permissions" in str(exc_info.value.detail)


class TestRoleChecking:
    """Test suite for role checking functionality."""

    @pytest.mark.asyncio
    @patch("src.infrastructure.database.repositories.user_repository.UserRepository")
    async def test_role_check_success_exact_match(
        self, mock_user_repo_class, mock_db_session, sample_current_user, regular_user
    ):
        """Test successful role check with exact role match."""
        # Setup mock
        mock_user_repo = AsyncMock()
        mock_user_repo.get_by_id.return_value = regular_user
        mock_user_repo_class.return_value = mock_user_repo

        # Create role dependency
        role_dep = require_roles(UserRole.USER.value)

        # Execute
        result = await role_dep(sample_current_user, mock_db_session)

        # Verify
        assert result["user_id"] == sample_current_user["user_id"]
        assert result["user_entity"] == regular_user

    @pytest.mark.asyncio
    @patch("src.infrastructure.database.repositories.user_repository.UserRepository")
    async def test_role_check_success_admin_hierarchy(
        self, mock_user_repo_class, mock_db_session, sample_current_user, admin_user
    ):
        """Test successful role check with admin accessing user-level resource."""
        # Setup mock
        mock_user_repo = AsyncMock()
        mock_user_repo.get_by_id.return_value = admin_user
        mock_user_repo_class.return_value = mock_user_repo

        # Create role dependency for user role (admin should have access)
        role_dep = require_roles(UserRole.USER.value)

        # Execute
        result = await role_dep(sample_current_user, mock_db_session)

        # Verify - Admin should always pass
        assert result["user_id"] == sample_current_user["user_id"]
        assert result["user_entity"] == admin_user

    @pytest.mark.asyncio
    @patch("src.infrastructure.database.repositories.user_repository.UserRepository")
    async def test_role_check_failure_insufficient_role(
        self, mock_user_repo_class, mock_db_session, sample_current_user, viewer_user
    ):
        """Test role check failure for user with insufficient role."""
        # Setup mock
        mock_user_repo = AsyncMock()
        mock_user_repo.get_by_id.return_value = viewer_user
        mock_user_repo_class.return_value = mock_user_repo

        # Create role dependency for moderator role (viewer doesn't have)
        role_dep = require_roles(UserRole.MODERATOR.value)

        # Execute and verify exception
        with pytest.raises(AuthorizationError) as exc_info:
            await role_dep(sample_current_user, mock_db_session)

        assert "Insufficient role" in str(exc_info.value.detail)

    @pytest.mark.asyncio
    @patch("src.infrastructure.database.repositories.user_repository.UserRepository")
    async def test_role_check_multiple_roles_success(
        self, mock_user_repo_class, mock_db_session, sample_current_user, regular_user
    ):
        """Test role check with multiple allowed roles."""
        # Setup mock
        mock_user_repo = AsyncMock()
        mock_user_repo.get_by_id.return_value = regular_user
        mock_user_repo_class.return_value = mock_user_repo

        # Create role dependency with multiple roles (user should match one)
        role_dep = require_roles(UserRole.USER.value, UserRole.MODERATOR.value)

        # Execute
        result = await role_dep(sample_current_user, mock_db_session)

        # Verify
        assert result["user_id"] == sample_current_user["user_id"]
        assert result["user_entity"] == regular_user

    @pytest.mark.asyncio
    @patch("src.infrastructure.database.repositories.user_repository.UserRepository")
    async def test_role_check_invalid_role_string(
        self, mock_user_repo_class, mock_db_session, sample_current_user, admin_user
    ):
        """Test role check with invalid role string."""
        # Setup mock
        mock_user_repo = AsyncMock()
        mock_user_repo.get_by_id.return_value = admin_user
        mock_user_repo_class.return_value = mock_user_repo

        # Create role dependency with invalid role
        role_dep = require_roles("invalid_role")

        # Execute and verify exception
        with pytest.raises(AuthorizationError) as exc_info:
            await role_dep(sample_current_user, mock_db_session)

        assert "Invalid role" in str(exc_info.value.detail)

    @pytest.mark.asyncio
    @patch("src.infrastructure.database.repositories.user_repository.UserRepository")
    async def test_role_check_user_not_found(
        self, mock_user_repo_class, mock_db_session, sample_current_user
    ):
        """Test role check failure when user not found."""
        # Setup mock
        mock_user_repo = AsyncMock()
        mock_user_repo.get_by_id.return_value = None
        mock_user_repo_class.return_value = mock_user_repo

        # Create role dependency
        role_dep = require_roles(UserRole.USER.value)

        # Execute and verify exception
        with pytest.raises(AuthorizationError) as exc_info:
            await role_dep(sample_current_user, mock_db_session)

        assert "User not found" in str(exc_info.value.detail)

    @pytest.mark.asyncio
    @patch("src.infrastructure.database.repositories.user_repository.UserRepository")
    async def test_role_check_inactive_user(
        self, mock_user_repo_class, mock_db_session, sample_current_user, inactive_user
    ):
        """Test role check failure for inactive user."""
        # Setup mock
        mock_user_repo = AsyncMock()
        mock_user_repo.get_by_id.return_value = inactive_user
        mock_user_repo_class.return_value = mock_user_repo

        # Create role dependency
        role_dep = require_roles(UserRole.USER.value)

        # Execute and verify exception
        with pytest.raises(AuthorizationError) as exc_info:
            await role_dep(sample_current_user, mock_db_session)

        assert "User account is inactive" in str(exc_info.value.detail)


class TestPerformanceAndSecurity:
    """Test suite for performance and security aspects."""

    @pytest.mark.asyncio
    @patch("src.infrastructure.database.repositories.user_repository.UserRepository")
    async def test_permission_check_performance_timing(
        self, mock_user_repo_class, mock_db_session, sample_current_user, admin_user
    ):
        """Test that permission checks complete within performance requirements."""
        import time

        # Setup mock
        mock_user_repo = AsyncMock()
        mock_user_repo.get_by_id.return_value = admin_user
        mock_user_repo_class.return_value = mock_user_repo

        # Create permission dependency
        permission_dep = require_permissions(Permission.WORKFLOW_READ.value)

        # Measure execution time
        start_time = time.time()
        await permission_dep(sample_current_user, mock_db_session)
        end_time = time.time()

        # Verify performance requirement (<200ms)
        execution_time = end_time - start_time
        assert execution_time < 0.2, (
            f"Permission check took {execution_time:.3f}s, should be <0.2s"
        )

    @pytest.mark.asyncio
    @patch("src.infrastructure.database.repositories.user_repository.UserRepository")
    async def test_role_check_performance_timing(
        self, mock_user_repo_class, mock_db_session, sample_current_user, admin_user
    ):
        """Test that role checks complete within performance requirements."""
        import time

        # Setup mock
        mock_user_repo = AsyncMock()
        mock_user_repo.get_by_id.return_value = admin_user
        mock_user_repo_class.return_value = mock_user_repo

        # Create role dependency
        role_dep = require_roles(UserRole.ADMIN.value)

        # Measure execution time
        start_time = time.time()
        await role_dep(sample_current_user, mock_db_session)
        end_time = time.time()

        # Verify performance requirement (<200ms)
        execution_time = end_time - start_time
        assert execution_time < 0.2, (
            f"Role check took {execution_time:.3f}s, should be <0.2s"
        )

    @pytest.mark.asyncio
    @patch("src.infrastructure.database.repositories.user_repository.UserRepository")
    async def test_security_logging_on_failure(
        self,
        mock_user_repo_class,
        mock_db_session,
        sample_current_user,
        viewer_user,
        caplog,
    ):
        """Test that security failures are properly logged for auditing."""
        # Setup mock
        mock_user_repo = AsyncMock()
        mock_user_repo.get_by_id.return_value = viewer_user
        mock_user_repo_class.return_value = mock_user_repo

        # Create permission dependency that will fail
        permission_dep = require_permissions(Permission.USER_MANAGE.value)

        # Execute and expect failure
        with pytest.raises(AuthorizationError):
            await permission_dep(sample_current_user, mock_db_session)

        # Verify security logging
        assert "lacks permissions" in caplog.text
        assert sample_current_user["user_id"] in caplog.text

    @pytest.mark.asyncio
    @patch("src.infrastructure.database.repositories.user_repository.UserRepository")
    async def test_database_error_handling(
        self, mock_user_repo_class, mock_db_session, sample_current_user
    ):
        """Test proper error handling when database operations fail."""
        # Setup mock to raise database error
        mock_user_repo = AsyncMock()
        mock_user_repo.get_by_id.side_effect = Exception("Database connection failed")
        mock_user_repo_class.return_value = mock_user_repo

        # Create permission dependency
        permission_dep = require_permissions(Permission.WORKFLOW_READ.value)

        # Execute and verify proper error handling
        with pytest.raises(AuthorizationError) as exc_info:
            await permission_dep(sample_current_user, mock_db_session)

        assert "Permission check failed" in str(exc_info.value.detail)


class TestIntegrationScenarios:
    """Test suite for integration scenarios."""

    @pytest.mark.asyncio
    @patch("src.infrastructure.database.repositories.user_repository.UserRepository")
    async def test_workflow_creation_permission_flow(
        self, mock_user_repo_class, mock_db_session, sample_current_user, regular_user
    ):
        """Test complete workflow creation permission flow."""
        # Setup mock
        mock_user_repo = AsyncMock()
        mock_user_repo.get_by_id.return_value = regular_user
        mock_user_repo_class.return_value = mock_user_repo

        # Test workflow creation permission (regular user should have this)
        permission_dep = require_permissions(Permission.WORKFLOW_CREATE.value)
        result = await permission_dep(sample_current_user, mock_db_session)

        assert result["user_entity"].role == UserRole.USER
        assert result["user_entity"].has_permission(Permission.WORKFLOW_CREATE)

    @pytest.mark.asyncio
    @patch("src.infrastructure.database.repositories.user_repository.UserRepository")
    async def test_admin_panel_access_flow(
        self, mock_user_repo_class, mock_db_session, sample_current_user, admin_user
    ):
        """Test admin panel access flow with multiple checks."""
        # Setup mock
        mock_user_repo = AsyncMock()
        mock_user_repo.get_by_id.return_value = admin_user
        mock_user_repo_class.return_value = mock_user_repo

        # Test admin role requirement
        role_dep = require_roles(UserRole.ADMIN.value)
        role_result = await role_dep(sample_current_user, mock_db_session)

        # Test system admin permission
        permission_dep = require_permissions(Permission.SYSTEM_ADMIN.value)
        perm_result = await permission_dep(sample_current_user, mock_db_session)

        assert role_result["user_entity"].role == UserRole.ADMIN
        assert perm_result["user_entity"].has_permission(Permission.SYSTEM_ADMIN)
