import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

// Mock the useAnime hook first
const mockUseAnime = vi.fn();
vi.mock('@/shared/lib/hooks/useAnime', () => ({
  useAnime: mockUseAnime,
}));

// Mock the animations module
vi.mock('@/shared/lib/animations', () => ({
  ANIMATION_PRESETS: {
    agentThinking: {
      scale: [1, 1.02, 1],
      opacity: [0.8, 1, 0.8],
      duration: 2000,
      loop: true
    },
    agentSuccess: {
      scale: [1, 1.2, 1],
      opacity: [0.8, 1],
      duration: 600
    },
    nodeEnter: {
      scale: [0, 1],
      opacity: [0, 1],
      duration: 600
    },
    workflowStart: {
      scale: [0.8, 1],
      opacity: [0, 1],
      duration: 800
    },
    statusIndicatorPulse: {
      scale: [1, 1.2, 1],
      opacity: [1, 0.8, 1],
      duration: 1000,
      loop: true
    },
  },
}));

// Mock anime.js
const mockAnime = vi.fn();
vi.mock('animejs', () => ({
  default: mockAnime,
}));

// Import the component after mocks
import {
    AGENT_ANIMATION_PRESETS,
    EnhancedAnimationPresets,
    FLOW_BUILDER_ANIMATION_PRESETS,
    WORKFLOW_ANIMATION_PRESETS,
    getAnimationPresetsByCategory
} from '../enhanced-animation-presets';

describe('EnhancedAnimationPresets', () => {
  beforeEach(() => {
    mockUseAnime.mockReturnValue({ current: null });
    mockAnime.mockReturnValue({
      pause: vi.fn(),
      play: vi.fn(),
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('renders children correctly', () => {
      render(
        <EnhancedAnimationPresets animationPreset="agentThinking">
          <div data-testid="child">Test Content</div>
        </EnhancedAnimationPresets>
      );

      expect(screen.getByTestId('child')).toBeInTheDocument();
      expect(screen.getByText('Test Content')).toBeInTheDocument();
    });

    it('applies custom className', () => {
      render(
        <EnhancedAnimationPresets
          animationPreset="agentThinking"
          className="custom-class"
          data-testid="animation-container"
        >
          <div>Content</div>
        </EnhancedAnimationPresets>
      );

      const container = screen.getByTestId('animation-container');
      expect(container).toHaveClass('custom-class');
    });

    it('forwards additional props', () => {
      render(
        <EnhancedAnimationPresets
          animationPreset="agentThinking"
          data-testid="animation-container"
          role="region"
          aria-label="Animation region"
        >
          <div>Content</div>
        </EnhancedAnimationPresets>
      );

      const container = screen.getByTestId('animation-container');
      expect(container).toHaveAttribute('role', 'region');
      expect(container).toHaveAttribute('aria-label', 'Animation region');
    });
  });

  describe('Animation Configuration', () => {
    it('uses correct animation preset for agent animations', () => {
      render(
        <EnhancedAnimationPresets animationPreset="agentThinking">
          <div>Agent Content</div>
        </EnhancedAnimationPresets>
      );

      expect(mockUseAnime).toHaveBeenCalledWith(
        expect.objectContaining({
          scale: [1, 1.02, 1],
          opacity: [0.8, 1, 0.8],
          duration: 2000,
          loop: true,
        }),
        expect.any(Array)
      );
    });

    it('merges custom animation config with preset', () => {
      render(
        <EnhancedAnimationPresets
          animationPreset="agentSuccess"
          animationConfig={{ duration: 1000 }}
        >
          <div>Content</div>
        </EnhancedAnimationPresets>
      );

      expect(mockUseAnime).toHaveBeenCalledWith(
        expect.objectContaining({
          scale: [1, 1.2, 1],
          opacity: [0.8, 1],
          duration: 1000, // Custom duration should override preset
        }),
        expect.any(Array)
      );
    });

    it('handles loop configuration correctly', () => {
      render(
        <EnhancedAnimationPresets
          animationPreset="agentSuccess"
          loop={true}
        >
          <div>Content</div>
        </EnhancedAnimationPresets>
      );

      expect(mockUseAnime).toHaveBeenCalledWith(
        expect.objectContaining({
          loop: true,
        }),
        expect.any(Array)
      );
    });

    it('handles autoplay configuration correctly', () => {
      render(
        <EnhancedAnimationPresets
          animationPreset="agentSuccess"
          autoplay={false}
        >
          <div>Content</div>
        </EnhancedAnimationPresets>
      );

      expect(mockUseAnime).toHaveBeenCalledWith(
        expect.objectContaining({
          autoplay: false,
        }),
        expect.any(Array)
      );
    });
  });

  describe('Trigger Modes', () => {
    it('handles mount trigger (default)', () => {
      render(
        <EnhancedAnimationPresets animationPreset="nodeEnter">
          <div>Content</div>
        </EnhancedAnimationPresets>
      );

      // Should call useAnime for mount trigger
      expect(mockUseAnime).toHaveBeenCalled();
    });

    it('handles hover trigger', async () => {
      render(
        <EnhancedAnimationPresets
          animationPreset="nodeEnter"
          trigger="hover"
          data-testid="hover-container"
        >
          <div>Content</div>
        </EnhancedAnimationPresets>
      );

      const container = screen.getByTestId('hover-container');

      // Should not call useAnime for non-mount triggers initially
      expect(mockUseAnime).toHaveBeenCalledWith({}, expect.any(Array));

      // Simulate hover
      fireEvent.mouseEnter(container);

      await waitFor(() => {
        expect(mockAnime).toHaveBeenCalled();
      });
    });

    it('handles click trigger', async () => {
      render(
        <EnhancedAnimationPresets
          animationPreset="nodeEnter"
          trigger="click"
          data-testid="click-container"
        >
          <div>Content</div>
        </EnhancedAnimationPresets>
      );

      const container = screen.getByTestId('click-container');

      fireEvent.click(container);

      await waitFor(() => {
        expect(mockAnime).toHaveBeenCalled();
      });
    });

    it('handles focus trigger', async () => {
      render(
        <EnhancedAnimationPresets
          animationPreset="nodeEnter"
          trigger="focus"
          data-testid="focus-container"
          tabIndex={0}
        >
          <div>Content</div>
        </EnhancedAnimationPresets>
      );

      const container = screen.getByTestId('focus-container');

      fireEvent.focus(container);

      await waitFor(() => {
        expect(mockAnime).toHaveBeenCalled();
      });
    });

    it('handles manual trigger', () => {
      render(
        <EnhancedAnimationPresets
          animationPreset="nodeEnter"
          trigger="manual"
          data-testid="manual-container"
        >
          <div>Content</div>
        </EnhancedAnimationPresets>
      );

      const container = screen.getByTestId('manual-container');

      // Manual trigger should not respond to events
      fireEvent.click(container);
      fireEvent.mouseEnter(container);
      fireEvent.focus(container);

      expect(mockAnime).not.toHaveBeenCalled();
    });
  });

  describe('Disabled State', () => {
    it('does not trigger animations when disabled', () => {
      render(
        <EnhancedAnimationPresets
          animationPreset="agentThinking"
          disabled={true}
          data-testid="disabled-container"
        >
          <div>Content</div>
        </EnhancedAnimationPresets>
      );

      // Should call useAnime with empty config when disabled
      expect(mockUseAnime).toHaveBeenCalledWith({}, expect.any(Array));
    });

    it('does not respond to interactions when disabled', () => {
      render(
        <EnhancedAnimationPresets
          animationPreset="nodeEnter"
          trigger="click"
          disabled={true}
          data-testid="disabled-click-container"
        >
          <div>Content</div>
        </EnhancedAnimationPresets>
      );

      const container = screen.getByTestId('disabled-click-container');
      fireEvent.click(container);

      expect(mockAnime).not.toHaveBeenCalled();
    });
  });

  describe('Callbacks', () => {
    it('calls onAnimationStart callback', () => {
      const onStart = vi.fn();

      render(
        <EnhancedAnimationPresets
          animationPreset="agentSuccess"
          onAnimationStart={onStart}
        >
          <div>Content</div>
        </EnhancedAnimationPresets>
      );

      expect(mockUseAnime).toHaveBeenCalledWith(
        expect.objectContaining({
          begin: onStart,
        }),
        expect.any(Array)
      );
    });

    it('calls onAnimationComplete callback', () => {
      const onComplete = vi.fn();

      render(
        <EnhancedAnimationPresets
          animationPreset="agentSuccess"
          onAnimationComplete={onComplete}
        >
          <div>Content</div>
        </EnhancedAnimationPresets>
      );

      expect(mockUseAnime).toHaveBeenCalledWith(
        expect.objectContaining({
          complete: onComplete,
        }),
        expect.any(Array)
      );
    });
  });

  describe('Error Handling', () => {
    it('handles invalid animation preset gracefully', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

      render(
        <EnhancedAnimationPresets
          animationPreset={'invalidPreset' as any}
        >
          <div>Content</div>
        </EnhancedAnimationPresets>
      );

      expect(consoleSpy).toHaveBeenCalledWith(
        'Animation preset "invalidPreset" not found'
      );

      // Should fallback to default animation
      expect(mockUseAnime).toHaveBeenCalledWith(
        expect.objectContaining({
          opacity: [0, 1],
          duration: 300,
        }),
        expect.any(Array)
      );

      consoleSpy.mockRestore();
    });
  });
});

describe('Animation Preset Utilities', () => {
  describe('Preset Arrays', () => {
    it('exports correct agent animation presets', () => {
      expect(AGENT_ANIMATION_PRESETS).toEqual([
        'agentThinking',
        'agentProcessing',
        'agentSuccess',
        'agentError',
        'agentWarning',
        'agentActivate',
        'agentDeactivate',
        'agentStatusChange',
      ]);
    });

    it('exports correct flow builder animation presets', () => {
      expect(FLOW_BUILDER_ANIMATION_PRESETS).toEqual([
        'nodeEnter',
        'nodeExit',
        'nodeHover',
        'nodeUnhover',
        'nodeSelect',
        'nodeDeselect',
        'connectionDraw',
        'connectionPulse',
      ]);
    });

    it('exports correct workflow animation presets', () => {
      expect(WORKFLOW_ANIMATION_PRESETS).toEqual([
        'workflowStart',
        'workflowComplete',
        'workflowError',
      ]);
    });
  });

  describe('getAnimationPresetsByCategory', () => {
    it('returns agent presets for agent category', () => {
      expect(getAnimationPresetsByCategory('agent')).toEqual(AGENT_ANIMATION_PRESETS);
    });

    it('returns flow presets for flow category', () => {
      expect(getAnimationPresetsByCategory('flow')).toEqual(FLOW_BUILDER_ANIMATION_PRESETS);
    });

    it('returns workflow presets for workflow category', () => {
      expect(getAnimationPresetsByCategory('workflow')).toEqual(WORKFLOW_ANIMATION_PRESETS);
    });

    it('returns status presets for status category', () => {
      expect(getAnimationPresetsByCategory('status')).toEqual(['statusIndicatorPulse']);
    });

    it('returns model presets for model category', () => {
      expect(getAnimationPresetsByCategory('model')).toEqual(['modelLoading']);
    });

    it('returns card presets for card category', () => {
      expect(getAnimationPresetsByCategory('card')).toEqual(['cardHoverAgent']);
    });

    it('returns empty array for invalid category', () => {
      expect(getAnimationPresetsByCategory('invalid' as any)).toEqual([]);
    });
  });
});
