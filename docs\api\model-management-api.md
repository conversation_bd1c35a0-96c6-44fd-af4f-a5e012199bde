# Model Management API Documentation

## Overview

The Model Management API provides comprehensive functionality for discovering, downloading, serving, and monitoring AI models from various providers including HuggingFace and Ollama.

## Base URL
```
https://api.lonors.com/v1
```

## Authentication
All API endpoints require authentication via Bearer token:
```
Authorization: Bearer <your-jwt-token>
```

## Model Discovery Endpoints

### Discover HuggingFace Models
Discover available models from HuggingFace Hub.

```http
GET /models/discover/huggingface
```

**Query Parameters:**
- `query` (string, optional): Search query for model names/descriptions
- `model_type` (string, optional): Filter by model type (language_model, image_model, etc.)
- `limit` (integer, optional, default: 20): Maximum number of models to return
- `offset` (integer, optional, default: 0): Number of models to skip

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "microsoft/DialoGPT-medium",
      "name": "microsoft/DialoGPT-medium",
      "display_name": "DialoGPT Medium",
      "description": "Medium-sized conversational AI model",
      "model_type": "language_model",
      "provider": "huggingface",
      "version": "latest",
      "size_bytes": **********,
      "size_display": "1.5 GB",
      "parameter_count": 117000000,
      "parameter_display": "117M",
      "context_length": 1024,
      "min_ram_gb": 4.0,
      "gpu_required": false,
      "capabilities": ["text-generation", "conversation"],
      "languages": ["en"],
      "tags": ["conversational", "chatbot", "microsoft"],
      "license": "MIT",
      "homepage_url": "https://huggingface.co/microsoft/DialoGPT-medium",
      "created_at": "2024-01-15T10:00:00Z",
      "updated_at": "2024-01-20T14:30:00Z"
    }
  ],
  "pagination": {
    "total": 1250,
    "limit": 20,
    "offset": 0,
    "has_next": true
  }
}
```

### Discover Ollama Models
Discover available models from Ollama registry.

```http
GET /models/discover/ollama
```

**Query Parameters:**
- `limit` (integer, optional, default: 20): Maximum number of models to return
- `offset` (integer, optional, default: 0): Number of models to skip

**Response:** Same structure as HuggingFace discovery

### Search Models
Search models across all providers.

```http
POST /models/search
```

**Request Body:**
```json
{
  "query": "language model",
  "model_type": "language_model",
  "provider": "huggingface",
  "limit": 50,
  "offset": 0
}
```

**Response:** Same structure as discovery endpoints

## Model Instance Management

### Create Model Instance
Create a new model instance for download and serving.

```http
POST /models/instances
```

**Request Body:**
```json
{
  "model_id": "microsoft/DialoGPT-medium",
  "configuration": {
    "max_length": 1000,
    "temperature": 0.7,
    "top_p": 0.9
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "model_id": "microsoft/DialoGPT-medium",
    "status": "available",
    "download_progress": 0.0,
    "configuration": {
      "max_length": 1000,
      "temperature": 0.7,
      "top_p": 0.9
    },
    "created_by": "user-123",
    "created_at": "2024-01-20T15:00:00Z",
    "updated_at": "2024-01-20T15:00:00Z"
  }
}
```

### Get Model Instance
Retrieve a specific model instance.

```http
GET /models/instances/{instance_id}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "model_id": "microsoft/DialoGPT-medium",
    "status": "serving",
    "download_progress": 100.0,
    "local_path": "./models/microsoft/DialoGPT-medium",
    "serving_port": 8000,
    "serving_url": "http://localhost:8000",
    "api_endpoint": "http://localhost:8000/v1/completions",
    "load_time_seconds": 15.2,
    "memory_usage_mb": 2048.5,
    "gpu_memory_usage_mb": 1024.0,
    "configuration": {
      "max_length": 1000,
      "temperature": 0.7,
      "top_p": 0.9
    },
    "created_by": "user-123",
    "created_at": "2024-01-20T15:00:00Z",
    "updated_at": "2024-01-20T15:30:00Z",
    "last_used_at": "2024-01-20T15:25:00Z"
  }
}
```

### List User Model Instances
Get all model instances for the authenticated user.

```http
GET /models/instances
```

**Query Parameters:**
- `status` (string, optional): Filter by status (available, downloading, serving, etc.)
- `limit` (integer, optional, default: 100): Maximum number of instances to return
- `offset` (integer, optional, default: 0): Number of instances to skip

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "model_id": "microsoft/DialoGPT-medium",
      "status": "serving",
      "download_progress": 100.0,
      "serving_port": 8000,
      "memory_usage_mb": 2048.5,
      "created_at": "2024-01-20T15:00:00Z",
      "updated_at": "2024-01-20T15:30:00Z"
    }
  ],
  "pagination": {
    "total": 5,
    "limit": 100,
    "offset": 0,
    "has_next": false
  }
}
```

## Model Download Management

### Start Model Download
Initiate download of a model instance.

```http
POST /models/instances/{instance_id}/download
```

**Response:**
```json
{
  "success": true,
  "data": {
    "task_id": "660e8400-e29b-41d4-a716-446655440000",
    "instance_id": "550e8400-e29b-41d4-a716-446655440000",
    "model_id": "microsoft/DialoGPT-medium",
    "status": "running",
    "progress": 0.0,
    "download_url": "https://huggingface.co/microsoft/DialoGPT-medium/resolve/main/pytorch_model.bin",
    "destination_path": "./models/microsoft/DialoGPT-medium",
    "total_size": **********,
    "downloaded_size": 0,
    "created_at": "2024-01-20T15:05:00Z"
  }
}
```

### Get Download Progress
Check the progress of a download task.

```http
GET /models/downloads/{task_id}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "task_id": "660e8400-e29b-41d4-a716-446655440000",
    "instance_id": "550e8400-e29b-41d4-a716-446655440000",
    "model_id": "microsoft/DialoGPT-medium",
    "status": "running",
    "progress": 75.5,
    "total_size": **********,
    "downloaded_size": 1132500000,
    "download_speed": 15000000,
    "started_at": "2024-01-20T15:05:00Z",
    "estimated_completion": "2024-01-20T15:07:30Z",
    "updated_at": "2024-01-20T15:06:45Z"
  }
}
```

### Cancel Download
Cancel an active download task.

```http
DELETE /models/downloads/{task_id}
```

**Response:**
```json
{
  "success": true,
  "message": "Download cancelled successfully"
}
```

## Model Serving Management

### Start Model Serving
Start serving a downloaded model instance.

```http
POST /models/instances/{instance_id}/serve
```

**Request Body:**
```json
{
  "port": 8000,
  "configuration": {
    "max_concurrent_requests": 10,
    "timeout_seconds": 30
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "serving_url": "http://localhost:8000",
    "api_endpoint": "http://localhost:8000/v1/completions",
    "status": "serving",
    "load_time_seconds": 15.2
  }
}
```

### Stop Model Serving
Stop serving a model instance.

```http
POST /models/instances/{instance_id}/stop
```

**Response:**
```json
{
  "success": true,
  "message": "Model serving stopped successfully"
}
```

### Check Model Health
Check the health status of a serving model.

```http
GET /models/instances/{instance_id}/health
```

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "message": "Model is serving normally",
    "response_time_ms": 150,
    "memory_usage_mb": 2048.5,
    "gpu_memory_usage_mb": 1024.0,
    "requests_served": 125,
    "errors_count": 0,
    "uptime_seconds": 3600,
    "last_check": "2024-01-20T15:30:00Z"
  }
}
```

## WebSocket Real-time Updates

### Connection
Connect to WebSocket for real-time updates:

```javascript
const ws = new WebSocket('ws://localhost:3001/ws/model-management/{user_id}');
```

### Subscribe to Download Progress
```javascript
ws.send(JSON.stringify({
  type: 'subscribe_download_task',
  data: { task_id: '660e8400-e29b-41d4-a716-446655440000' }
}));
```

### Subscribe to Model Instance Status
```javascript
ws.send(JSON.stringify({
  type: 'subscribe_model_instance',
  data: { instance_id: '550e8400-e29b-41d4-a716-446655440000' }
}));
```

### Message Types
- `download_progress`: Download progress updates
- `instance_status`: Model instance status changes
- `model_health`: Health check results
- `error`: Error notifications

## Error Responses

All error responses follow this format:

```json
{
  "success": false,
  "error": {
    "error_code": "MODEL_NOT_FOUND",
    "message": "Model microsoft/invalid-model not found",
    "details": {
      "model_id": "microsoft/invalid-model"
    },
    "correlation_id": "550e8400-e29b-41d4-a716-446655440000"
  }
}
```

### Common Error Codes
- `MODEL_NOT_FOUND`: Requested model does not exist
- `MODEL_DOWNLOAD_FAILED`: Model download failed
- `MODEL_SERVING_FAILED`: Model serving failed
- `VALIDATION_ERROR`: Request validation failed
- `PERMISSION_DENIED`: Insufficient permissions
- `RATE_LIMIT_EXCEEDED`: Too many requests

## Rate Limits
- **Discovery endpoints**: 100 requests per minute
- **Instance management**: 50 requests per minute
- **Download operations**: 10 concurrent downloads per user
- **Serving operations**: 20 requests per minute

## SDK Examples

### Python
```python
from lonors_sdk import LonorsClient

client = LonorsClient(api_key="your-api-key")

# Discover models
models = client.models.discover_huggingface(query="conversational")

# Create and download model
instance = client.models.create_instance(
    model_id="microsoft/DialoGPT-medium",
    configuration={"max_length": 1000}
)

download_task = client.models.download(instance.id)

# Monitor progress
while download_task.status == "running":
    download_task = client.models.get_download_progress(download_task.id)
    print(f"Progress: {download_task.progress}%")
    time.sleep(5)

# Start serving
serving_info = client.models.start_serving(instance.id, port=8000)
print(f"Model serving at: {serving_info.api_endpoint}")
```

### JavaScript
```javascript
import { LonorsClient } from '@lonors/sdk';

const client = new LonorsClient({ apiKey: 'your-api-key' });

// Discover and download model
const models = await client.models.discoverHuggingface({ query: 'conversational' });
const instance = await client.models.createInstance({
  modelId: 'microsoft/DialoGPT-medium',
  configuration: { maxLength: 1000 }
});

const downloadTask = await client.models.download(instance.id);

// Real-time progress updates
client.models.onDownloadProgress(downloadTask.id, (progress) => {
  console.log(`Progress: ${progress.progress}%`);
});
```
