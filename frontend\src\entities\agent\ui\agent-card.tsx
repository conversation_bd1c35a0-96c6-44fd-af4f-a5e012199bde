'use client';

import { animateAgentCard } from '@/shared/lib/animations';
import { cn } from '@/shared/lib/utils';
import { AgentStatus, AgentType } from '@/shared/types';
import { AgentStatusIndicator } from '@/shared/ui/agent-status';
import { AnimatedBox } from '@/shared/ui/animated-box';
import { Badge } from '@/shared/ui/badge';
import { Button } from '@/shared/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/shared/ui/card';
import {
    Bot,
    Brain,
    Clock,
    MessageSquare,
    MoreVertical,
    Pause,
    Play,
    Settings,
    Sparkles,
    Square,
    Tool,
    TrendingUp,
    Workflow,
    Zap
} from 'lucide-react';
import React, { useRef, useState } from 'react';
import { AgentModel, agentSelectors } from '../model';

interface AgentCardProps {
  agent: AgentModel;
  onExecute?: (agent: AgentModel) => void;
  onPause?: (agent: AgentModel) => void;
  onStop?: (agent: AgentModel) => void;
  onEdit?: (agent: AgentModel) => void;
  onSelect?: (agent: AgentModel) => void;
  isSelected?: boolean;
  className?: string;
  variant?: 'default' | 'compact' | 'detailed';
  showMetrics?: boolean;
  animate?: boolean;
}

// Agent type icon mapping
const getAgentTypeIcon = (type: AgentType) => {
  switch (type) {
    case AgentType.CHAT:
      return MessageSquare;
    case AgentType.WORKFLOW:
      return Workflow;
    case AgentType.TOOL:
      return Tool;
    case AgentType.REASONING:
      return Brain;
    case AgentType.MULTIMODAL:
      return Sparkles;
    default:
      return Bot;
  }
};

export const AgentCard = React.forwardRef<HTMLDivElement, AgentCardProps>(
  ({
    agent,
    onExecute,
    onPause,
    onStop,
    onEdit,
    onSelect,
    isSelected = false,
    className,
    variant = 'default',
    showMetrics = true,
    animate = true,
    ...props
  }, ref) => {
    const cardRef = useRef<HTMLDivElement>(null);
    const [isHovered, setIsHovered] = useState(false);

    const canExecute = agentSelectors.canExecute(agent);
    const isActive = agentSelectors.isActive(agent);
    const isCompleted = agentSelectors.isCompleted(agent);
    const successRate = agentSelectors.getSuccessRate(agent);
    const avgExecutionTime = agentSelectors.getAverageExecutionTime(agent);
    const totalCost = agentSelectors.formatCost(agent);

    const AgentIcon = getAgentTypeIcon(agent.agent_type);

    const handleMouseEnter = () => {
      setIsHovered(true);
      if (animate && cardRef.current) {
        animateAgentCard(cardRef.current, 'hover');
      }
    };

    const handleMouseLeave = () => {
      setIsHovered(false);
    };

    const handleCardClick = () => {
      if (onSelect) {
        onSelect(agent);
        if (animate && cardRef.current) {
          animateAgentCard(cardRef.current, isSelected ? 'deselect' : 'select');
        }
      }
    };

    const handleAction = (e: React.MouseEvent) => {
      e.stopPropagation();
      if (agent.status === AgentStatus.RUNNING && onPause) {
        onPause(agent);
      } else if (canExecute && onExecute) {
        onExecute(agent);
      }
    };

    const handleStop = (e: React.MouseEvent) => {
      e.stopPropagation();
      if (onStop) onStop(agent);
    };

    const handleEdit = (e: React.MouseEvent) => {
      e.stopPropagation();
      if (onEdit) onEdit(agent);
    };

    const cardContent = (
      <Card
        ref={ref || cardRef}
        className={cn(
          'group relative overflow-hidden transition-all duration-200 cursor-pointer',
          'hover:shadow-lg hover:-translate-y-1',
          isSelected && 'ring-2 ring-primary ring-offset-2',
          variant === 'compact' && 'max-w-xs',
          variant === 'detailed' && 'max-w-md',
          className
        )}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleCardClick}
        role="button"
        tabIndex={0}
        aria-label={`Agent ${agent.name}, status: ${agent.status}`}
        {...props}
      >
        {/* Status indicator overlay */}
        <div className={cn(
          'absolute top-0 left-0 w-full h-1 transition-all duration-300',
          isActive && 'bg-gradient-to-r from-green-400 to-green-600',
          isCompleted && agent.status === AgentStatus.COMPLETED && 'bg-gradient-to-r from-blue-400 to-blue-600',
          agent.status === AgentStatus.FAILED && 'bg-gradient-to-r from-red-400 to-red-600',
          agent.status === AgentStatus.IDLE && 'bg-gradient-to-r from-slate-300 to-slate-400'
        )} />

        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-3 min-w-0 flex-1">
              <div className="relative">
                <AgentIcon className={cn(
                  'h-6 w-6 transition-colors duration-200',
                  isActive ? 'text-green-600' : 'text-primary'
                )} />
                {isActive && (
                  <div className="absolute -top-1 -right-1 h-3 w-3 bg-green-500 rounded-full animate-pulse" />
                )}
              </div>
              <div className="min-w-0 flex-1">
                <CardTitle className="text-lg font-semibold truncate">
                  {agent.name}
                </CardTitle>
                <div className="flex items-center gap-2 mt-1">
                  <AgentStatusIndicator
                    status={agent.status}
                    size="sm"
                    animate={animate}
                  />
                  <Badge variant="outline" className="text-xs">
                    {agent.agent_type}
                  </Badge>
                </div>
              </div>
            </div>

            <Button
              variant="ghost"
              size="sm"
              className="opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={handleEdit}
              aria-label="More options"
            >
              <MoreVertical className="h-4 w-4" />
            </Button>
          </div>

          {variant !== 'compact' && (
            <CardDescription className="line-clamp-2 mt-2">
              {agent.description || 'No description available'}
            </CardDescription>
          )}
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Capabilities and Tags */}
          <div className="space-y-2">
            {agent.capabilities.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {agent.capabilities.slice(0, 3).map((capability) => (
                  <Badge key={capability.name} variant="secondary" className="text-xs">
                    {capability.name}
                  </Badge>
                ))}
                {agent.capabilities.length > 3 && (
                  <Badge variant="secondary" className="text-xs">
                    +{agent.capabilities.length - 3}
                  </Badge>
                )}
              </div>
            )}

            {agent.tags.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {agent.tags.slice(0, 2).map((tag) => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
                {agent.tags.length > 2 && (
                  <Badge variant="outline" className="text-xs">
                    +{agent.tags.length - 2}
                  </Badge>
                )}
              </div>
            )}
          </div>

          {/* Metrics */}
          {showMetrics && variant !== 'compact' && agent.metrics.total_executions > 0 && (
            <div className="grid grid-cols-2 gap-3 text-sm">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
                <div>
                  <div className="font-medium">{successRate.toFixed(1)}%</div>
                  <div className="text-xs text-muted-foreground">Success</div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <div>
                  <div className="font-medium">{avgExecutionTime}</div>
                  <div className="text-xs text-muted-foreground">Avg Time</div>
                </div>
              </div>
              {variant === 'detailed' && (
                <>
                  <div className="flex items-center gap-2">
                    <Zap className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <div className="font-medium">{agent.metrics.total_executions}</div>
                      <div className="text-xs text-muted-foreground">Runs</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-muted-foreground">$</span>
                    <div>
                      <div className="font-medium">{totalCost}</div>
                      <div className="text-xs text-muted-foreground">Cost</div>
                    </div>
                  </div>
                </>
              )}
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2 pt-2">
            <Button
              size="sm"
              onClick={handleAction}
              disabled={!canExecute && agent.status !== AgentStatus.RUNNING}
              className="flex-1"
              aria-label={agent.status === AgentStatus.RUNNING ? 'Pause agent' : 'Execute agent'}
            >
              {agent.status === AgentStatus.RUNNING ? (
                <>
                  <Pause className="h-4 w-4 mr-1" />
                  Pause
                </>
              ) : (
                <>
                  <Play className="h-4 w-4 mr-1" />
                  Execute
                </>
              )}
            </Button>

            {isActive && onStop && (
              <Button
                size="sm"
                variant="outline"
                onClick={handleStop}
                aria-label="Stop agent"
              >
                <Square className="h-4 w-4" />
              </Button>
            )}

            {onEdit && (
              <Button
                size="sm"
                variant="outline"
                onClick={handleEdit}
                aria-label="Edit agent"
              >
                <Settings className="h-4 w-4" />
              </Button>
            )}
          </div>
        </CardContent>

        {/* Hover effect overlay */}
        <div className={cn(
          'absolute inset-0 bg-gradient-to-br from-primary/5 to-primary/10 opacity-0 transition-opacity duration-200 pointer-events-none',
          isHovered && 'opacity-100'
        )} />
      </Card>
    );

    return animate ? (
      <AnimatedBox
        animationPreset="slideInUp"
        animationDeps={[agent.id]}
        className="w-full"
      >
        {cardContent}
      </AnimatedBox>
    ) : cardContent;
  }
);

AgentCard.displayName = 'AgentCard';
