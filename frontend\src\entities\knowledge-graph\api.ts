import { useState, useEffect } from 'react';
import { apiClient, API_ENDPOINTS } from '@/shared/lib/api';
import { KnowledgeEntity, KnowledgeRelationship, KnowledgeGraph, EntityType, RelationType } from '@/shared/types';

export interface KnowledgeEntityListParams {
  entity_type?: EntityType;
  limit?: number;
  offset?: number;
}

export interface KnowledgeEntitySearchParams {
  query: string;
  entity_type?: EntityType;
  include_public?: boolean;
  limit?: number;
  offset?: number;
}

export interface KnowledgeEntityCreateRequest {
  name: string;
  description?: string;
  entity_type: EntityType;
  properties?: Record<string, any>;
  attributes?: Record<string, any>;
  tags?: string[];
  categories?: string[];
  confidence_score?: number;
  source_id?: string;
  source_type?: string;
  source_metadata?: Record<string, any>;
  is_public?: boolean;
}

export interface KnowledgeRelationshipCreateRequest {
  source_entity_id: string;
  target_entity_id: string;
  relation_type: RelationType;
  properties?: Record<string, any>;
  weight?: number;
  confidence_score?: number;
  is_directed?: boolean;
  description?: string;
  tags?: string[];
  source_id?: string;
  source_type?: string;
  source_metadata?: Record<string, any>;
}

export interface SubgraphRequest {
  entity_ids: string[];
  max_depth?: number;
}

export interface ContextSearchRequest {
  query: string;
  context_entity_ids: string[];
  limit?: number;
}

export interface KnowledgeEntityListResponse {
  entities: KnowledgeEntity[];
  total: number;
  limit: number;
  offset: number;
}

// Update API endpoints
const KNOWLEDGE_GRAPH_ENDPOINTS = {
  ENTITIES: '/knowledge-graph/entities',
  ENTITY_BY_ID: (id: string) => `/knowledge-graph/entities/${id}`,
  SEARCH_ENTITIES: '/knowledge-graph/entities/search',
  RELATIONSHIPS: '/knowledge-graph/relationships',
  ENTITY_RELATIONSHIPS: (id: string) => `/knowledge-graph/entities/${id}/relationships`,
  SUBGRAPH: '/knowledge-graph/subgraph',
  SEARCH_IN_CONTEXT: '/knowledge-graph/entities/search-in-context',
  ENTITY_PATHS: (sourceId: string, targetId: string) => `/knowledge-graph/paths/${sourceId}/${targetId}`,
};

export const knowledgeGraphService = {
  // Entity operations
  async getEntities(params: KnowledgeEntityListParams = {}): Promise<KnowledgeEntity[]> {
    const searchParams = new URLSearchParams();
    
    if (params.entity_type) searchParams.append('entity_type', params.entity_type);
    if (params.limit) searchParams.append('limit', params.limit.toString());
    if (params.offset) searchParams.append('offset', params.offset.toString());

    const query = searchParams.toString();
    const endpoint = query ? `${KNOWLEDGE_GRAPH_ENDPOINTS.ENTITIES}?${query}` : KNOWLEDGE_GRAPH_ENDPOINTS.ENTITIES;
    
    const response = await apiClient.get<KnowledgeEntity[]>(endpoint);
    return response.data;
  },

  async getEntity(id: string): Promise<KnowledgeEntity> {
    const response = await apiClient.get<KnowledgeEntity>(KNOWLEDGE_GRAPH_ENDPOINTS.ENTITY_BY_ID(id));
    return response.data;
  },

  async searchEntities(params: KnowledgeEntitySearchParams): Promise<KnowledgeEntity[]> {
    const searchParams = new URLSearchParams();
    searchParams.append('query', params.query);
    if (params.entity_type) searchParams.append('entity_type', params.entity_type);
    if (params.include_public !== undefined) searchParams.append('include_public', params.include_public.toString());
    if (params.limit) searchParams.append('limit', params.limit.toString());
    if (params.offset) searchParams.append('offset', params.offset.toString());

    const endpoint = `${KNOWLEDGE_GRAPH_ENDPOINTS.SEARCH_ENTITIES}?${searchParams.toString()}`;
    const response = await apiClient.get<KnowledgeEntity[]>(endpoint);
    return response.data;
  },

  async createEntity(entity: KnowledgeEntityCreateRequest): Promise<KnowledgeEntity> {
    const response = await apiClient.post<KnowledgeEntity>(KNOWLEDGE_GRAPH_ENDPOINTS.ENTITIES, entity);
    return response.data;
  },

  async updateEntity(id: string, updates: Partial<KnowledgeEntityCreateRequest>): Promise<KnowledgeEntity> {
    const response = await apiClient.put<KnowledgeEntity>(KNOWLEDGE_GRAPH_ENDPOINTS.ENTITY_BY_ID(id), updates);
    return response.data;
  },

  async deleteEntity(id: string): Promise<void> {
    await apiClient.delete(KNOWLEDGE_GRAPH_ENDPOINTS.ENTITY_BY_ID(id));
  },

  // Relationship operations
  async createRelationship(relationship: KnowledgeRelationshipCreateRequest): Promise<KnowledgeRelationship> {
    const response = await apiClient.post<KnowledgeRelationship>(KNOWLEDGE_GRAPH_ENDPOINTS.RELATIONSHIPS, relationship);
    return response.data;
  },

  async getEntityRelationships(
    entityId: string,
    relationshipType?: RelationType,
    direction?: 'incoming' | 'outgoing'
  ): Promise<KnowledgeRelationship[]> {
    const searchParams = new URLSearchParams();
    if (relationshipType) searchParams.append('relation_type', relationshipType);
    if (direction) searchParams.append('direction', direction);

    const query = searchParams.toString();
    const endpoint = query 
      ? `${KNOWLEDGE_GRAPH_ENDPOINTS.ENTITY_RELATIONSHIPS(entityId)}?${query}` 
      : KNOWLEDGE_GRAPH_ENDPOINTS.ENTITY_RELATIONSHIPS(entityId);
    
    const response = await apiClient.get<KnowledgeRelationship[]>(endpoint);
    return response.data;
  },

  // Graph operations
  async getSubgraph(request: SubgraphRequest): Promise<KnowledgeGraph> {
    const response = await apiClient.post<KnowledgeGraph>(KNOWLEDGE_GRAPH_ENDPOINTS.SUBGRAPH, request);
    return response.data;
  },

  async searchInContext(request: ContextSearchRequest): Promise<KnowledgeEntity[]> {
    const searchParams = new URLSearchParams();
    searchParams.append('query', request.query);
    request.context_entity_ids.forEach(id => searchParams.append('context_entity_ids', id));
    if (request.limit) searchParams.append('limit', request.limit.toString());

    const endpoint = `${KNOWLEDGE_GRAPH_ENDPOINTS.SEARCH_IN_CONTEXT}?${searchParams.toString()}`;
    const response = await apiClient.get<KnowledgeEntity[]>(endpoint);
    return response.data;
  },

  async findEntityPaths(sourceId: string, targetId: string, maxDepth: number = 5): Promise<string[][]> {
    const searchParams = new URLSearchParams();
    searchParams.append('max_depth', maxDepth.toString());

    const endpoint = `${KNOWLEDGE_GRAPH_ENDPOINTS.ENTITY_PATHS(sourceId, targetId)}?${searchParams.toString()}`;
    const response = await apiClient.get<string[][]>(endpoint);
    return response.data;
  },
};

// React hooks for knowledge graph operations
export function useKnowledgeEntities(params: KnowledgeEntityListParams = {}) {
  const [entities, setEntities] = useState<KnowledgeEntity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchEntities = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await knowledgeGraphService.getEntities(params);
        setEntities(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch entities');
      } finally {
        setLoading(false);
      }
    };

    fetchEntities();
  }, [params.entity_type, params.limit, params.offset]);

  const refetch = async () => {
    try {
      setError(null);
      const data = await knowledgeGraphService.getEntities(params);
      setEntities(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch entities');
    }
  };

  return { entities, loading, error, refetch };
}

export function useKnowledgeEntity(id: string) {
  const [entity, setEntity] = useState<KnowledgeEntity | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchEntity = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await knowledgeGraphService.getEntity(id);
        setEntity(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch entity');
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchEntity();
    }
  }, [id]);

  return { entity, loading, error };
}

export function useEntityRelationships(entityId: string, relationshipType?: RelationType, direction?: 'incoming' | 'outgoing') {
  const [relationships, setRelationships] = useState<KnowledgeRelationship[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchRelationships = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await knowledgeGraphService.getEntityRelationships(entityId, relationshipType, direction);
        setRelationships(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch relationships');
      } finally {
        setLoading(false);
      }
    };

    if (entityId) {
      fetchRelationships();
    }
  }, [entityId, relationshipType, direction]);

  const refetch = async () => {
    try {
      setError(null);
      const data = await knowledgeGraphService.getEntityRelationships(entityId, relationshipType, direction);
      setRelationships(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch relationships');
    }
  };

  return { relationships, loading, error, refetch };
}

export function useSubgraph(entityIds: string[], maxDepth: number = 2) {
  const [graph, setGraph] = useState<KnowledgeGraph | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchSubgraph = async () => {
    if (entityIds.length === 0) return;

    try {
      setLoading(true);
      setError(null);
      const data = await knowledgeGraphService.getSubgraph({ entity_ids: entityIds, max_depth: maxDepth });
      setGraph(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch subgraph');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSubgraph();
  }, [entityIds.join(','), maxDepth]);

  return { graph, loading, error, refetch: fetchSubgraph };
}
