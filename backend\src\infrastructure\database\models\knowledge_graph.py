"""
Knowledge Graph SQLAlchemy models.

This module contains the SQLAlchemy models for the knowledge graph tables.
"""

import uuid
from datetime import datetime
from typing import TYPE_CHECKING

from sqlalchemy import (
    Boolean,
    DateTime,
    Enum,
    Float,
    ForeignKey,
    Index,
    JSON,
    String,
    Text,
    UUID,
)
from sqlalchemy.orm import Mapped, mapped_column, relationship

from src.domain.entities.knowledge_graph import EntityType, RelationType
from src.infrastructure.database.models.base import Base

if TYPE_CHECKING:
    from src.infrastructure.database.models.user import UserModel


class KnowledgeEntityModel(Base):
    """
    Knowledge Entity SQLAlchemy model.

    Represents the knowledge_entities table in the database with all
    necessary constraints and indexes for efficient graph operations.
    """

    __tablename__ = "knowledge_entities"

    # Entity identification
    name: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        index=True,
        doc="Entity name"
    )

    description: Mapped[str | None] = mapped_column(
        Text,
        nullable=True,
        doc="Entity description"
    )

    # Classification
    entity_type: Mapped[EntityType] = mapped_column(
        Enum(EntityType),
        nullable=False,
        index=True,
        doc="Entity type"
    )

    # Properties and attributes (stored as JSON)
    properties: Mapped[dict] = mapped_column(
        JSON,
        default=dict,
        nullable=False,
        doc="Entity properties as JSON"
    )

    attributes: Mapped[dict] = mapped_column(
        JSON,
        default=dict,
        nullable=False,
        doc="Entity attributes as JSON"
    )

    # Metadata
    tags: Mapped[list] = mapped_column(
        JSON,
        default=list,
        nullable=False,
        doc="Entity tags as JSON array"
    )

    categories: Mapped[list] = mapped_column(
        JSON,
        default=list,
        nullable=False,
        doc="Entity categories as JSON array"
    )

    confidence_score: Mapped[float] = mapped_column(
        Float,
        default=1.0,
        nullable=False,
        doc="Confidence score"
    )

    # Source information
    source_id: Mapped[str | None] = mapped_column(
        String(255),
        nullable=True,
        index=True,
        doc="Source identifier"
    )

    source_type: Mapped[str | None] = mapped_column(
        String(100),
        nullable=True,
        index=True,
        doc="Source type"
    )

    source_metadata: Mapped[dict] = mapped_column(
        JSON,
        default=dict,
        nullable=False,
        doc="Source metadata as JSON"
    )

    # Ownership and access
    created_by: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Creator user ID"
    )

    is_public: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        index=True,
        doc="Whether entity is publicly accessible"
    )

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=datetime.utcnow,
        nullable=False,
        index=True,
        doc="Creation timestamp"
    )

    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=datetime.utcnow,
        onupdate=datetime.utcnow,
        nullable=False,
        index=True,
        doc="Last update timestamp"
    )

    # Relationships
    creator: Mapped["UserModel"] = relationship(
        "UserModel",
        foreign_keys=[created_by],
        back_populates="knowledge_entities"
    )

    # Indexes for efficient querying
    __table_args__ = (
        Index('ix_knowledge_entities_name_type', 'name', 'entity_type'),
        Index('ix_knowledge_entities_creator_type', 'created_by', 'entity_type'),
        Index('ix_knowledge_entities_public_type', 'is_public', 'entity_type'),
        Index('ix_knowledge_entities_source', 'source_id', 'source_type'),
        Index('ix_knowledge_entities_confidence', 'confidence_score'),
    )

    def to_domain(self) -> "KnowledgeEntity":
        """
        Convert SQLAlchemy model to domain entity.

        Returns:
            KnowledgeEntity domain entity
        """
        from src.domain.entities.knowledge_graph import KnowledgeEntity

        return KnowledgeEntity(
            id=self.id,
            name=self.name,
            description=self.description,
            entity_type=self.entity_type,
            properties=self.properties or {},
            attributes=self.attributes or {},
            tags=self.tags or [],
            categories=self.categories or [],
            confidence_score=self.confidence_score,
            source_id=self.source_id,
            source_type=self.source_type,
            source_metadata=self.source_metadata or {},
            created_by=self.created_by,
            is_public=self.is_public,
            created_at=self.created_at,
            updated_at=self.updated_at,
        )

    @classmethod
    def from_domain(cls, entity: "KnowledgeEntity") -> "KnowledgeEntityModel":
        """
        Create SQLAlchemy model from domain entity.

        Args:
            entity: KnowledgeEntity domain entity

        Returns:
            KnowledgeEntityModel instance
        """
        return cls(
            id=entity.id,
            name=entity.name,
            description=entity.description,
            entity_type=entity.entity_type,
            properties=entity.properties,
            attributes=entity.attributes,
            tags=entity.tags,
            categories=entity.categories,
            confidence_score=entity.confidence_score,
            source_id=entity.source_id,
            source_type=entity.source_type,
            source_metadata=entity.source_metadata,
            created_by=entity.created_by,
            is_public=entity.is_public,
            created_at=entity.created_at,
            updated_at=entity.updated_at,
        )


class KnowledgeRelationshipModel(Base):
    """
    Knowledge Relationship SQLAlchemy model.

    Represents the knowledge_relationships table in the database with all
    necessary constraints and indexes for efficient graph traversal.
    """

    __tablename__ = "knowledge_relationships"

    # Relationship endpoints
    source_entity_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("knowledge_entities.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Source entity ID"
    )

    target_entity_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("knowledge_entities.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Target entity ID"
    )

    # Relationship type
    relation_type: Mapped[RelationType] = mapped_column(
        Enum(RelationType),
        nullable=False,
        index=True,
        doc="Relationship type"
    )

    # Relationship properties
    properties: Mapped[dict] = mapped_column(
        JSON,
        default=dict,
        nullable=False,
        doc="Relationship properties as JSON"
    )

    weight: Mapped[float] = mapped_column(
        Float,
        default=1.0,
        nullable=False,
        doc="Relationship weight/strength"
    )

    confidence_score: Mapped[float] = mapped_column(
        Float,
        default=1.0,
        nullable=False,
        doc="Confidence score"
    )

    # Directional information
    is_directed: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        doc="Whether relationship is directed"
    )

    # Metadata
    description: Mapped[str | None] = mapped_column(
        Text,
        nullable=True,
        doc="Relationship description"
    )

    tags: Mapped[list] = mapped_column(
        JSON,
        default=list,
        nullable=False,
        doc="Relationship tags as JSON array"
    )

    # Source information
    source_id: Mapped[str | None] = mapped_column(
        String(255),
        nullable=True,
        index=True,
        doc="Source identifier"
    )

    source_type: Mapped[str | None] = mapped_column(
        String(100),
        nullable=True,
        index=True,
        doc="Source type"
    )

    source_metadata: Mapped[dict] = mapped_column(
        JSON,
        default=dict,
        nullable=False,
        doc="Source metadata as JSON"
    )

    # Ownership
    created_by: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Creator user ID"
    )

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=datetime.utcnow,
        nullable=False,
        index=True,
        doc="Creation timestamp"
    )

    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=datetime.utcnow,
        onupdate=datetime.utcnow,
        nullable=False,
        index=True,
        doc="Last update timestamp"
    )

    # Relationships
    source_entity: Mapped["KnowledgeEntityModel"] = relationship(
        "KnowledgeEntityModel",
        foreign_keys=[source_entity_id],
        back_populates="outgoing_relationships"
    )

    target_entity: Mapped["KnowledgeEntityModel"] = relationship(
        "KnowledgeEntityModel",
        foreign_keys=[target_entity_id],
        back_populates="incoming_relationships"
    )

    creator: Mapped["UserModel"] = relationship(
        "UserModel",
        foreign_keys=[created_by]
    )

    # Indexes for efficient graph traversal
    __table_args__ = (
        Index('ix_knowledge_relationships_source_type', 'source_entity_id', 'relation_type'),
        Index('ix_knowledge_relationships_target_type', 'target_entity_id', 'relation_type'),
        Index('ix_knowledge_relationships_entities', 'source_entity_id', 'target_entity_id'),
        Index('ix_knowledge_relationships_weight', 'weight'),
        Index('ix_knowledge_relationships_confidence', 'confidence_score'),
    )

    def to_domain(self) -> "KnowledgeRelationship":
        """
        Convert SQLAlchemy model to domain entity.

        Returns:
            KnowledgeRelationship domain entity
        """
        from src.domain.entities.knowledge_graph import KnowledgeRelationship

        return KnowledgeRelationship(
            id=self.id,
            source_entity_id=self.source_entity_id,
            target_entity_id=self.target_entity_id,
            relation_type=self.relation_type,
            properties=self.properties or {},
            weight=self.weight,
            confidence_score=self.confidence_score,
            is_directed=self.is_directed,
            description=self.description,
            tags=self.tags or [],
            source_id=self.source_id,
            source_type=self.source_type,
            source_metadata=self.source_metadata or {},
            created_by=self.created_by,
            created_at=self.created_at,
            updated_at=self.updated_at,
        )

    @classmethod
    def from_domain(cls, relationship: "KnowledgeRelationship") -> "KnowledgeRelationshipModel":
        """
        Create SQLAlchemy model from domain entity.

        Args:
            relationship: KnowledgeRelationship domain entity

        Returns:
            KnowledgeRelationshipModel instance
        """
        return cls(
            id=relationship.id,
            source_entity_id=relationship.source_entity_id,
            target_entity_id=relationship.target_entity_id,
            relation_type=relationship.relation_type,
            properties=relationship.properties,
            weight=relationship.weight,
            confidence_score=relationship.confidence_score,
            is_directed=relationship.is_directed,
            description=relationship.description,
            tags=relationship.tags,
            source_id=relationship.source_id,
            source_type=relationship.source_type,
            source_metadata=relationship.source_metadata,
            created_by=relationship.created_by,
            created_at=relationship.created_at,
            updated_at=relationship.updated_at,
        )


# Add relationships to KnowledgeEntityModel
KnowledgeEntityModel.outgoing_relationships = relationship(
    "KnowledgeRelationshipModel",
    foreign_keys=[KnowledgeRelationshipModel.source_entity_id],
    back_populates="source_entity",
    cascade="all, delete-orphan"
)

KnowledgeEntityModel.incoming_relationships = relationship(
    "KnowledgeRelationshipModel",
    foreign_keys=[KnowledgeRelationshipModel.target_entity_id],
    back_populates="target_entity",
    cascade="all, delete-orphan"
)
