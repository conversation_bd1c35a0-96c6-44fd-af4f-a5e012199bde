'use client';

import React, { useState, useEffect } from 'react';
import { Plus, Search, Filter, MoreVertical, Play, Pause, Settings, Trash2 } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAgents } from '@/entities/agent/api';
import { AgentModel, agentSelectors } from '@/entities/agent/model';
import { AgentType, AgentStatus } from '@/shared/types';
import { Button } from '@/shared/ui/button';
import { Input } from '@/shared/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/ui/card';
import { Badge } from '@/shared/ui/badge';
import { Avatar, AvatarFallback } from '@/shared/ui/avatar';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/shared/ui/dropdown-menu';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/shared/ui/select';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/shared/ui/tabs';
import { Progress } from '@/shared/ui/progress';
import { Skeleton } from '@/shared/ui/skeleton';
import { useToast } from '@/shared/hooks/use-toast';

interface AgentDashboardProps {
  className?: string;
}

export function AgentDashboard({ className }: AgentDashboardProps) {
  const { agents, loading, error, refetch } = useAgents();
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<AgentStatus | 'all'>('all');
  const [typeFilter, setTypeFilter] = useState<AgentType | 'all'>('all');
  const [selectedAgent, setSelectedAgent] = useState<AgentModel | null>(null);
  const { toast } = useToast();

  // Filter agents based on search and filters
  const filteredAgents = agents.filter(agent => {
    const matchesSearch = agent.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         agent.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === 'all' || agent.status === statusFilter;
    const matchesType = typeFilter === 'all' || agent.agent_type === typeFilter;
    
    return matchesSearch && matchesStatus && matchesType;
  });

  // Calculate dashboard metrics
  const metrics = {
    total: agents.length,
    active: agents.filter(a => a.status === AgentStatus.ACTIVE).length,
    inactive: agents.filter(a => a.status === AgentStatus.INACTIVE).length,
    error: agents.filter(a => a.status === AgentStatus.ERROR).length,
    totalExecutions: agents.reduce((sum, a) => sum + a.metrics.total_executions, 0),
    totalCost: agents.reduce((sum, a) => sum + a.metrics.total_cost, 0),
    avgSuccessRate: agents.length > 0 
      ? agents.reduce((sum, a) => sum + agentSelectors.getSuccessRate(a), 0) / agents.length 
      : 0,
  };

  const handleAgentAction = async (agent: AgentModel, action: 'activate' | 'deactivate' | 'delete') => {
    try {
      // TODO: Implement actual API calls
      switch (action) {
        case 'activate':
          toast({
            title: 'Agent Activated',
            description: `${agent.name} has been activated successfully.`,
          });
          break;
        case 'deactivate':
          toast({
            title: 'Agent Deactivated',
            description: `${agent.name} has been deactivated.`,
          });
          break;
        case 'delete':
          toast({
            title: 'Agent Deleted',
            description: `${agent.name} has been deleted.`,
            variant: 'destructive',
          });
          break;
      }
      await refetch();
    } catch (error) {
      toast({
        title: 'Error',
        description: `Failed to ${action} agent: ${error}`,
        variant: 'destructive',
      });
    }
  };

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-red-500 mb-4">Failed to load agents</p>
          <Button onClick={refetch}>Retry</Button>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Agent Dashboard</h1>
          <p className="text-muted-foreground">
            Manage and monitor your AI agents
          </p>
        </div>
        <Button className="gap-2">
          <Plus className="h-4 w-4" />
          Create Agent
        </Button>
      </div>

      {/* Metrics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Agents</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.total}</div>
            <p className="text-xs text-muted-foreground">
              {metrics.active} active, {metrics.inactive} inactive
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Executions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.totalExecutions.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Across all agents
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.avgSuccessRate.toFixed(1)}%</div>
            <Progress value={metrics.avgSuccessRate} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Cost</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${metrics.totalCost.toFixed(4)}</div>
            <p className="text-xs text-muted-foreground">
              Cumulative usage cost
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search agents..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <Select value={statusFilter} onValueChange={(value) => setStatusFilter(value as AgentStatus | 'all')}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value={AgentStatus.ACTIVE}>Active</SelectItem>
            <SelectItem value={AgentStatus.INACTIVE}>Inactive</SelectItem>
            <SelectItem value={AgentStatus.TRAINING}>Training</SelectItem>
            <SelectItem value={AgentStatus.ERROR}>Error</SelectItem>
            <SelectItem value={AgentStatus.MAINTENANCE}>Maintenance</SelectItem>
          </SelectContent>
        </Select>

        <Select value={typeFilter} onValueChange={(value) => setTypeFilter(value as AgentType | 'all')}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            <SelectItem value={AgentType.CONVERSATIONAL}>Conversational</SelectItem>
            <SelectItem value={AgentType.TASK_EXECUTOR}>Task Executor</SelectItem>
            <SelectItem value={AgentType.KNOWLEDGE_WORKER}>Knowledge Worker</SelectItem>
            <SelectItem value={AgentType.CODE_ASSISTANT}>Code Assistant</SelectItem>
            <SelectItem value={AgentType.DATA_ANALYST}>Data Analyst</SelectItem>
            <SelectItem value={AgentType.CREATIVE_WRITER}>Creative Writer</SelectItem>
            <SelectItem value={AgentType.RESEARCH_ASSISTANT}>Research Assistant</SelectItem>
            <SelectItem value={AgentType.CUSTOM}>Custom</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Agent Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <AnimatePresence>
          {loading ? (
            // Loading skeletons
            Array.from({ length: 6 }).map((_, i) => (
              <Card key={i}>
                <CardHeader>
                  <div className="flex items-center space-x-4">
                    <Skeleton className="h-12 w-12 rounded-full" />
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-[200px]" />
                      <Skeleton className="h-4 w-[160px]" />
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-3/4" />
                  </div>
                </CardContent>
              </Card>
            ))
          ) : (
            filteredAgents.map((agent) => (
              <motion.div
                key={agent.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.2 }}
              >
                <AgentCard
                  agent={agent}
                  onAction={handleAgentAction}
                  onClick={() => setSelectedAgent(agent)}
                />
              </motion.div>
            ))
          )}
        </AnimatePresence>
      </div>

      {/* Empty State */}
      {!loading && filteredAgents.length === 0 && (
        <div className="text-center py-12">
          <div className="mx-auto w-24 h-24 bg-muted rounded-full flex items-center justify-center mb-4">
            <Search className="h-8 w-8 text-muted-foreground" />
          </div>
          <h3 className="text-lg font-semibold mb-2">No agents found</h3>
          <p className="text-muted-foreground mb-4">
            {searchQuery || statusFilter !== 'all' || typeFilter !== 'all'
              ? 'Try adjusting your search or filters'
              : 'Create your first agent to get started'}
          </p>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Create Agent
          </Button>
        </div>
      )}
    </div>
  );
}

interface AgentCardProps {
  agent: AgentModel;
  onAction: (agent: AgentModel, action: 'activate' | 'deactivate' | 'delete') => void;
  onClick: () => void;
}

function AgentCard({ agent, onAction, onClick }: AgentCardProps) {
  const statusColor = agentSelectors.getStatusColor(agent.status);
  const successRate = agentSelectors.getSuccessRate(agent);

  return (
    <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={onClick}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Avatar>
              <AvatarFallback className="bg-primary/10">
                {agent.name.slice(0, 2).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div>
              <CardTitle className="text-base">{agent.name}</CardTitle>
              <p className="text-sm text-muted-foreground">{agent.agent_type}</p>
            </div>
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
              <Button variant="ghost" size="sm">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {agent.status === AgentStatus.ACTIVE ? (
                <DropdownMenuItem onClick={() => onAction(agent, 'deactivate')}>
                  <Pause className="h-4 w-4 mr-2" />
                  Deactivate
                </DropdownMenuItem>
              ) : (
                <DropdownMenuItem onClick={() => onAction(agent, 'activate')}>
                  <Play className="h-4 w-4 mr-2" />
                  Activate
                </DropdownMenuItem>
              )}
              <DropdownMenuItem>
                <Settings className="h-4 w-4 mr-2" />
                Configure
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => onAction(agent, 'delete')}
                className="text-destructive"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Badge variant={statusColor === 'green' ? 'default' : 'secondary'}>
              {agent.status}
            </Badge>
            <span className="text-sm text-muted-foreground">
              v{agent.version}
            </span>
          </div>
          
          <p className="text-sm text-muted-foreground line-clamp-2">
            {agent.description}
          </p>
          
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <p className="text-muted-foreground">Executions</p>
              <p className="font-medium">{agent.metrics.total_executions}</p>
            </div>
            <div>
              <p className="text-muted-foreground">Success Rate</p>
              <p className="font-medium">{successRate.toFixed(1)}%</p>
            </div>
          </div>
          
          <div className="flex flex-wrap gap-1">
            {agent.tags.slice(0, 3).map((tag) => (
              <Badge key={tag} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
            {agent.tags.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{agent.tags.length - 3}
              </Badge>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
