"""
Knowledge Graph domain entities.

This module contains the core knowledge graph entities for the
Knowledge Graph Visualization System.
"""

import uuid
from datetime import UTC, datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Set

from pydantic import BaseModel, Field


class EntityType(str, Enum):
    """Knowledge entity type enumeration."""
    
    PERSON = "person"
    ORGANIZATION = "organization"
    LOCATION = "location"
    CONCEPT = "concept"
    DOCUMENT = "document"
    EVENT = "event"
    PRODUCT = "product"
    TECHNOLOGY = "technology"
    WORKFLOW = "workflow"
    AGENT = "agent"
    CUSTOM = "custom"


class RelationType(str, Enum):
    """Relationship type enumeration."""
    
    # General relationships
    RELATED_TO = "related_to"
    PART_OF = "part_of"
    CONTAINS = "contains"
    DEPENDS_ON = "depends_on"
    
    # Person relationships
    WORKS_FOR = "works_for"
    KNOWS = "knows"
    CREATED_BY = "created_by"
    
    # Organizational relationships
    OWNS = "owns"
    MANAGES = "manages"
    COLLABORATES_WITH = "collaborates_with"
    
    # Temporal relationships
    PRECEDES = "precedes"
    FOLLOWS = "follows"
    OCCURS_DURING = "occurs_during"
    
    # Semantic relationships
    IS_A = "is_a"
    INSTANCE_OF = "instance_of"
    SIMILAR_TO = "similar_to"
    OPPOSITE_OF = "opposite_of"
    
    # Workflow relationships
    TRIGGERS = "triggers"
    USES = "uses"
    PRODUCES = "produces"
    
    # Custom
    CUSTOM = "custom"


class KnowledgeEntity(BaseModel):
    """
    Knowledge entity domain entity.
    
    Represents a node in the knowledge graph with its properties and metadata.
    """
    
    id: uuid.UUID = Field(default_factory=uuid.uuid4, description="Unique entity identifier")
    name: str = Field(..., min_length=1, max_length=255, description="Entity name")
    description: Optional[str] = Field(None, max_length=2000, description="Entity description")
    entity_type: EntityType = Field(..., description="Entity type")
    
    # Properties and attributes
    properties: Dict[str, Any] = Field(default_factory=dict, description="Entity properties")
    attributes: Dict[str, Any] = Field(default_factory=dict, description="Entity attributes")
    
    # Metadata
    tags: List[str] = Field(default_factory=list, description="Entity tags")
    categories: List[str] = Field(default_factory=list, description="Entity categories")
    confidence_score: float = Field(default=1.0, ge=0.0, le=1.0, description="Confidence score")
    
    # Source information
    source_id: Optional[str] = Field(None, description="Source identifier")
    source_type: Optional[str] = Field(None, description="Source type")
    source_metadata: Dict[str, Any] = Field(default_factory=dict, description="Source metadata")
    
    # Ownership and access
    created_by: uuid.UUID = Field(..., description="Creator user ID")
    is_public: bool = Field(default=False, description="Whether entity is publicly accessible")
    
    # Timestamps
    created_at: datetime = Field(default_factory=lambda: datetime.now(UTC), description="Creation timestamp")
    updated_at: datetime = Field(default_factory=lambda: datetime.now(UTC), description="Last update timestamp")
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            uuid.UUID: lambda v: str(v),
        }

    def add_property(self, key: str, value: Any) -> None:
        """Add a property to the entity."""
        self.properties[key] = value
        self.updated_at = datetime.now(UTC)

    def remove_property(self, key: str) -> None:
        """Remove a property from the entity."""
        if key in self.properties:
            del self.properties[key]
            self.updated_at = datetime.now(UTC)

    def add_tag(self, tag: str) -> None:
        """Add a tag to the entity."""
        if tag not in self.tags:
            self.tags.append(tag)
            self.updated_at = datetime.now(UTC)

    def remove_tag(self, tag: str) -> None:
        """Remove a tag from the entity."""
        if tag in self.tags:
            self.tags.remove(tag)
            self.updated_at = datetime.now(UTC)

    def update_confidence(self, score: float) -> None:
        """Update confidence score."""
        self.confidence_score = max(0.0, min(1.0, score))
        self.updated_at = datetime.now(UTC)

    def model_dump(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "id": str(self.id),
            "name": self.name,
            "description": self.description,
            "entity_type": self.entity_type.value,
            "properties": self.properties,
            "attributes": self.attributes,
            "tags": self.tags,
            "categories": self.categories,
            "confidence_score": self.confidence_score,
            "source_id": self.source_id,
            "source_type": self.source_type,
            "source_metadata": self.source_metadata,
            "created_by": str(self.created_by),
            "is_public": self.is_public,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
        }


class KnowledgeRelationship(BaseModel):
    """
    Knowledge relationship domain entity.
    
    Represents an edge in the knowledge graph connecting two entities.
    """
    
    id: uuid.UUID = Field(default_factory=uuid.uuid4, description="Unique relationship identifier")
    source_entity_id: uuid.UUID = Field(..., description="Source entity ID")
    target_entity_id: uuid.UUID = Field(..., description="Target entity ID")
    relation_type: RelationType = Field(..., description="Relationship type")
    
    # Relationship properties
    properties: Dict[str, Any] = Field(default_factory=dict, description="Relationship properties")
    weight: float = Field(default=1.0, ge=0.0, description="Relationship weight/strength")
    confidence_score: float = Field(default=1.0, ge=0.0, le=1.0, description="Confidence score")
    
    # Directional information
    is_directed: bool = Field(default=True, description="Whether relationship is directed")
    
    # Metadata
    description: Optional[str] = Field(None, max_length=1000, description="Relationship description")
    tags: List[str] = Field(default_factory=list, description="Relationship tags")
    
    # Source information
    source_id: Optional[str] = Field(None, description="Source identifier")
    source_type: Optional[str] = Field(None, description="Source type")
    source_metadata: Dict[str, Any] = Field(default_factory=dict, description="Source metadata")
    
    # Ownership and access
    created_by: uuid.UUID = Field(..., description="Creator user ID")
    
    # Timestamps
    created_at: datetime = Field(default_factory=lambda: datetime.now(UTC), description="Creation timestamp")
    updated_at: datetime = Field(default_factory=lambda: datetime.now(UTC), description="Last update timestamp")
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            uuid.UUID: lambda v: str(v),
        }

    def update_weight(self, weight: float) -> None:
        """Update relationship weight."""
        self.weight = max(0.0, weight)
        self.updated_at = datetime.now(UTC)

    def update_confidence(self, score: float) -> None:
        """Update confidence score."""
        self.confidence_score = max(0.0, min(1.0, score))
        self.updated_at = datetime.now(UTC)

    def add_property(self, key: str, value: Any) -> None:
        """Add a property to the relationship."""
        self.properties[key] = value
        self.updated_at = datetime.now(UTC)

    def model_dump(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "id": str(self.id),
            "source_entity_id": str(self.source_entity_id),
            "target_entity_id": str(self.target_entity_id),
            "relation_type": self.relation_type.value,
            "properties": self.properties,
            "weight": self.weight,
            "confidence_score": self.confidence_score,
            "is_directed": self.is_directed,
            "description": self.description,
            "tags": self.tags,
            "source_id": self.source_id,
            "source_type": self.source_type,
            "source_metadata": self.source_metadata,
            "created_by": str(self.created_by),
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
        }


class KnowledgeGraph(BaseModel):
    """
    Knowledge graph domain entity.
    
    Represents a collection of entities and relationships forming a knowledge graph.
    """
    
    id: uuid.UUID = Field(default_factory=uuid.uuid4, description="Unique graph identifier")
    name: str = Field(..., min_length=1, max_length=255, description="Graph name")
    description: Optional[str] = Field(None, max_length=2000, description="Graph description")
    
    # Graph structure
    entities: List[KnowledgeEntity] = Field(default_factory=list, description="Graph entities")
    relationships: List[KnowledgeRelationship] = Field(default_factory=list, description="Graph relationships")
    
    # Metadata
    tags: List[str] = Field(default_factory=list, description="Graph tags")
    categories: List[str] = Field(default_factory=list, description="Graph categories")
    
    # Ownership and access
    created_by: uuid.UUID = Field(..., description="Creator user ID")
    is_public: bool = Field(default=False, description="Whether graph is publicly accessible")
    
    # Timestamps
    created_at: datetime = Field(default_factory=lambda: datetime.now(UTC), description="Creation timestamp")
    updated_at: datetime = Field(default_factory=lambda: datetime.now(UTC), description="Last update timestamp")
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            uuid.UUID: lambda v: str(v),
        }

    def add_entity(self, entity: KnowledgeEntity) -> None:
        """Add an entity to the graph."""
        if entity not in self.entities:
            self.entities.append(entity)
            self.updated_at = datetime.now(UTC)

    def remove_entity(self, entity_id: uuid.UUID) -> None:
        """Remove an entity and its relationships from the graph."""
        # Remove entity
        self.entities = [e for e in self.entities if e.id != entity_id]
        
        # Remove relationships involving this entity
        self.relationships = [
            r for r in self.relationships 
            if r.source_entity_id != entity_id and r.target_entity_id != entity_id
        ]
        
        self.updated_at = datetime.now(UTC)

    def add_relationship(self, relationship: KnowledgeRelationship) -> None:
        """Add a relationship to the graph."""
        # Verify entities exist
        entity_ids = {e.id for e in self.entities}
        if (relationship.source_entity_id in entity_ids and 
            relationship.target_entity_id in entity_ids):
            
            if relationship not in self.relationships:
                self.relationships.append(relationship)
                self.updated_at = datetime.now(UTC)

    def remove_relationship(self, relationship_id: uuid.UUID) -> None:
        """Remove a relationship from the graph."""
        self.relationships = [r for r in self.relationships if r.id != relationship_id]
        self.updated_at = datetime.now(UTC)

    def get_entity_neighbors(self, entity_id: uuid.UUID) -> Set[uuid.UUID]:
        """Get neighboring entities of a given entity."""
        neighbors = set()
        for rel in self.relationships:
            if rel.source_entity_id == entity_id:
                neighbors.add(rel.target_entity_id)
            elif rel.target_entity_id == entity_id and not rel.is_directed:
                neighbors.add(rel.source_entity_id)
        return neighbors

    def get_entity_relationships(self, entity_id: uuid.UUID) -> List[KnowledgeRelationship]:
        """Get all relationships involving a given entity."""
        return [
            rel for rel in self.relationships
            if rel.source_entity_id == entity_id or rel.target_entity_id == entity_id
        ]

    def get_subgraph(self, entity_ids: Set[uuid.UUID]) -> 'KnowledgeGraph':
        """Extract a subgraph containing only specified entities."""
        # Filter entities
        filtered_entities = [e for e in self.entities if e.id in entity_ids]
        
        # Filter relationships
        filtered_relationships = [
            r for r in self.relationships
            if r.source_entity_id in entity_ids and r.target_entity_id in entity_ids
        ]
        
        return KnowledgeGraph(
            name=f"{self.name} (Subgraph)",
            description=f"Subgraph of {self.name}",
            entities=filtered_entities,
            relationships=filtered_relationships,
            created_by=self.created_by,
            is_public=self.is_public,
        )

    def model_dump(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "id": str(self.id),
            "name": self.name,
            "description": self.description,
            "entities": [entity.model_dump() for entity in self.entities],
            "relationships": [rel.model_dump() for rel in self.relationships],
            "tags": self.tags,
            "categories": self.categories,
            "created_by": str(self.created_by),
            "is_public": self.is_public,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
        }
