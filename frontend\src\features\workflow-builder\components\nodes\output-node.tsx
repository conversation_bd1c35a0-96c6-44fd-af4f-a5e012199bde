'use client';

import React, { memo, useCallback } from 'react';
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow';
import { cn } from '@/shared/lib/utils';
import { Badge } from '@/shared/ui/badge';
import { Button } from '@/shared/ui/button';
import { NodeData, PortType } from '../../types';
import {
  ArrowLeft,
  Settings,
  Download,
  Copy,
  CheckCircle,
  Type,
  Hash,
  ToggleLeft,
  FileText,
  Image,
  Music,
  Video,
  Database,
  User,
} from 'lucide-react';

interface OutputNodeData extends NodeData {
  outputType: PortType;
  outputName: string;
  format?: string;
  destination?: string;
  currentValue?: any;
  lastUpdated?: string;
  exported?: boolean;
}

interface OutputNodeProps extends NodeProps {
  data: OutputNodeData;
}

// Output type icon mapping
const getOutputTypeIcon = (type: PortType) => {
  switch (type) {
    case PortType.STRING:
      return Type;
    case PortType.NUMBER:
      return Hash;
    case PortType.BOOLEAN:
      return ToggleLeft;
    case PortType.FILE:
      return FileText;
    case PortType.IMAGE:
      return Image;
    case PortType.AUDIO:
      return Music;
    case PortType.VIDEO:
      return Video;
    case PortType.OBJECT:
    case PortType.ARRAY:
      return Database;
    case PortType.AGENT:
      return User;
    default:
      return Type;
  }
};

// Output type color mapping
const getOutputTypeColor = (type: PortType) => {
  switch (type) {
    case PortType.STRING:
      return 'text-emerald-600 bg-emerald-50 border-emerald-200';
    case PortType.NUMBER:
      return 'text-teal-600 bg-teal-50 border-teal-200';
    case PortType.BOOLEAN:
      return 'text-violet-600 bg-violet-50 border-violet-200';
    case PortType.FILE:
      return 'text-amber-600 bg-amber-50 border-amber-200';
    case PortType.IMAGE:
      return 'text-rose-600 bg-rose-50 border-rose-200';
    case PortType.AUDIO:
      return 'text-indigo-600 bg-indigo-50 border-indigo-200';
    case PortType.VIDEO:
      return 'text-red-600 bg-red-50 border-red-200';
    case PortType.OBJECT:
    case PortType.ARRAY:
      return 'text-gray-600 bg-gray-50 border-gray-200';
    case PortType.AGENT:
      return 'text-cyan-600 bg-cyan-50 border-cyan-200';
    default:
      return 'text-slate-600 bg-slate-50 border-slate-200';
  }
};

// Format value for display
const formatValue = (value: any, type: PortType): string => {
  if (value === null || value === undefined) return 'No output';
  
  switch (type) {
    case PortType.STRING:
      return value.length > 50 ? `"${value.substring(0, 50)}..."` : `"${value}"`;
    case PortType.NUMBER:
      return value.toString();
    case PortType.BOOLEAN:
      return value ? 'true' : 'false';
    case PortType.OBJECT:
      return JSON.stringify(value, null, 2).substring(0, 100) + '...';
    case PortType.ARRAY:
      return `Array(${value.length}) [${value.slice(0, 3).join(', ')}${value.length > 3 ? '...' : ''}]`;
    case PortType.FILE:
      return value.name || 'File';
    case PortType.IMAGE:
      return value.name || 'Image';
    default:
      return value.toString().substring(0, 50);
  }
};

// Get file size display
const getFileSize = (value: any): string => {
  if (value && value.size) {
    const size = value.size;
    if (size < 1024) return `${size} B`;
    if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`;
    return `${(size / (1024 * 1024)).toFixed(1)} MB`;
  }
  return '';
};

export const OutputNode = memo<OutputNodeProps>(({ 
  data, 
  selected, 
  dragging,
  id 
}) => {
  const { 
    outputType,
    outputName,
    format,
    destination,
    currentValue,
    lastUpdated,
    exported = false
  } = data;

  const OutputIcon = getOutputTypeIcon(outputType);
  const outputColorClass = getOutputTypeColor(outputType);
  const hasValue = currentValue !== undefined && currentValue !== null;

  const handleConfigure = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    console.log('Configure output:', outputName);
  }, [outputName]);

  const handleExport = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    console.log('Export output:', outputName, currentValue);
  }, [outputName, currentValue]);

  const handleCopy = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    if (currentValue) {
      navigator.clipboard.writeText(
        typeof currentValue === 'string' 
          ? currentValue 
          : JSON.stringify(currentValue, null, 2)
      );
    }
  }, [currentValue]);

  return (
    <div
      className={cn(
        'relative bg-background border-2 rounded-lg shadow-sm transition-all duration-200',
        'min-w-[200px] max-w-[280px]',
        selected && 'border-primary ring-2 ring-primary/20',
        dragging && 'shadow-lg scale-105',
        hasValue && 'border-green-400 shadow-green-100',
        exported && 'border-emerald-400 shadow-emerald-100'
      )}
    >
      {/* Input Handle */}
      <Handle
        type="target"
        position={Position.Left}
        className="w-3 h-3 border-2 border-background bg-muted-foreground hover:bg-primary transition-colors"
        style={{ left: -6 }}
      />

      {/* Status indicator overlay */}
      <div className={cn(
        'absolute top-0 left-0 w-full h-1 rounded-t-lg transition-all duration-300',
        exported && 'bg-gradient-to-r from-emerald-400 to-emerald-600',
        hasValue && !exported && 'bg-gradient-to-r from-green-400 to-green-600',
        !hasValue && 'bg-gradient-to-r from-slate-300 to-slate-400'
      )} />

      {/* Header */}
      <div className="p-3">
        <div className="flex items-start justify-between mb-2">
          <div className="flex items-center space-x-2 min-w-0 flex-1">
            <ArrowLeft className="h-4 w-4 text-muted-foreground" />
            <div className={cn(
              'p-1.5 rounded-md border',
              outputColorClass
            )}>
              <OutputIcon className="h-4 w-4" />
            </div>
            <div className="min-w-0 flex-1">
              <h3 className="font-semibold text-sm truncate">
                {outputName}
              </h3>
              <div className="flex items-center gap-1 mt-1">
                <Badge variant="outline" className="text-xs">
                  {outputType}
                </Badge>
                {exported && (
                  <Badge variant="default" className="text-xs bg-emerald-500">
                    Exported
                  </Badge>
                )}
              </div>
            </div>
          </div>
          
          {exported && (
            <CheckCircle className="h-4 w-4 text-emerald-600" />
          )}
        </div>

        {data.description && (
          <p className="text-xs text-muted-foreground line-clamp-2 mb-2">
            {data.description}
          </p>
        )}

        {/* Current Value Display */}
        <div className="mb-3">
          <div className="text-xs text-muted-foreground mb-1">Output Value:</div>
          <div className={cn(
            'text-sm font-mono bg-muted p-2 rounded border max-h-20 overflow-y-auto',
            hasValue ? 'border-green-200 bg-green-50' : 'border-dashed'
          )}>
            {hasValue ? (
              <span className="text-green-800 whitespace-pre-wrap">
                {formatValue(currentValue, outputType)}
              </span>
            ) : (
              <span className="text-muted-foreground italic">
                No output generated
              </span>
            )}
          </div>
          
          {/* File size for file types */}
          {hasValue && (outputType === PortType.FILE || outputType === PortType.IMAGE || 
                       outputType === PortType.AUDIO || outputType === PortType.VIDEO) && (
            <div className="text-xs text-muted-foreground mt-1">
              Size: {getFileSize(currentValue)}
            </div>
          )}
        </div>

        {/* Format and Destination */}
        {(format || destination) && (
          <div className="mb-3 space-y-1">
            {format && (
              <div className="flex justify-between text-xs">
                <span className="text-muted-foreground">Format:</span>
                <Badge variant="secondary" className="text-xs">{format}</Badge>
              </div>
            )}
            {destination && (
              <div className="flex justify-between text-xs">
                <span className="text-muted-foreground">Destination:</span>
                <span className="font-mono text-xs truncate max-w-[120px]" title={destination}>
                  {destination}
                </span>
              </div>
            )}
          </div>
        )}

        {/* Last Updated */}
        {lastUpdated && (
          <div className="mb-3">
            <div className="text-xs text-muted-foreground">
              Last updated: {new Date(lastUpdated).toLocaleString()}
            </div>
          </div>
        )}

        {/* Controls */}
        <div className="flex gap-1">
          {hasValue && (
            <>
              <Button size="sm" variant="outline" onClick={handleCopy}>
                <Copy className="h-3 w-3" />
              </Button>
              <Button size="sm" variant="outline" onClick={handleExport}>
                <Download className="h-3 w-3" />
              </Button>
            </>
          )}
          <Button size="sm" variant="ghost" onClick={handleConfigure} className="flex-1">
            <Settings className="h-3 w-3 mr-1" />
            Configure
          </Button>
        </div>
      </div>

      {/* Value indicator overlay */}
      {hasValue && (
        <div className={cn(
          'absolute inset-0 rounded-lg pointer-events-none',
          exported 
            ? 'bg-gradient-to-br from-emerald-500/5 to-emerald-600/10'
            : 'bg-gradient-to-br from-green-500/5 to-green-600/10'
        )} />
      )}

      {/* Export success indicator */}
      {exported && (
        <div className="absolute -top-2 -right-2 bg-emerald-500 text-white text-xs px-2 py-1 rounded-full">
          ✓
        </div>
      )}
    </div>
  );
});

OutputNode.displayName = 'OutputNode';
