'use client';

import React, { useCallback, useState, useRef } from 'react';
import { useReactFlow } from 'reactflow';
import { cn } from '@/shared/lib/utils';
import { Button } from '@/shared/ui/button';
import { useToast } from '@/shared/ui/use-toast';
import { 
  WorkflowNode, 
  WorkflowEdge, 
  NodeTemplate, 
  NodeChangeEvent, 
  EdgeChangeEvent,
  ConnectionEvent,
  WorkflowStatus,
  NodeType,
  PortType
} from '../types';
import { AgentModel, createAgent } from '@/entities/agent/model';
import { AgentType, AgentStatus } from '@/shared/types';
import WorkflowCanvas from './canvas/workflow-canvas';
import NodeLibrary from './node-library/node-library';
import { 
  Sidebar, 
  SidebarContent, 
  SidebarProvider 
} from '@/shared/ui/sidebar';
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from '@/shared/ui/resizable';

interface WorkflowBuilderProps {
  initialNodes?: WorkflowNode[];
  initialEdges?: WorkflowEdge[];
  workflowId?: string;
  readonly?: boolean;
  className?: string;
  onSave?: (nodes: WorkflowNode[], edges: WorkflowEdge[]) => void;
  onExecute?: (workflowId: string) => void;
  onNodesChange?: (nodes: WorkflowNode[]) => void;
  onEdgesChange?: (edges: WorkflowEdge[]) => void;
}

let nodeId = 0;
const getId = () => `node_${nodeId++}`;

export const WorkflowBuilder: React.FC<WorkflowBuilderProps> = ({
  initialNodes = [],
  initialEdges = [],
  workflowId,
  readonly = false,
  className,
  onSave,
  onExecute,
  onNodesChange,
  onEdgesChange,
}) => {
  const [nodes, setNodes] = useState<WorkflowNode[]>(initialNodes);
  const [edges, setEdges] = useState<WorkflowEdge[]>(initialEdges);
  const [workflowStatus, setWorkflowStatus] = useState<WorkflowStatus>(WorkflowStatus.DRAFT);
  const [isLibraryCollapsed, setIsLibraryCollapsed] = useState(false);
  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const { toast } = useToast();

  // Handle node changes
  const handleNodesChange = useCallback((changes: NodeChangeEvent[]) => {
    setNodes((nds) => {
      const updatedNodes = [...nds];
      changes.forEach((change) => {
        const nodeIndex = updatedNodes.findIndex(node => node.id === change.id);
        if (nodeIndex !== -1) {
          switch (change.type) {
            case 'position':
              if (change.position) {
                updatedNodes[nodeIndex] = {
                  ...updatedNodes[nodeIndex],
                  position: change.position,
                };
              }
              break;
            case 'selection':
              updatedNodes[nodeIndex] = {
                ...updatedNodes[nodeIndex],
                selected: change.selected,
              };
              break;
            case 'remove':
              updatedNodes.splice(nodeIndex, 1);
              break;
          }
        }
      });
      onNodesChange?.(updatedNodes);
      return updatedNodes;
    });
  }, [onNodesChange]);

  // Handle edge changes
  const handleEdgesChange = useCallback((changes: EdgeChangeEvent[]) => {
    setEdges((eds) => {
      const updatedEdges = [...eds];
      changes.forEach((change) => {
        const edgeIndex = updatedEdges.findIndex(edge => edge.id === change.id);
        if (edgeIndex !== -1) {
          switch (change.type) {
            case 'select':
              updatedEdges[edgeIndex] = {
                ...updatedEdges[edgeIndex],
                selected: change.selected,
              };
              break;
            case 'remove':
              updatedEdges.splice(edgeIndex, 1);
              break;
          }
        }
      });
      onEdgesChange?.(updatedEdges);
      return updatedEdges;
    });
  }, [onEdgesChange]);

  // Handle new connections
  const handleConnect = useCallback((connection: ConnectionEvent) => {
    const newEdge: WorkflowEdge = {
      id: `edge-${connection.source}-${connection.target}`,
      source: connection.source,
      target: connection.target,
      sourceHandle: connection.sourceHandle,
      targetHandle: connection.targetHandle,
      type: 'smoothstep',
      animated: workflowStatus === WorkflowStatus.ACTIVE,
    };

    setEdges((eds) => [...eds, newEdge]);
    onEdgesChange?.([...edges, newEdge]);
  }, [edges, onEdgesChange, workflowStatus]);

  // Handle node drag start from library
  const handleNodeDragStart = useCallback((event: React.DragEvent, nodeTemplate: NodeTemplate) => {
    event.dataTransfer.effectAllowed = 'move';
  }, []);

  // Handle drop on canvas
  const handleDrop = useCallback((event: React.DragEvent) => {
    event.preventDefault();

    if (!reactFlowWrapper.current) return;

    const reactFlowBounds = reactFlowWrapper.current.getBoundingClientRect();
    const data = event.dataTransfer.getData('application/reactflow');
    
    if (!data) return;

    try {
      const { template } = JSON.parse(data);
      const position = {
        x: event.clientX - reactFlowBounds.left - 150,
        y: event.clientY - reactFlowBounds.top - 50,
      };

      // Create node data based on template
      let nodeData = { ...template.defaultData };

      // Special handling for agent nodes
      if (template.type === NodeType.AGENT) {
        const agentData = createAgent({
          id: getId(),
          name: template.name,
          description: template.description,
          agent_type: template.defaultData.agent?.agent_type || AgentType.CHAT,
          status: AgentStatus.IDLE,
          capabilities: template.inputs.map(input => ({
            name: input.name,
            description: input.description || '',
            parameters: {},
            required: input.required || false,
          })),
        });
        nodeData.agent = agentData;
      }

      const newNode: WorkflowNode = {
        id: getId(),
        type: template.type,
        position,
        data: nodeData,
        connectable: !readonly,
        deletable: !readonly,
        selectable: !readonly,
      };

      setNodes((nds) => {
        const updatedNodes = [...nds, newNode];
        onNodesChange?.(updatedNodes);
        return updatedNodes;
      });

      toast({
        title: 'Node Added',
        description: `${template.name} has been added to the workflow.`,
      });
    } catch (error) {
      console.error('Error parsing dropped data:', error);
      toast({
        title: 'Error',
        description: 'Failed to add node to workflow.',
        variant: 'destructive',
      });
    }
  }, [readonly, onNodesChange, toast]);

  // Handle workflow save
  const handleSave = useCallback(() => {
    if (onSave) {
      onSave(nodes, edges);
      toast({
        title: 'Workflow Saved',
        description: 'Your workflow has been saved successfully.',
      });
    }
  }, [nodes, edges, onSave, toast]);

  // Handle workflow execution
  const handleExecute = useCallback(() => {
    if (nodes.length === 0) {
      toast({
        title: 'Empty Workflow',
        description: 'Add some nodes to your workflow before executing.',
        variant: 'destructive',
      });
      return;
    }

    setWorkflowStatus(WorkflowStatus.ACTIVE);
    
    if (onExecute && workflowId) {
      onExecute(workflowId);
    }

    toast({
      title: 'Workflow Started',
      description: 'Your workflow is now executing.',
    });
  }, [nodes.length, onExecute, workflowId, toast]);

  // Handle workflow pause
  const handlePause = useCallback(() => {
    setWorkflowStatus(WorkflowStatus.PAUSED);
    toast({
      title: 'Workflow Paused',
      description: 'Workflow execution has been paused.',
    });
  }, [toast]);

  // Handle workflow stop
  const handleStop = useCallback(() => {
    setWorkflowStatus(WorkflowStatus.DRAFT);
    toast({
      title: 'Workflow Stopped',
      description: 'Workflow execution has been stopped.',
    });
  }, [toast]);

  // Handle workflow reset
  const handleReset = useCallback(() => {
    setNodes([]);
    setEdges([]);
    setWorkflowStatus(WorkflowStatus.DRAFT);
    onNodesChange?.([]);
    onEdgesChange?.([]);
    toast({
      title: 'Workflow Reset',
      description: 'Workflow has been cleared.',
    });
  }, [onNodesChange, onEdgesChange, toast]);

  return (
    <div className={cn('h-full w-full flex', className)}>
      <ResizablePanelGroup direction="horizontal" className="h-full">
        {/* Node Library Panel */}
        <ResizablePanel 
          defaultSize={isLibraryCollapsed ? 5 : 25} 
          minSize={5} 
          maxSize={40}
          className="min-w-0"
        >
          <NodeLibrary
            onNodeDragStart={handleNodeDragStart}
            collapsed={isLibraryCollapsed}
            onToggleCollapse={() => setIsLibraryCollapsed(!isLibraryCollapsed)}
            className="h-full"
          />
        </ResizablePanel>

        <ResizableHandle withHandle />

        {/* Canvas Panel */}
        <ResizablePanel defaultSize={isLibraryCollapsed ? 95 : 75} minSize={50}>
          <div ref={reactFlowWrapper} className="h-full w-full">
            <WorkflowCanvas
              nodes={nodes}
              edges={edges}
              onNodesChange={handleNodesChange}
              onEdgesChange={handleEdgesChange}
              onConnect={handleConnect}
              onDrop={handleDrop}
              onDragOver={(e) => {
                e.preventDefault();
                e.dataTransfer.dropEffect = 'move';
              }}
              readonly={readonly}
              workflowStatus={workflowStatus}
              onSave={handleSave}
              onExecute={handleExecute}
              onPause={handlePause}
              onStop={handleStop}
              onReset={handleReset}
              className="h-full"
            />
          </div>
        </ResizablePanel>
      </ResizablePanelGroup>
    </div>
  );
};

export default WorkflowBuilder;
