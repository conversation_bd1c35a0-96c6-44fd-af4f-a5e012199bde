"""
Agent SQLAlchemy model.

This module contains the SQLAlchemy model for the agents table.
"""

import uuid
from datetime import datetime
from typing import TYPE_CHECKING

from sqlalchemy import (
    Boolean,
    DateTime,
    Enum,
    Foreign<PERSON>ey,
    Integer,
    JSON,
    String,
    Text,
    UUID,
    Float,
)
from sqlalchemy.orm import Mapped, mapped_column, relationship

from src.domain.entities.agent import AgentStatus, AgentType
from src.infrastructure.database.models.base import Base

if TYPE_CHECKING:
    from src.infrastructure.database.models.user import UserModel


class AgentModel(Base):
    """
    Agent SQLAlchemy model.

    Represents the agents table in the database with all
    necessary constraints and indexes.
    """

    # Agent identification
    name: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        index=True,
        doc="Agent name"
    )

    description: Mapped[str | None] = mapped_column(
        Text,
        nullable=True,
        doc="Agent description"
    )

    # Classification
    agent_type: Mapped[AgentType] = mapped_column(
        Enum(AgentType),
        nullable=False,
        index=True,
        doc="Agent type"
    )

    version: Mapped[str] = mapped_column(
        String(50),
        default="1.0.0",
        nullable=False,
        doc="Agent version"
    )

    # State
    status: Mapped[AgentStatus] = mapped_column(
        Enum(AgentStatus),
        default=AgentStatus.IDLE,
        nullable=False,
        index=True,
        doc="Agent status"
    )

    current_task_id: Mapped[uuid.UUID | None] = mapped_column(
        UUID(as_uuid=True),
        nullable=True,
        doc="Current task ID"
    )

    # Configuration (stored as JSON)
    configuration: Mapped[dict] = mapped_column(
        JSON,
        default=dict,
        nullable=False,
        doc="Agent configuration as JSON"
    )

    # Capabilities (stored as JSON)
    capabilities: Mapped[list] = mapped_column(
        JSON,
        default=list,
        nullable=False,
        doc="Agent capabilities as JSON array"
    )

    # Relationships
    created_by: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Creator user ID"
    )

    parent_agent_id: Mapped[uuid.UUID | None] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("agents.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        doc="Parent agent ID"
    )

    # Performance metrics (stored as JSON)
    metrics: Mapped[dict] = mapped_column(
        JSON,
        default=dict,
        nullable=False,
        doc="Performance metrics as JSON"
    )

    # Tags and metadata
    tags: Mapped[list] = mapped_column(
        JSON,
        default=list,
        nullable=False,
        doc="Agent tags as JSON array"
    )

    metadata: Mapped[dict] = mapped_column(
        JSON,
        default=dict,
        nullable=False,
        doc="Agent metadata as JSON"
    )

    # Child agent IDs (stored as JSON)
    child_agent_ids: Mapped[list] = mapped_column(
        JSON,
        default=list,
        nullable=False,
        doc="Child agent IDs as JSON array"
    )

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=datetime.utcnow,
        nullable=False,
        index=True,
        doc="Creation timestamp"
    )

    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=datetime.utcnow,
        onupdate=datetime.utcnow,
        nullable=False,
        index=True,
        doc="Last update timestamp"
    )

    # Relationships
    creator: Mapped["UserModel"] = relationship(
        "UserModel",
        foreign_keys=[created_by],
        back_populates="agents"
    )

    parent_agent: Mapped["AgentModel"] = relationship(
        "AgentModel",
        remote_side="AgentModel.id",
        back_populates="child_agents"
    )

    child_agents: Mapped[list["AgentModel"]] = relationship(
        "AgentModel",
        back_populates="parent_agent"
    )

    def to_domain(self) -> "Agent":
        """
        Convert SQLAlchemy model to domain entity.

        Returns:
            Agent domain entity
        """
        from src.domain.entities.agent import (
            Agent,
            AgentCapability,
            AgentConfiguration,
            AgentMetrics,
        )

        # Convert configuration
        config_data = self.configuration or {}
        configuration = AgentConfiguration(
            model_name=config_data.get("model_name", "gpt-4"),
            temperature=config_data.get("temperature", 0.7),
            max_tokens=config_data.get("max_tokens", 2048),
            timeout_seconds=config_data.get("timeout_seconds", 300),
            retry_attempts=config_data.get("retry_attempts", 3),
            memory_enabled=config_data.get("memory_enabled", True),
            tools_enabled=config_data.get("tools_enabled", True),
            custom_settings=config_data.get("custom_settings", {}),
        )

        # Convert capabilities
        capabilities = []
        for cap_data in self.capabilities or []:
            if isinstance(cap_data, dict):
                capabilities.append(AgentCapability(
                    name=cap_data.get("name", ""),
                    description=cap_data.get("description", ""),
                    parameters=cap_data.get("parameters", {}),
                    required=cap_data.get("required", False),
                ))

        # Convert metrics
        metrics_data = self.metrics or {}
        metrics = AgentMetrics(
            total_executions=metrics_data.get("total_executions", 0),
            successful_executions=metrics_data.get("successful_executions", 0),
            failed_executions=metrics_data.get("failed_executions", 0),
            average_execution_time=metrics_data.get("average_execution_time", 0.0),
            last_execution_time=metrics_data.get("last_execution_time"),
            total_tokens_used=metrics_data.get("total_tokens_used", 0),
            total_cost=metrics_data.get("total_cost", 0.0),
        )

        return Agent(
            id=self.id,
            name=self.name,
            description=self.description or "",
            agent_type=self.agent_type,
            version=self.version,
            configuration=configuration,
            capabilities=capabilities,
            status=self.status,
            current_task_id=self.current_task_id,
            created_at=self.created_at,
            updated_at=self.updated_at,
            created_by=self.created_by,
            metrics=metrics,
            parent_agent_id=self.parent_agent_id,
            child_agent_ids=self.child_agent_ids or [],
            tags=self.tags or [],
            metadata=self.metadata or {},
        )

    @classmethod
    def from_domain(cls, agent: "Agent") -> "AgentModel":
        """
        Create SQLAlchemy model from domain entity.

        Args:
            agent: Agent domain entity

        Returns:
            AgentModel instance
        """
        # Convert configuration to dict
        configuration = {
            "model_name": agent.configuration.model_name,
            "temperature": agent.configuration.temperature,
            "max_tokens": agent.configuration.max_tokens,
            "timeout_seconds": agent.configuration.timeout_seconds,
            "retry_attempts": agent.configuration.retry_attempts,
            "memory_enabled": agent.configuration.memory_enabled,
            "tools_enabled": agent.configuration.tools_enabled,
            "custom_settings": agent.configuration.custom_settings,
        }

        # Convert capabilities to list of dicts
        capabilities = [
            {
                "name": cap.name,
                "description": cap.description,
                "parameters": cap.parameters,
                "required": cap.required,
            }
            for cap in agent.capabilities
        ]

        # Convert metrics to dict
        metrics = {
            "total_executions": agent.metrics.total_executions,
            "successful_executions": agent.metrics.successful_executions,
            "failed_executions": agent.metrics.failed_executions,
            "average_execution_time": agent.metrics.average_execution_time,
            "last_execution_time": agent.metrics.last_execution_time.isoformat() if agent.metrics.last_execution_time else None,
            "total_tokens_used": agent.metrics.total_tokens_used,
            "total_cost": agent.metrics.total_cost,
        }

        return cls(
            id=agent.id,
            name=agent.name,
            description=agent.description,
            agent_type=agent.agent_type,
            version=agent.version,
            configuration=configuration,
            capabilities=capabilities,
            status=agent.status,
            current_task_id=agent.current_task_id,
            created_at=agent.created_at,
            updated_at=agent.updated_at,
            created_by=agent.created_by,
            metrics=metrics,
            parent_agent_id=agent.parent_agent_id,
            child_agent_ids=agent.child_agent_ids,
            tags=agent.tags,
            metadata=agent.metadata,
        )
