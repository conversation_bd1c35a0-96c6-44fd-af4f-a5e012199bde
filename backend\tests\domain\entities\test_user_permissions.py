"""
User Entity Permission Tests

Test suite for the User entity's permission and role checking capabilities.
"""

import pytest
from datetime import datetime, UTC

from src.domain.entities.user import Permission, User, UserRole, UserStatus
from src.domain.value_objects.email import Email
from src.domain.value_objects.username import Username


@pytest.fixture
def admin_user():
    """Create admin user for testing."""
    return User(
        id="admin-123",
        email=Email("<EMAIL>"),
        username=Username("admin"),
        full_name="Admin User",
        hashed_password="hashed_password",
        role=UserRole.ADMIN,
        status=UserStatus.ACTIVE,
        is_active=True,
        is_verified=True,
    )


@pytest.fixture
def moderator_user():
    """Create moderator user for testing."""
    return User(
        id="mod-123",
        email=Email("<EMAIL>"),
        username=Username("moderator"),
        full_name="Moderator User",
        hashed_password="hashed_password",
        role=UserRole.MODERATOR,
        status=UserStatus.ACTIVE,
        is_active=True,
        is_verified=True,
    )


@pytest.fixture
def regular_user():
    """Create regular user for testing."""
    return User(
        id="user-123",
        email=Email("<EMAIL>"),
        username=Username("user"),
        full_name="Regular User",
        hashed_password="hashed_password",
        role=UserRole.USER,
        status=UserStatus.ACTIVE,
        is_active=True,
        is_verified=True,
    )


@pytest.fixture
def viewer_user():
    """Create viewer user for testing."""
    return User(
        id="viewer-123",
        email=Email("<EMAIL>"),
        username=Username("viewer"),
        full_name="Viewer User",
        hashed_password="hashed_password",
        role=UserRole.VIEWER,
        status=UserStatus.ACTIVE,
        is_active=True,
        is_verified=True,
    )


class TestUserPermissions:
    """Test suite for user permission checking."""

    def test_admin_has_all_permissions(self, admin_user):
        """Test that admin user has all permissions."""
        # Test workflow permissions
        assert admin_user.has_permission(Permission.WORKFLOW_CREATE)
        assert admin_user.has_permission(Permission.WORKFLOW_READ)
        assert admin_user.has_permission(Permission.WORKFLOW_UPDATE)
        assert admin_user.has_permission(Permission.WORKFLOW_DELETE)
        assert admin_user.has_permission(Permission.WORKFLOW_EXECUTE)
        assert admin_user.has_permission(Permission.WORKFLOW_SHARE)

        # Test agent permissions
        assert admin_user.has_permission(Permission.AGENT_CREATE)
        assert admin_user.has_permission(Permission.AGENT_READ)
        assert admin_user.has_permission(Permission.AGENT_UPDATE)
        assert admin_user.has_permission(Permission.AGENT_DELETE)
        assert admin_user.has_permission(Permission.AGENT_EXECUTE)

        # Test model permissions
        assert admin_user.has_permission(Permission.MODEL_READ)
        assert admin_user.has_permission(Permission.MODEL_MANAGE)
        assert admin_user.has_permission(Permission.MODEL_INFERENCE)

        # Test user management permissions
        assert admin_user.has_permission(Permission.USER_READ)
        assert admin_user.has_permission(Permission.USER_MANAGE)
        assert admin_user.has_permission(Permission.USER_DELETE)

        # Test system permissions
        assert admin_user.has_permission(Permission.SYSTEM_ADMIN)
        assert admin_user.has_permission(Permission.SYSTEM_MONITOR)

    def test_moderator_permissions(self, moderator_user):
        """Test that moderator user has appropriate permissions."""
        # Should have workflow permissions
        assert moderator_user.has_permission(Permission.WORKFLOW_CREATE)
        assert moderator_user.has_permission(Permission.WORKFLOW_READ)
        assert moderator_user.has_permission(Permission.WORKFLOW_UPDATE)
        assert moderator_user.has_permission(Permission.WORKFLOW_DELETE)
        assert moderator_user.has_permission(Permission.WORKFLOW_EXECUTE)
        assert moderator_user.has_permission(Permission.WORKFLOW_SHARE)

        # Should have agent permissions
        assert moderator_user.has_permission(Permission.AGENT_CREATE)
        assert moderator_user.has_permission(Permission.AGENT_READ)
        assert moderator_user.has_permission(Permission.AGENT_UPDATE)
        assert moderator_user.has_permission(Permission.AGENT_DELETE)
        assert moderator_user.has_permission(Permission.AGENT_EXECUTE)

        # Should have basic model permissions
        assert moderator_user.has_permission(Permission.MODEL_READ)
        assert moderator_user.has_permission(Permission.MODEL_INFERENCE)
        assert not moderator_user.has_permission(Permission.MODEL_MANAGE)

        # Should have user read permission
        assert moderator_user.has_permission(Permission.USER_READ)
        assert not moderator_user.has_permission(Permission.USER_MANAGE)
        assert not moderator_user.has_permission(Permission.USER_DELETE)

        # Should have monitoring but not admin permissions
        assert moderator_user.has_permission(Permission.SYSTEM_MONITOR)
        assert not moderator_user.has_permission(Permission.SYSTEM_ADMIN)

    def test_regular_user_permissions(self, regular_user):
        """Test that regular user has basic permissions."""
        # Should have basic workflow permissions
        assert regular_user.has_permission(Permission.WORKFLOW_CREATE)
        assert regular_user.has_permission(Permission.WORKFLOW_READ)
        assert regular_user.has_permission(Permission.WORKFLOW_UPDATE)
        assert regular_user.has_permission(Permission.WORKFLOW_EXECUTE)
        assert regular_user.has_permission(Permission.WORKFLOW_SHARE)
        assert not regular_user.has_permission(Permission.WORKFLOW_DELETE)

        # Should have basic agent permissions
        assert regular_user.has_permission(Permission.AGENT_READ)
        assert regular_user.has_permission(Permission.AGENT_EXECUTE)
        assert not regular_user.has_permission(Permission.AGENT_CREATE)
        assert not regular_user.has_permission(Permission.AGENT_UPDATE)
        assert not regular_user.has_permission(Permission.AGENT_DELETE)

        # Should have basic model permissions
        assert regular_user.has_permission(Permission.MODEL_READ)
        assert regular_user.has_permission(Permission.MODEL_INFERENCE)
        assert not regular_user.has_permission(Permission.MODEL_MANAGE)

        # Should not have user management permissions
        assert not regular_user.has_permission(Permission.USER_READ)
        assert not regular_user.has_permission(Permission.USER_MANAGE)
        assert not regular_user.has_permission(Permission.USER_DELETE)

        # Should not have system permissions
        assert not regular_user.has_permission(Permission.SYSTEM_ADMIN)
        assert not regular_user.has_permission(Permission.SYSTEM_MONITOR)

    def test_viewer_permissions(self, viewer_user):
        """Test that viewer user has only read permissions."""
        # Should have only read workflow permissions
        assert viewer_user.has_permission(Permission.WORKFLOW_READ)
        assert not viewer_user.has_permission(Permission.WORKFLOW_CREATE)
        assert not viewer_user.has_permission(Permission.WORKFLOW_UPDATE)
        assert not viewer_user.has_permission(Permission.WORKFLOW_DELETE)
        assert not viewer_user.has_permission(Permission.WORKFLOW_EXECUTE)
        assert not viewer_user.has_permission(Permission.WORKFLOW_SHARE)

        # Should have only read agent permissions
        assert viewer_user.has_permission(Permission.AGENT_READ)
        assert not viewer_user.has_permission(Permission.AGENT_CREATE)
        assert not viewer_user.has_permission(Permission.AGENT_UPDATE)
        assert not viewer_user.has_permission(Permission.AGENT_DELETE)
        assert not viewer_user.has_permission(Permission.AGENT_EXECUTE)

        # Should have only read model permissions
        assert viewer_user.has_permission(Permission.MODEL_READ)
        assert not viewer_user.has_permission(Permission.MODEL_MANAGE)
        assert not viewer_user.has_permission(Permission.MODEL_INFERENCE)

        # Should not have any user management permissions
        assert not viewer_user.has_permission(Permission.USER_READ)
        assert not viewer_user.has_permission(Permission.USER_MANAGE)
        assert not viewer_user.has_permission(Permission.USER_DELETE)

        # Should not have any system permissions
        assert not viewer_user.has_permission(Permission.SYSTEM_ADMIN)
        assert not viewer_user.has_permission(Permission.SYSTEM_MONITOR)

    def test_has_any_permission(self, regular_user, viewer_user):
        """Test has_any_permission method."""
        # Regular user should have at least one of these permissions
        permissions = [Permission.WORKFLOW_CREATE, Permission.USER_MANAGE]
        assert regular_user.has_any_permission(permissions)

        # Viewer should not have any of these permissions
        permissions = [Permission.WORKFLOW_CREATE, Permission.USER_MANAGE]
        assert not viewer_user.has_any_permission(permissions)

        # Viewer should have at least one of these permissions
        permissions = [Permission.WORKFLOW_READ, Permission.USER_MANAGE]
        assert viewer_user.has_any_permission(permissions)

    def test_has_all_permissions(self, admin_user, regular_user):
        """Test has_all_permissions method."""
        # Admin should have all these permissions
        permissions = [Permission.WORKFLOW_CREATE, Permission.USER_MANAGE, Permission.SYSTEM_ADMIN]
        assert admin_user.has_all_permissions(permissions)

        # Regular user should not have all these permissions
        permissions = [Permission.WORKFLOW_CREATE, Permission.USER_MANAGE]
        assert not regular_user.has_all_permissions(permissions)

        # Regular user should have all these permissions
        permissions = [Permission.WORKFLOW_CREATE, Permission.WORKFLOW_READ]
        assert regular_user.has_all_permissions(permissions)


class TestUserRoles:
    """Test suite for user role checking."""

    def test_has_role(self, admin_user, regular_user):
        """Test has_role method."""
        assert admin_user.has_role(UserRole.ADMIN)
        assert not admin_user.has_role(UserRole.USER)

        assert regular_user.has_role(UserRole.USER)
        assert not regular_user.has_role(UserRole.ADMIN)

    def test_has_any_role(self, moderator_user):
        """Test has_any_role method."""
        roles = [UserRole.ADMIN, UserRole.MODERATOR]
        assert moderator_user.has_any_role(roles)

        roles = [UserRole.ADMIN, UserRole.USER]
        assert not moderator_user.has_any_role(roles)

        roles = [UserRole.MODERATOR, UserRole.VIEWER]
        assert moderator_user.has_any_role(roles)

    def test_role_properties(self, admin_user, regular_user, viewer_user):
        """Test role property access."""
        assert admin_user.role == UserRole.ADMIN
        assert regular_user.role == UserRole.USER
        assert viewer_user.role == UserRole.VIEWER

    def test_get_permissions(self, admin_user, regular_user):
        """Test get_permissions method returns correct set."""
        admin_permissions = admin_user.get_permissions()
        assert Permission.SYSTEM_ADMIN in admin_permissions
        assert Permission.USER_MANAGE in admin_permissions
        assert len(admin_permissions) > 15  # Admin should have many permissions

        user_permissions = regular_user.get_permissions()
        assert Permission.WORKFLOW_CREATE in user_permissions
        assert Permission.SYSTEM_ADMIN not in user_permissions
        assert len(user_permissions) < len(admin_permissions)


class TestUserEntityMethods:
    """Test suite for additional user entity methods."""

    def test_update_last_login(self, regular_user):
        """Test update_last_login method."""
        original_login = regular_user.last_login
        original_updated = regular_user.updated_at

        regular_user.update_last_login()

        assert regular_user.last_login != original_login
        assert regular_user.updated_at != original_updated
        assert regular_user.last_login is not None

    def test_to_dict_includes_new_fields(self, admin_user):
        """Test that to_dict includes role and status fields."""
        user_dict = admin_user.to_dict()

        assert "role" in user_dict
        assert "status" in user_dict
        assert "last_login" in user_dict
        assert user_dict["role"] == UserRole.ADMIN.value
        assert user_dict["status"] == UserStatus.ACTIVE.value

    def test_user_creation_with_defaults(self):
        """Test user creation with default role and status."""
        user = User(
            id="test-123",
            email=Email("<EMAIL>"),
            username=Username("testuser"),
            full_name="Test User",
            hashed_password="hashed_password",
        )

        assert user.role == UserRole.USER
        assert user.status == UserStatus.PENDING_VERIFICATION
        assert user.is_active is True
        assert user.is_verified is False
