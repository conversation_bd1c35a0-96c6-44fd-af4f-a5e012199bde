"""
Model Management SQLAlchemy models.

This module contains the SQLAlchemy models for the model management tables.
"""

import uuid
from datetime import datetime
from typing import TYPE_CHECKING

from sqlalchemy import (
    Boolean,
    DateTime,
    Enum,
    Float,
    Foreign<PERSON>ey,
    Index,
    Integer,
    JSON,
    String,
    Text,
    UUID,
)
from sqlalchemy.orm import Mapped, mapped_column, relationship

from src.domain.entities.model_management import ModelProvider, ModelStatus, ModelType
from src.infrastructure.database.models.base import Base

if TYPE_CHECKING:
    from src.infrastructure.database.models.user import UserModel


class ModelInstanceModel(Base):
    """
    Model Instance SQLAlchemy model.

    Represents the model_instances table in the database with all
    necessary constraints and indexes for efficient model management.
    """

    __tablename__ = "model_instances"

    # Model reference
    model_id: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        index=True,
        doc="Reference to ModelInfo ID"
    )

    # Instance status
    status: Mapped[ModelStatus] = mapped_column(
        Enum(ModelStatus),
        nullable=False,
        index=True,
        doc="Current instance status"
    )

    # Download information
    download_url: Mapped[str | None] = mapped_column(
        Text,
        nullable=True,
        doc="Download URL"
    )

    local_path: Mapped[str | None] = mapped_column(
        String(500),
        nullable=True,
        doc="Local file path"
    )

    download_progress: Mapped[float] = mapped_column(
        Float,
        default=0.0,
        nullable=False,
        doc="Download progress percentage"
    )

    download_speed: Mapped[float | None] = mapped_column(
        Float,
        nullable=True,
        doc="Download speed in bytes/second"
    )

    # Serving information
    serving_port: Mapped[int | None] = mapped_column(
        Integer,
        nullable=True,
        doc="Serving port"
    )

    serving_url: Mapped[str | None] = mapped_column(
        String(255),
        nullable=True,
        doc="Serving URL"
    )

    api_endpoint: Mapped[str | None] = mapped_column(
        String(255),
        nullable=True,
        doc="API endpoint"
    )

    # Performance metrics
    load_time_seconds: Mapped[float | None] = mapped_column(
        Float,
        nullable=True,
        doc="Model load time in seconds"
    )

    memory_usage_mb: Mapped[float | None] = mapped_column(
        Float,
        nullable=True,
        doc="Memory usage in MB"
    )

    gpu_memory_usage_mb: Mapped[float | None] = mapped_column(
        Float,
        nullable=True,
        doc="GPU memory usage in MB"
    )

    # Configuration
    configuration: Mapped[dict] = mapped_column(
        JSON,
        default=dict,
        nullable=False,
        doc="Model configuration as JSON"
    )

    # Error information
    error_message: Mapped[str | None] = mapped_column(
        Text,
        nullable=True,
        doc="Error message if status is ERROR"
    )

    error_details: Mapped[dict] = mapped_column(
        JSON,
        default=dict,
        nullable=False,
        doc="Detailed error information as JSON"
    )

    # Ownership
    created_by: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Creator user ID"
    )

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=datetime.utcnow,
        nullable=False,
        index=True,
        doc="Creation timestamp"
    )

    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=datetime.utcnow,
        onupdate=datetime.utcnow,
        nullable=False,
        index=True,
        doc="Last update timestamp"
    )

    last_used_at: Mapped[datetime | None] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        doc="Last usage timestamp"
    )

    # Relationships
    creator: Mapped["UserModel"] = relationship(
        "UserModel",
        foreign_keys=[created_by],
        back_populates="model_instances"
    )

    download_tasks: Mapped[list["ModelDownloadTaskModel"]] = relationship(
        "ModelDownloadTaskModel",
        back_populates="instance",
        cascade="all, delete-orphan"
    )

    # Indexes for efficient querying
    __table_args__ = (
        Index('ix_model_instances_model_status', 'model_id', 'status'),
        Index('ix_model_instances_creator_status', 'created_by', 'status'),
        Index('ix_model_instances_serving', 'serving_port', 'status'),
        Index('ix_model_instances_last_used', 'last_used_at'),
    )

    def to_domain(self) -> "ModelInstance":
        """
        Convert SQLAlchemy model to domain entity.

        Returns:
            ModelInstance domain entity
        """
        from src.domain.entities.model_management import ModelInstance

        return ModelInstance(
            id=self.id,
            model_id=self.model_id,
            status=self.status,
            download_url=self.download_url,
            local_path=self.local_path,
            download_progress=self.download_progress,
            download_speed=self.download_speed,
            serving_port=self.serving_port,
            serving_url=self.serving_url,
            api_endpoint=self.api_endpoint,
            load_time_seconds=self.load_time_seconds,
            memory_usage_mb=self.memory_usage_mb,
            gpu_memory_usage_mb=self.gpu_memory_usage_mb,
            configuration=self.configuration or {},
            error_message=self.error_message,
            error_details=self.error_details or {},
            created_by=self.created_by,
            created_at=self.created_at,
            updated_at=self.updated_at,
            last_used_at=self.last_used_at,
        )

    @classmethod
    def from_domain(cls, instance: "ModelInstance") -> "ModelInstanceModel":
        """
        Create SQLAlchemy model from domain entity.

        Args:
            instance: ModelInstance domain entity

        Returns:
            ModelInstanceModel instance
        """
        return cls(
            id=instance.id,
            model_id=instance.model_id,
            status=instance.status,
            download_url=instance.download_url,
            local_path=instance.local_path,
            download_progress=instance.download_progress,
            download_speed=instance.download_speed,
            serving_port=instance.serving_port,
            serving_url=instance.serving_url,
            api_endpoint=instance.api_endpoint,
            load_time_seconds=instance.load_time_seconds,
            memory_usage_mb=instance.memory_usage_mb,
            gpu_memory_usage_mb=instance.gpu_memory_usage_mb,
            configuration=instance.configuration,
            error_message=instance.error_message,
            error_details=instance.error_details,
            created_by=instance.created_by,
            created_at=instance.created_at,
            updated_at=instance.updated_at,
            last_used_at=instance.last_used_at,
        )


class ModelDownloadTaskModel(Base):
    """
    Model Download Task SQLAlchemy model.

    Represents the model_download_tasks table in the database for
    tracking background download operations.
    """

    __tablename__ = "model_download_tasks"

    # Task reference
    instance_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("model_instances.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Model instance ID"
    )

    model_id: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        index=True,
        doc="Model ID"
    )

    # Task status
    status: Mapped[str] = mapped_column(
        String(50),
        default="pending",
        nullable=False,
        index=True,
        doc="Task status"
    )

    progress: Mapped[float] = mapped_column(
        Float,
        default=0.0,
        nullable=False,
        doc="Progress percentage"
    )

    # Download information
    download_url: Mapped[str] = mapped_column(
        Text,
        nullable=False,
        doc="Download URL"
    )

    destination_path: Mapped[str] = mapped_column(
        String(500),
        nullable=False,
        doc="Destination file path"
    )

    total_size: Mapped[int | None] = mapped_column(
        Integer,
        nullable=True,
        doc="Total download size in bytes"
    )

    downloaded_size: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        doc="Downloaded size in bytes"
    )

    download_speed: Mapped[float | None] = mapped_column(
        Float,
        nullable=True,
        doc="Download speed in bytes/second"
    )

    # Timing
    started_at: Mapped[datetime | None] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="Task start timestamp"
    )

    completed_at: Mapped[datetime | None] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="Task completion timestamp"
    )

    estimated_completion: Mapped[datetime | None] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="Estimated completion timestamp"
    )

    # Error handling
    error_message: Mapped[str | None] = mapped_column(
        Text,
        nullable=True,
        doc="Error message if failed"
    )

    retry_count: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of retry attempts"
    )

    max_retries: Mapped[int] = mapped_column(
        Integer,
        default=3,
        nullable=False,
        doc="Maximum retry attempts"
    )

    # Ownership
    created_by: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Creator user ID"
    )

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=datetime.utcnow,
        nullable=False,
        index=True,
        doc="Creation timestamp"
    )

    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=datetime.utcnow,
        onupdate=datetime.utcnow,
        nullable=False,
        index=True,
        doc="Last update timestamp"
    )

    # Relationships
    instance: Mapped["ModelInstanceModel"] = relationship(
        "ModelInstanceModel",
        foreign_keys=[instance_id],
        back_populates="download_tasks"
    )

    creator: Mapped["UserModel"] = relationship(
        "UserModel",
        foreign_keys=[created_by]
    )

    # Indexes for efficient querying
    __table_args__ = (
        Index('ix_model_download_tasks_status_created', 'status', 'created_at'),
        Index('ix_model_download_tasks_creator_status', 'created_by', 'status'),
        Index('ix_model_download_tasks_model_status', 'model_id', 'status'),
    )

    def to_domain(self) -> "ModelDownloadTask":
        """
        Convert SQLAlchemy model to domain entity.

        Returns:
            ModelDownloadTask domain entity
        """
        from src.domain.entities.model_management import ModelDownloadTask

        return ModelDownloadTask(
            id=self.id,
            instance_id=self.instance_id,
            model_id=self.model_id,
            status=self.status,
            progress=self.progress,
            download_url=self.download_url,
            destination_path=self.destination_path,
            total_size=self.total_size,
            downloaded_size=self.downloaded_size,
            download_speed=self.download_speed,
            started_at=self.started_at,
            completed_at=self.completed_at,
            estimated_completion=self.estimated_completion,
            error_message=self.error_message,
            retry_count=self.retry_count,
            max_retries=self.max_retries,
            created_by=self.created_by,
            created_at=self.created_at,
            updated_at=self.updated_at,
        )

    @classmethod
    def from_domain(cls, task: "ModelDownloadTask") -> "ModelDownloadTaskModel":
        """
        Create SQLAlchemy model from domain entity.

        Args:
            task: ModelDownloadTask domain entity

        Returns:
            ModelDownloadTaskModel instance
        """
        return cls(
            id=task.id,
            instance_id=task.instance_id,
            model_id=task.model_id,
            status=task.status,
            progress=task.progress,
            download_url=task.download_url,
            destination_path=task.destination_path,
            total_size=task.total_size,
            downloaded_size=task.downloaded_size,
            download_speed=task.download_speed,
            started_at=task.started_at,
            completed_at=task.completed_at,
            estimated_completion=task.estimated_completion,
            error_message=task.error_message,
            retry_count=task.retry_count,
            max_retries=task.max_retries,
            created_by=task.created_by,
            created_at=task.created_at,
            updated_at=task.updated_at,
        )


# Add relationship to UserModel
from src.infrastructure.database.models.user import UserModel

UserModel.model_instances = relationship(
    "ModelInstanceModel",
    foreign_keys=[ModelInstanceModel.created_by],
    back_populates="creator",
    cascade="all, delete-orphan"
)
