"""
Model Management service.

This module contains use cases for model management operations including
model discovery, download, serving, and monitoring.
"""

import asyncio
import uuid
from datetime import UTC, datetime
from typing import Any, Dict, List, Optional

import aiohttp
from huggingface_hub import HfA<PERSON>, ModelInfo as HfModelInfo

from src.domain.entities.model_management import (
    ModelDownloadTask,
    ModelInfo,
    ModelInstance,
    ModelProvider,
    ModelStatus,
    ModelType,
)
from src.domain.entities.user import Permission, User
from src.infrastructure.logging.setup import get_logger

logger = get_logger(__name__)


class ModelManagementService:
    """Service for model management operations."""

    def __init__(self) -> None:
        """Initialize the model management service."""
        self.hf_api = HfApi()
        self.ollama_base_url = "http://localhost:11434"
        self.download_tasks: Dict[str, ModelDownloadTask] = {}

    # Model discovery
    async def discover_huggingface_models(
        self,
        query: Optional[str] = None,
        model_type: Optional[ModelType] = None,
        limit: int = 20,
    ) -> List[ModelInfo]:
        """
        Discover models from HuggingFace Hub.

        Args:
            query: Search query
            model_type: Filter by model type
            limit: Maximum number of models to return

        Returns:
            List of discovered models
        """
        try:
            # Map our model types to HuggingFace pipeline tags
            pipeline_tag_map = {
                ModelType.LANGUAGE_MODEL: "text-generation",
                ModelType.EMBEDDING_MODEL: "feature-extraction",
                ModelType.VISION_MODEL: "image-classification",
                ModelType.AUDIO_MODEL: "automatic-speech-recognition",
                ModelType.CODE_MODEL: "text-generation",
            }

            search_kwargs = {
                "limit": limit,
                "sort": "downloads",
                "direction": -1,
            }

            if query:
                search_kwargs["search"] = query

            if model_type and model_type in pipeline_tag_map:
                search_kwargs["pipeline_tag"] = pipeline_tag_map[model_type]

            # Search HuggingFace models
            hf_models = self.hf_api.list_models(**search_kwargs)

            models = []
            for hf_model in hf_models:
                try:
                    model_info = await self._convert_hf_model_to_model_info(hf_model)
                    models.append(model_info)
                except Exception as e:
                    logger.warning(f"Failed to convert HF model {hf_model.id}: {e}")
                    continue

            logger.info(f"Discovered {len(models)} models from HuggingFace")
            return models

        except Exception as e:
            logger.error(f"Failed to discover HuggingFace models: {e}")
            raise

    async def discover_ollama_models(self) -> List[ModelInfo]:
        """
        Discover available models from Ollama.

        Returns:
            List of available Ollama models
        """
        try:
            async with aiohttp.ClientSession() as session:
                # Get list of available models
                async with session.get(f"{self.ollama_base_url}/api/tags") as response:
                    if response.status == 200:
                        data = await response.json()
                        models = []

                        for model_data in data.get("models", []):
                            model_info = await self._convert_ollama_model_to_model_info(model_data)
                            models.append(model_info)

                        logger.info(f"Discovered {len(models)} models from Ollama")
                        return models
                    else:
                        logger.warning(f"Ollama API returned status {response.status}")
                        return []

        except aiohttp.ClientError as e:
            logger.warning(f"Failed to connect to Ollama: {e}")
            return []
        except Exception as e:
            logger.error(f"Failed to discover Ollama models: {e}")
            raise

    async def search_models(
        self,
        query: str,
        model_type: Optional[ModelType] = None,
        provider: Optional[ModelProvider] = None,
        limit: int = 50,
    ) -> List[ModelInfo]:
        """
        Search for models across all providers.

        Args:
            query: Search query
            model_type: Filter by model type
            provider: Filter by provider
            limit: Maximum number of models to return

        Returns:
            List of matching models
        """
        models = []

        # Search HuggingFace if not filtered to other providers
        if not provider or provider == ModelProvider.HUGGINGFACE:
            try:
                hf_models = await self.discover_huggingface_models(query, model_type, limit // 2)
                models.extend(hf_models)
            except Exception as e:
                logger.warning(f"Failed to search HuggingFace models: {e}")

        # Search Ollama if not filtered to other providers
        if not provider or provider == ModelProvider.OLLAMA:
            try:
                ollama_models = await self.discover_ollama_models()
                # Filter Ollama models by query and type
                filtered_ollama = [
                    model for model in ollama_models
                    if (not query or query.lower() in model.name.lower() or query.lower() in model.display_name.lower())
                    and (not model_type or model.model_type == model_type)
                ]
                models.extend(filtered_ollama[:limit // 2])
            except Exception as e:
                logger.warning(f"Failed to search Ollama models: {e}")

        # Sort by relevance (simple name matching for now)
        if query:
            models.sort(key=lambda m: (
                query.lower() in m.name.lower(),
                query.lower() in m.display_name.lower(),
                query.lower() in (m.description or "").lower()
            ), reverse=True)

        return models[:limit]

    # Model instance management
    async def create_model_instance(
        self,
        model_id: str,
        user: User,
        configuration: Optional[Dict[str, Any]] = None,
    ) -> ModelInstance:
        """
        Create a new model instance.

        Args:
            model_id: Model identifier
            user: User creating the instance
            configuration: Optional model configuration

        Returns:
            Created model instance

        Raises:
            PermissionError: If user lacks permission
        """
        # Check permissions
        if not user.has_permission(Permission.MODEL_CREATE):
            raise PermissionError("User lacks permission to create model instances")

        # Create instance
        instance = ModelInstance(
            model_id=model_id,
            status=ModelStatus.AVAILABLE,
            configuration=configuration or {},
            created_by=uuid.UUID(user.id),
        )

        logger.info(f"Created model instance {instance.id} for model {model_id}")
        return instance

    async def download_model(
        self,
        instance: ModelInstance,
        user: User,
    ) -> ModelDownloadTask:
        """
        Start downloading a model.

        Args:
            instance: Model instance to download
            user: User requesting the download

        Returns:
            Download task

        Raises:
            PermissionError: If user lacks permission
            ValueError: If model cannot be downloaded
        """
        # Check permissions
        if not user.has_permission(Permission.MODEL_DOWNLOAD):
            raise PermissionError("User lacks permission to download models")

        # Check if already downloading
        if instance.is_downloading():
            raise ValueError("Model is already downloading")

        # Get model info to determine download URL
        model_info = await self._get_model_info(instance.model_id)
        if not model_info:
            raise ValueError(f"Model {instance.model_id} not found")

        # Determine download URL based on provider
        download_url = await self._get_download_url(model_info)
        if not download_url:
            raise ValueError(f"No download URL available for model {instance.model_id}")

        # Create download task
        task = ModelDownloadTask(
            instance_id=instance.id,
            model_id=instance.model_id,
            download_url=download_url,
            destination_path=f"./models/{instance.model_id}",
            created_by=uuid.UUID(user.id),
        )

        # Update instance status
        instance.update_status(ModelStatus.DOWNLOADING)
        instance.download_url = download_url

        # Store task
        self.download_tasks[str(task.id)] = task

        # Start download in background
        asyncio.create_task(self._execute_download_task(task, instance))

        logger.info(f"Started download task {task.id} for model {instance.model_id}")
        return task

    async def get_download_progress(self, task_id: str) -> Optional[ModelDownloadTask]:
        """
        Get download progress for a task.

        Args:
            task_id: Download task ID

        Returns:
            Download task with current progress
        """
        return self.download_tasks.get(task_id)

    async def cancel_download(self, task_id: str, user: User) -> bool:
        """
        Cancel a download task.

        Args:
            task_id: Download task ID
            user: User requesting cancellation

        Returns:
            True if cancelled successfully

        Raises:
            PermissionError: If user lacks permission
        """
        # Check permissions
        if not user.has_permission(Permission.MODEL_DOWNLOAD):
            raise PermissionError("User lacks permission to cancel downloads")

        task = self.download_tasks.get(task_id)
        if not task:
            return False

        # Check ownership
        if str(task.created_by) != user.id:
            raise PermissionError("User can only cancel their own downloads")

        # Cancel task
        task.fail_download("Cancelled by user")
        
        logger.info(f"Cancelled download task {task_id}")
        return True

    # Model serving
    async def start_model_serving(
        self,
        instance: ModelInstance,
        user: User,
        port: Optional[int] = None,
    ) -> bool:
        """
        Start serving a model.

        Args:
            instance: Model instance to serve
            user: User requesting serving
            port: Optional specific port to use

        Returns:
            True if started successfully

        Raises:
            PermissionError: If user lacks permission
            ValueError: If model cannot be served
        """
        # Check permissions
        if not user.has_permission(Permission.MODEL_SERVE):
            raise PermissionError("User lacks permission to serve models")

        # Check if model is downloaded
        if instance.status != ModelStatus.DOWNLOADED:
            raise ValueError("Model must be downloaded before serving")

        try:
            # Update status to loading
            instance.update_status(ModelStatus.LOADING)

            # Get model info
            model_info = await self._get_model_info(instance.model_id)
            if not model_info:
                raise ValueError(f"Model {instance.model_id} not found")

            # Start serving based on provider
            if model_info.provider == ModelProvider.OLLAMA:
                success = await self._start_ollama_serving(instance, model_info, port)
            else:
                # For other providers, use generic serving
                success = await self._start_generic_serving(instance, model_info, port)

            if success:
                instance.update_status(ModelStatus.SERVING)
                logger.info(f"Started serving model {instance.model_id}")
            else:
                instance.update_status(ModelStatus.ERROR, "Failed to start serving")

            return success

        except Exception as e:
            instance.update_status(ModelStatus.ERROR, str(e))
            logger.error(f"Failed to start serving model {instance.model_id}: {e}")
            raise

    async def stop_model_serving(self, instance: ModelInstance, user: User) -> bool:
        """
        Stop serving a model.

        Args:
            instance: Model instance to stop
            user: User requesting stop

        Returns:
            True if stopped successfully

        Raises:
            PermissionError: If user lacks permission
        """
        # Check permissions
        if not user.has_permission(Permission.MODEL_SERVE):
            raise PermissionError("User lacks permission to stop model serving")

        try:
            # Get model info
            model_info = await self._get_model_info(instance.model_id)
            if not model_info:
                raise ValueError(f"Model {instance.model_id} not found")

            # Stop serving based on provider
            if model_info.provider == ModelProvider.OLLAMA:
                success = await self._stop_ollama_serving(instance)
            else:
                success = await self._stop_generic_serving(instance)

            if success:
                instance.update_status(ModelStatus.STOPPED)
                instance.serving_port = None
                instance.serving_url = None
                instance.api_endpoint = None
                logger.info(f"Stopped serving model {instance.model_id}")
            else:
                logger.warning(f"Failed to stop serving model {instance.model_id}")

            return success

        except Exception as e:
            logger.error(f"Failed to stop serving model {instance.model_id}: {e}")
            raise

    # Health monitoring
    async def check_model_health(self, instance: ModelInstance) -> Dict[str, Any]:
        """
        Check health status of a serving model.

        Args:
            instance: Model instance to check

        Returns:
            Health status information
        """
        if not instance.is_available():
            return {
                "status": "unavailable",
                "message": f"Model is {instance.status.value}",
            }

        try:
            if instance.api_endpoint:
                async with aiohttp.ClientSession() as session:
                    async with session.get(f"{instance.api_endpoint}/health", timeout=5) as response:
                        if response.status == 200:
                            return {
                                "status": "healthy",
                                "response_time_ms": response.headers.get("X-Response-Time", "unknown"),
                                "memory_usage_mb": instance.memory_usage_mb,
                                "gpu_memory_usage_mb": instance.gpu_memory_usage_mb,
                            }
                        else:
                            return {
                                "status": "unhealthy",
                                "message": f"Health check returned status {response.status}",
                            }
            else:
                return {
                    "status": "unknown",
                    "message": "No API endpoint available",
                }

        except Exception as e:
            return {
                "status": "error",
                "message": str(e),
            }

    # Private helper methods
    async def _convert_hf_model_to_model_info(self, hf_model: HfModelInfo) -> ModelInfo:
        """Convert HuggingFace model to ModelInfo."""
        # Determine model type from tags
        model_type = ModelType.LANGUAGE_MODEL  # Default
        if hf_model.pipeline_tag:
            if "embedding" in hf_model.pipeline_tag or "feature-extraction" in hf_model.pipeline_tag:
                model_type = ModelType.EMBEDDING_MODEL
            elif "image" in hf_model.pipeline_tag:
                model_type = ModelType.VISION_MODEL
            elif "audio" in hf_model.pipeline_tag or "speech" in hf_model.pipeline_tag:
                model_type = ModelType.AUDIO_MODEL

        return ModelInfo(
            id=hf_model.id,
            name=hf_model.id,
            display_name=hf_model.id.split("/")[-1] if "/" in hf_model.id else hf_model.id,
            description=getattr(hf_model, "description", None),
            model_type=model_type,
            provider=ModelProvider.HUGGINGFACE,
            version="latest",
            tags=hf_model.tags or [],
            license=getattr(hf_model, "license", None),
            homepage_url=f"https://huggingface.co/{hf_model.id}",
            repository_url=f"https://huggingface.co/{hf_model.id}",
            provider_metadata={
                "downloads": getattr(hf_model, "downloads", 0),
                "likes": getattr(hf_model, "likes", 0),
                "pipeline_tag": hf_model.pipeline_tag,
                "library_name": getattr(hf_model, "library_name", None),
            },
        )

    async def _convert_ollama_model_to_model_info(self, ollama_data: Dict[str, Any]) -> ModelInfo:
        """Convert Ollama model data to ModelInfo."""
        name = ollama_data.get("name", "")
        
        return ModelInfo(
            id=name,
            name=name,
            display_name=name.split(":")[0] if ":" in name else name,
            description=f"Ollama model: {name}",
            model_type=ModelType.LANGUAGE_MODEL,
            provider=ModelProvider.OLLAMA,
            version=name.split(":")[-1] if ":" in name else "latest",
            size_bytes=ollama_data.get("size", 0),
            tags=["ollama", "local"],
            provider_metadata={
                "digest": ollama_data.get("digest"),
                "modified_at": ollama_data.get("modified_at"),
                "details": ollama_data.get("details", {}),
            },
        )

    async def _get_model_info(self, model_id: str) -> Optional[ModelInfo]:
        """Get model info by ID."""
        # This would typically query a database or cache
        # For now, we'll try to fetch from providers
        try:
            # Try HuggingFace first
            hf_model = self.hf_api.model_info(model_id)
            return await self._convert_hf_model_to_model_info(hf_model)
        except:
            # Try Ollama
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(f"{self.ollama_base_url}/api/show", 
                                         json={"name": model_id}) as response:
                        if response.status == 200:
                            data = await response.json()
                            return await self._convert_ollama_model_to_model_info({"name": model_id, **data})
            except:
                pass
        
        return None

    async def _get_download_url(self, model_info: ModelInfo) -> Optional[str]:
        """Get download URL for a model."""
        if model_info.provider == ModelProvider.HUGGINGFACE:
            return f"https://huggingface.co/{model_info.id}/resolve/main/pytorch_model.bin"
        elif model_info.provider == ModelProvider.OLLAMA:
            return f"{self.ollama_base_url}/api/pull"
        
        return None

    async def _execute_download_task(self, task: ModelDownloadTask, instance: ModelInstance) -> None:
        """Execute a download task in the background."""
        try:
            task.start_download()
            
            # Simulate download progress (replace with actual download logic)
            for progress in range(0, 101, 10):
                await asyncio.sleep(1)  # Simulate download time
                task.update_progress(progress * (task.total_size or 1000000) // 100, 1000000)
                instance.update_download_progress(progress)
            
            task.complete_download()
            instance.update_status(ModelStatus.DOWNLOADED)
            instance.local_path = task.destination_path
            
        except Exception as e:
            task.fail_download(str(e))
            instance.update_status(ModelStatus.ERROR, str(e))

    async def _start_ollama_serving(self, instance: ModelInstance, model_info: ModelInfo, port: Optional[int]) -> bool:
        """Start serving an Ollama model."""
        # Ollama handles serving automatically
        instance.update_serving_info(
            port=11434,
            url=f"{self.ollama_base_url}",
            endpoint=f"{self.ollama_base_url}/api/generate"
        )
        return True

    async def _start_generic_serving(self, instance: ModelInstance, model_info: ModelInfo, port: Optional[int]) -> bool:
        """Start serving a generic model."""
        # Implement generic model serving logic
        serving_port = port or 8000
        instance.update_serving_info(
            port=serving_port,
            url=f"http://localhost:{serving_port}",
            endpoint=f"http://localhost:{serving_port}/v1/completions"
        )
        return True

    async def _stop_ollama_serving(self, instance: ModelInstance) -> bool:
        """Stop serving an Ollama model."""
        # Ollama models are managed by the Ollama service
        return True

    async def _stop_generic_serving(self, instance: ModelInstance) -> bool:
        """Stop serving a generic model."""
        # Implement generic model stopping logic
        return True
