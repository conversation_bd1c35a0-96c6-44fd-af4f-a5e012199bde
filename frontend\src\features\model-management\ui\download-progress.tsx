'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Download, Pause, Play, X, AlertCircle, CheckCircle, Clock, HardDrive } from 'lucide-react';
import { useDownloadProgress, formatBytes, formatSpeed, estimateTimeRemaining } from '@/entities/model-management/api';
import { ModelDownloadTask } from '@/shared/types';
import { Button } from '@/shared/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/ui/card';
import { Badge } from '@/shared/ui/badge';
import { Progress } from '@/shared/ui/progress';
import { Alert, AlertDescription } from '@/shared/ui/alert';
import { useToast } from '@/shared/hooks/use-toast';

interface DownloadProgressProps {
  tasks: ModelDownloadTask[];
  onCancelDownload?: (taskId: string) => void;
  onRetryDownload?: (taskId: string) => void;
  className?: string;
}

export function DownloadProgress({ tasks, onCancelDownload, onRetryDownload, className }: DownloadProgressProps) {
  const { toast } = useToast();

  const handleCancelDownload = async (taskId: string) => {
    try {
      await onCancelDownload?.(taskId);
      toast({
        title: 'Download Cancelled',
        description: 'The download has been cancelled successfully.',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to cancel download.',
        variant: 'destructive',
      });
    }
  };

  const handleRetryDownload = async (taskId: string) => {
    try {
      await onRetryDownload?.(taskId);
      toast({
        title: 'Download Retried',
        description: 'The download has been restarted.',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to retry download.',
        variant: 'destructive',
      });
    }
  };

  if (tasks.length === 0) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <Download className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <h3 className="text-lg font-semibold mb-2">No Downloads</h3>
        <p className="text-muted-foreground">No model downloads in progress</p>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Download Progress</h2>
        <Badge variant="outline">
          {tasks.filter(t => t.status === 'running').length} active
        </Badge>
      </div>

      <div className="space-y-4">
        <AnimatePresence>
          {tasks.map((task) => (
            <motion.div
              key={task.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.2 }}
            >
              <DownloadTaskCard
                task={task}
                onCancel={() => handleCancelDownload(task.id)}
                onRetry={() => handleRetryDownload(task.id)}
              />
            </motion.div>
          ))}
        </AnimatePresence>
      </div>
    </div>
  );
}

interface DownloadTaskCardProps {
  task: ModelDownloadTask;
  onCancel: () => void;
  onRetry: () => void;
}

function DownloadTaskCard({ task, onCancel, onRetry }: DownloadTaskCardProps) {
  const [realTimeTask, setRealTimeTask] = useState(task);

  // Use WebSocket for real-time updates
  useEffect(() => {
    const ws = new WebSocket(`ws://localhost:3001/ws/model-management/${task.created_by}`);
    
    ws.onopen = () => {
      // Subscribe to this task's updates
      ws.send(JSON.stringify({
        type: 'subscribe_download_task',
        data: { task_id: task.id }
      }));
    };

    ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        if (message.type === 'download_progress' && message.data.task_id === task.id) {
          setRealTimeTask(prev => ({
            ...prev,
            ...message.data
          }));
        }
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error);
      }
    };

    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };

    return () => {
      ws.close();
    };
  }, [task.id, task.created_by]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <Download className="h-4 w-4 text-blue-500 animate-pulse" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'cancelled':
        return <X className="h-4 w-4 text-gray-500" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      default:
        return <Download className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'blue';
      case 'completed':
        return 'green';
      case 'failed':
        return 'red';
      case 'cancelled':
        return 'gray';
      case 'pending':
        return 'yellow';
      default:
        return 'gray';
    }
  };

  const canCancel = realTimeTask.status === 'running' || realTimeTask.status === 'pending';
  const canRetry = realTimeTask.status === 'failed' && realTimeTask.retry_count < realTimeTask.max_retries;

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {getStatusIcon(realTimeTask.status)}
            <div>
              <CardTitle className="text-base">{realTimeTask.model_id}</CardTitle>
              <p className="text-sm text-muted-foreground">
                Task ID: {realTimeTask.id.slice(0, 8)}...
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className={`text-${getStatusColor(realTimeTask.status)}-600`}>
              {realTimeTask.status}
            </Badge>
            
            {canCancel && (
              <Button variant="outline" size="sm" onClick={onCancel}>
                <X className="h-4 w-4" />
              </Button>
            )}
            
            {canRetry && (
              <Button variant="outline" size="sm" onClick={onRetry}>
                <Play className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-4">
          {/* Progress Bar */}
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>Progress</span>
              <span>{realTimeTask.progress.toFixed(1)}%</span>
            </div>
            <Progress value={realTimeTask.progress} className="h-2" />
          </div>

          {/* Download Stats */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center space-x-2">
              <HardDrive className="h-4 w-4 text-muted-foreground" />
              <div>
                <div className="font-medium">Size</div>
                <div className="text-muted-foreground">
                  {formatBytes(realTimeTask.downloaded_size)} / {realTimeTask.total_size ? formatBytes(realTimeTask.total_size) : 'Unknown'}
                </div>
              </div>
            </div>
            
            {realTimeTask.download_speed && (
              <div className="flex items-center space-x-2">
                <Download className="h-4 w-4 text-muted-foreground" />
                <div>
                  <div className="font-medium">Speed</div>
                  <div className="text-muted-foreground">
                    {formatSpeed(realTimeTask.download_speed)}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Time Estimates */}
          {realTimeTask.status === 'running' && realTimeTask.download_speed && realTimeTask.total_size && (
            <div className="text-sm">
              <div className="font-medium">Estimated Time Remaining</div>
              <div className="text-muted-foreground">
                {estimateTimeRemaining(realTimeTask.total_size, realTimeTask.downloaded_size, realTimeTask.download_speed)}
              </div>
            </div>
          )}

          {/* Timestamps */}
          <div className="grid grid-cols-2 gap-4 text-xs text-muted-foreground">
            {realTimeTask.started_at && (
              <div>
                <div className="font-medium">Started</div>
                <div>{new Date(realTimeTask.started_at).toLocaleTimeString()}</div>
              </div>
            )}
            
            {realTimeTask.estimated_completion && realTimeTask.status === 'running' && (
              <div>
                <div className="font-medium">ETA</div>
                <div>{new Date(realTimeTask.estimated_completion).toLocaleTimeString()}</div>
              </div>
            )}
            
            {realTimeTask.completed_at && (
              <div>
                <div className="font-medium">Completed</div>
                <div>{new Date(realTimeTask.completed_at).toLocaleTimeString()}</div>
              </div>
            )}
          </div>

          {/* Error Message */}
          {realTimeTask.error_message && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {realTimeTask.error_message}
                {realTimeTask.retry_count > 0 && (
                  <div className="mt-1 text-xs">
                    Retry {realTimeTask.retry_count} of {realTimeTask.max_retries}
                  </div>
                )}
              </AlertDescription>
            </Alert>
          )}

          {/* Success Message */}
          {realTimeTask.status === 'completed' && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                Download completed successfully! Model saved to {realTimeTask.destination_path}
              </AlertDescription>
            </Alert>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// Hook for managing multiple download tasks with real-time updates
export function useDownloadTasks(userId: string) {
  const [tasks, setTasks] = useState<ModelDownloadTask[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Fetch initial tasks
    const fetchTasks = async () => {
      try {
        setLoading(true);
        // This would call the API to get user's download tasks
        // const response = await modelManagementService.getDownloadTasks(userId);
        // setTasks(response);
        setTasks([]); // Placeholder
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch download tasks');
      } finally {
        setLoading(false);
      }
    };

    fetchTasks();

    // Set up WebSocket for real-time updates
    const ws = new WebSocket(`ws://localhost:3001/ws/model-management/${userId}`);
    
    ws.onopen = () => {
      console.log('Connected to model management WebSocket');
    };

    ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        
        if (message.type === 'download_progress') {
          const taskId = message.data.task_id;
          setTasks(prev => prev.map(task => 
            task.id === taskId 
              ? { ...task, ...message.data }
              : task
          ));
        }
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error);
      }
    };

    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
      setError('WebSocket connection failed');
    };

    return () => {
      ws.close();
    };
  }, [userId]);

  const addTask = useCallback((task: ModelDownloadTask) => {
    setTasks(prev => [task, ...prev]);
  }, []);

  const removeTask = useCallback((taskId: string) => {
    setTasks(prev => prev.filter(task => task.id !== taskId));
  }, []);

  const updateTask = useCallback((taskId: string, updates: Partial<ModelDownloadTask>) => {
    setTasks(prev => prev.map(task => 
      task.id === taskId 
        ? { ...task, ...updates }
        : task
    ));
  }, []);

  return {
    tasks,
    loading,
    error,
    addTask,
    removeTask,
    updateTask,
  };
}
