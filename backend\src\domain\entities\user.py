"""
User domain entity for the <PERSON>nors application.

This module contains the User entity that represents a user in the system
following Domain-Driven Design principles with domain events.
"""

from datetime import UTC, datetime
from enum import Enum
from typing import Any

from ..events.base import DomainEvent
from ..events.user_events import UserActivatedEvent, UserEmailVerifiedEvent
from ..exceptions import DomainValidationError
from ..value_objects.email import Email
from ..value_objects.username import Username


class UserStatus(Enum):
    """User status enumeration."""

    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    PENDING = "pending"
    PENDING_VERIFICATION = "pending_verification"


class UserRole(Enum):
    """User role enumeration with hierarchical permissions."""

    ADMIN = "admin"
    MODERATOR = "moderator"
    USER = "user"
    VIEWER = "viewer"


class Permission(Enum):
    """Permission enumeration for fine-grained access control."""

    # Workflow permissions
    WORKFLOW_CREATE = "workflow:create"
    WORKFLOW_READ = "workflow:read"
    WORKFLOW_UPDATE = "workflow:update"
    WORKFLOW_DELETE = "workflow:delete"
    WORKFLOW_EXECUTE = "workflow:execute"
    WORKFLOW_SHARE = "workflow:share"

    # Agent permissions
    AGENT_CREATE = "agent:create"
    AGENT_READ = "agent:read"
    AGENT_UPDATE = "agent:update"
    AGENT_DELETE = "agent:delete"
    AGENT_EXECUTE = "agent:execute"

    # Model permissions
    MODEL_READ = "model:read"
    MODEL_MANAGE = "model:manage"
    MODEL_INFERENCE = "model:inference"

    # User management permissions
    USER_READ = "user:read"
    USER_MANAGE = "user:manage"
    USER_DELETE = "user:delete"

    # System permissions
    SYSTEM_ADMIN = "system:admin"
    SYSTEM_MONITOR = "system:monitor"


class User:
    """User domain entity with business logic and domain events."""

    def __init__(
        self,
        id: str,
        email: Email,
        username: Username,
        full_name: str,
        hashed_password: str,
        role: UserRole = UserRole.USER,
        status: UserStatus = UserStatus.PENDING_VERIFICATION,
        is_active: bool = True,
        is_verified: bool = False,
        last_login: datetime | None = None,
        created_at: datetime | None = None,
        updated_at: datetime | None = None,
    ) -> None:
        """
        Initialize user entity.

        Args:
            id: Unique user identifier
            email: User email (Email value object)
            username: User username (Username value object)
            full_name: User's full name
            hashed_password: Hashed password
            role: User role for permissions
            status: User account status
            is_active: Whether user is active
            is_verified: Whether email is verified
            last_login: Last login timestamp
            created_at: Creation timestamp
            updated_at: Last update timestamp

        Raises:
            DomainValidationError: If validation fails
        """
        self._validate_full_name(full_name)

        self._id = id
        self._email = email
        self._username = username
        self._full_name = full_name
        self._hashed_password = hashed_password
        self._role = role
        self._status = status
        self._is_active = is_active
        self._is_verified = is_verified
        self._last_login = last_login
        self._created_at = created_at or datetime.now(UTC)
        self._updated_at = updated_at or datetime.now(UTC)

        # Domain events
        self._events: list[DomainEvent] = []

    @property
    def id(self) -> str:
        """Get user ID."""
        return self._id

    @property
    def email(self) -> Email:
        """Get user email."""
        return self._email

    @property
    def username(self) -> Username:
        """Get user username."""
        return self._username

    @property
    def full_name(self) -> str:
        """Get user full name."""
        return self._full_name

    @property
    def hashed_password(self) -> str:
        """Get user hashed password."""
        return self._hashed_password

    @property
    def is_active(self) -> bool:
        """Get user active status."""
        return self._is_active

    @property
    def is_verified(self) -> bool:
        """Get user verification status."""
        return self._is_verified

    @property
    def role(self) -> UserRole:
        """Get user role."""
        return self._role

    @property
    def status(self) -> UserStatus:
        """Get user status."""
        return self._status

    @property
    def last_login(self) -> datetime | None:
        """Get last login timestamp."""
        return self._last_login

    @property
    def created_at(self) -> datetime:
        """Get creation timestamp."""
        return self._created_at

    @property
    def updated_at(self) -> datetime:
        """Get last update timestamp."""
        return self._updated_at

    def activate(self) -> None:
        """Activate the user."""
        if not self._is_active:
            self._is_active = True
            self._updated_at = datetime.now(UTC)
            self._add_event(UserActivatedEvent(user_id=self._id))

    def deactivate(self) -> None:
        """Deactivate the user."""
        if self._is_active:
            self._is_active = False
            self._updated_at = datetime.now(UTC)

    def verify_email(self) -> None:
        """Verify user's email."""
        if not self._is_verified:
            self._is_verified = True
            self._updated_at = datetime.now(UTC)
            self._add_event(
                UserEmailVerifiedEvent(user_id=self._id, email=self._email.value)
            )

    def update_profile(self, full_name: str | None = None) -> None:
        """
        Update user profile.

        Args:
            full_name: New full name

        Raises:
            DomainValidationError: If validation fails
        """
        if full_name is not None:
            self._validate_full_name(full_name)
            self._full_name = full_name
            self._updated_at = datetime.now(UTC)

    def change_password(self, new_hashed_password: str) -> None:
        """
        Change user password.

        Args:
            new_hashed_password: New hashed password
        """
        self._hashed_password = new_hashed_password
        self._updated_at = datetime.now(UTC)

    def update_last_login(self) -> None:
        """Update last login timestamp."""
        self._last_login = datetime.now(UTC)
        self._updated_at = datetime.now(UTC)

    def has_permission(self, permission: Permission) -> bool:
        """
        Check if user has a specific permission.

        Args:
            permission: Permission to check

        Returns:
            bool: True if user has permission
        """
        user_permissions = self.get_permissions()
        return permission in user_permissions

    def has_any_permission(self, permissions: list[Permission]) -> bool:
        """
        Check if user has any of the specified permissions.

        Args:
            permissions: List of permissions to check

        Returns:
            bool: True if user has at least one permission
        """
        user_permissions = self.get_permissions()
        return any(permission in user_permissions for permission in permissions)

    def has_all_permissions(self, permissions: list[Permission]) -> bool:
        """
        Check if user has all specified permissions.

        Args:
            permissions: List of permissions to check

        Returns:
            bool: True if user has all permissions
        """
        user_permissions = self.get_permissions()
        return all(permission in user_permissions for permission in permissions)

    def has_role(self, role: UserRole) -> bool:
        """
        Check if user has a specific role.

        Args:
            role: Role to check

        Returns:
            bool: True if user has role
        """
        return self._role == role

    def has_any_role(self, roles: list[UserRole]) -> bool:
        """
        Check if user has any of the specified roles.

        Args:
            roles: List of roles to check

        Returns:
            bool: True if user has at least one role
        """
        return self._role in roles

    def get_permissions(self) -> set[Permission]:
        """
        Get all permissions for the user based on their role.

        Returns:
            set[Permission]: Set of permissions
        """
        return self._get_role_permissions(self._role)

    @staticmethod
    def _get_role_permissions(role: UserRole) -> set[Permission]:
        """
        Get permissions for a specific role.

        Args:
            role: User role

        Returns:
            set[Permission]: Set of permissions for the role
        """
        role_permissions = {
            UserRole.ADMIN: {
                # Admin has all permissions
                Permission.WORKFLOW_CREATE,
                Permission.WORKFLOW_READ,
                Permission.WORKFLOW_UPDATE,
                Permission.WORKFLOW_DELETE,
                Permission.WORKFLOW_EXECUTE,
                Permission.WORKFLOW_SHARE,
                Permission.AGENT_CREATE,
                Permission.AGENT_READ,
                Permission.AGENT_UPDATE,
                Permission.AGENT_DELETE,
                Permission.AGENT_EXECUTE,
                Permission.MODEL_READ,
                Permission.MODEL_MANAGE,
                Permission.MODEL_INFERENCE,
                Permission.USER_READ,
                Permission.USER_MANAGE,
                Permission.USER_DELETE,
                Permission.SYSTEM_ADMIN,
                Permission.SYSTEM_MONITOR,
            },
            UserRole.MODERATOR: {
                # Moderator has workflow and agent management permissions
                Permission.WORKFLOW_CREATE,
                Permission.WORKFLOW_READ,
                Permission.WORKFLOW_UPDATE,
                Permission.WORKFLOW_DELETE,
                Permission.WORKFLOW_EXECUTE,
                Permission.WORKFLOW_SHARE,
                Permission.AGENT_CREATE,
                Permission.AGENT_READ,
                Permission.AGENT_UPDATE,
                Permission.AGENT_DELETE,
                Permission.AGENT_EXECUTE,
                Permission.MODEL_READ,
                Permission.MODEL_INFERENCE,
                Permission.USER_READ,
                Permission.SYSTEM_MONITOR,
            },
            UserRole.USER: {
                # Regular user has basic workflow and agent permissions
                Permission.WORKFLOW_CREATE,
                Permission.WORKFLOW_READ,
                Permission.WORKFLOW_UPDATE,
                Permission.WORKFLOW_EXECUTE,
                Permission.WORKFLOW_SHARE,
                Permission.AGENT_READ,
                Permission.AGENT_EXECUTE,
                Permission.MODEL_READ,
                Permission.MODEL_INFERENCE,
            },
            UserRole.VIEWER: {
                # Viewer has read-only permissions
                Permission.WORKFLOW_READ,
                Permission.AGENT_READ,
                Permission.MODEL_READ,
            },
        }

        return role_permissions.get(role, set())

    def to_dict(self) -> dict[str, Any]:
        """
        Convert user to dictionary representation.

        Returns:
            Dictionary representation of user (without password)
        """
        return {
            "id": self._id,
            "email": self._email.value,
            "username": self._username.value,
            "full_name": self._full_name,
            "role": self._role.value,
            "status": self._status.value,
            "is_active": self._is_active,
            "is_verified": self._is_verified,
            "last_login": self._last_login.isoformat() if self._last_login else None,
            "created_at": self._created_at.isoformat(),
            "updated_at": self._updated_at.isoformat(),
        }

    def get_events(self) -> list[DomainEvent]:
        """Get domain events."""
        return self._events.copy()

    def clear_events(self) -> None:
        """Clear domain events."""
        self._events.clear()

    def _add_event(self, event: DomainEvent) -> None:
        """Add domain event."""
        self._events.append(event)

    def _validate_full_name(self, full_name: str) -> None:
        """
        Validate full name.

        Args:
            full_name: Full name to validate

        Raises:
            DomainValidationError: If full name is invalid
        """
        if not full_name or not full_name.strip():
            raise DomainValidationError("Full name cannot be empty")

        if len(full_name.strip()) > 100:
            raise DomainValidationError("Full name cannot exceed 100 characters")

    def __eq__(self, other: Any) -> bool:
        """Check equality with another User object."""
        if not isinstance(other, User):
            return False
        return self._id == other._id

    def __hash__(self) -> int:
        """Return hash of the user ID."""
        return hash(self._id)

    def __str__(self) -> str:
        """Return string representation of the user."""
        return f"User(id={self._id}, username={self._username.value}, email={self._email.value})"

    def __repr__(self) -> str:
        """Return detailed string representation of the user."""
        return (
            f"User(id='{self._id}', email='{self._email.value}', "
            f"username='{self._username.value}', full_name='{self._full_name}', "
            f"is_active={self._is_active}, is_verified={self._is_verified})"
        )
