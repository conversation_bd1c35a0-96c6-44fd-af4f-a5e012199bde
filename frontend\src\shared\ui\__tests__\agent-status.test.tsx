import { render, screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import { AgentStatus } from '@/shared/types';
import { 
  AgentStatusIndicator, 
  getStatusColor, 
  isActiveStatus, 
  isCompletedStatus 
} from '../agent-status';

// Mock the animations module
vi.mock('@/shared/lib/animations', () => ({
  animateStatusIndicator: vi.fn(),
}));

describe('AgentStatusIndicator', () => {
  it('renders with default props', () => {
    render(<AgentStatusIndicator status={AgentStatus.IDLE} />);
    
    expect(screen.getByRole('status')).toBeInTheDocument();
    expect(screen.getByText('Idle')).toBeInTheDocument();
  });

  it('renders all status variants correctly', () => {
    const statuses = Object.values(AgentStatus);
    
    statuses.forEach((status) => {
      const { unmount } = render(<AgentStatusIndicator status={status} />);
      expect(screen.getByRole('status')).toBeInTheDocument();
      unmount();
    });
  });

  it('shows custom text when provided', () => {
    render(
      <AgentStatusIndicator 
        status={AgentStatus.RUNNING} 
        customText="Processing..." 
      />
    );
    
    expect(screen.getByText('Processing...')).toBeInTheDocument();
    expect(screen.queryByText('Running')).not.toBeInTheDocument();
  });

  it('hides icon when showIcon is false', () => {
    render(
      <AgentStatusIndicator 
        status={AgentStatus.RUNNING} 
        showIcon={false} 
      />
    );
    
    const statusElement = screen.getByRole('status');
    expect(statusElement.querySelector('svg')).not.toBeInTheDocument();
  });

  it('hides text when showText is false', () => {
    render(
      <AgentStatusIndicator 
        status={AgentStatus.RUNNING} 
        showText={false} 
      />
    );
    
    expect(screen.queryByText('Running')).not.toBeInTheDocument();
    expect(screen.getByRole('status').querySelector('svg')).toBeInTheDocument();
  });

  it('applies correct ARIA attributes', () => {
    render(<AgentStatusIndicator status={AgentStatus.RUNNING} />);
    
    const statusElement = screen.getByRole('status');
    expect(statusElement).toHaveAttribute('aria-live', 'polite');
    expect(statusElement).toHaveAttribute('aria-label', 'Agent is currently executing a task');
  });

  it('accepts custom ARIA label', () => {
    render(
      <AgentStatusIndicator 
        status={AgentStatus.RUNNING} 
        aria-label="Custom status description"
      />
    );
    
    expect(screen.getByLabelText('Custom status description')).toBeInTheDocument();
  });

  it('applies size variants correctly', () => {
    const { rerender } = render(
      <AgentStatusIndicator status={AgentStatus.IDLE} size="sm" />
    );
    
    let statusElement = screen.getByRole('status');
    expect(statusElement).toHaveClass('px-1.5', 'py-0.5', 'text-xs');
    
    rerender(<AgentStatusIndicator status={AgentStatus.IDLE} size="lg" />);
    statusElement = screen.getByRole('status');
    expect(statusElement).toHaveClass('px-3', 'py-1.5', 'text-sm');
  });

  it('applies variant styles correctly', () => {
    const { rerender } = render(
      <AgentStatusIndicator status={AgentStatus.IDLE} variant="minimal" />
    );
    
    let statusElement = screen.getByRole('status');
    expect(statusElement).toHaveClass('bg-transparent', 'px-0');
    
    rerender(<AgentStatusIndicator status={AgentStatus.IDLE} variant="outlined" />);
    statusElement = screen.getByRole('status');
    expect(statusElement).toHaveClass('border', 'border-current', 'bg-transparent');
  });

  it('shows spinning animation for running status', () => {
    render(<AgentStatusIndicator status={AgentStatus.RUNNING} />);
    
    const icon = screen.getByRole('status').querySelector('svg');
    expect(icon).toHaveClass('animate-spin');
  });

  it('applies pulse animation when specified', () => {
    render(<AgentStatusIndicator status={AgentStatus.RUNNING} pulse />);
    
    const icon = screen.getByRole('status').querySelector('svg');
    expect(icon).toHaveClass('animate-pulse');
  });
});

describe('AgentStatusIndicator utility functions', () => {
  describe('getStatusColor', () => {
    it('returns correct colors for each status', () => {
      expect(getStatusColor(AgentStatus.RUNNING)).toBe('green');
      expect(getStatusColor(AgentStatus.PAUSED)).toBe('yellow');
      expect(getStatusColor(AgentStatus.COMPLETED)).toBe('blue');
      expect(getStatusColor(AgentStatus.FAILED)).toBe('red');
      expect(getStatusColor(AgentStatus.CANCELLED)).toBe('gray');
      expect(getStatusColor(AgentStatus.IDLE)).toBe('slate');
    });
  });

  describe('isActiveStatus', () => {
    it('returns true for active statuses', () => {
      expect(isActiveStatus(AgentStatus.RUNNING)).toBe(true);
      expect(isActiveStatus(AgentStatus.PAUSED)).toBe(true);
    });

    it('returns false for inactive statuses', () => {
      expect(isActiveStatus(AgentStatus.IDLE)).toBe(false);
      expect(isActiveStatus(AgentStatus.COMPLETED)).toBe(false);
      expect(isActiveStatus(AgentStatus.FAILED)).toBe(false);
      expect(isActiveStatus(AgentStatus.CANCELLED)).toBe(false);
    });
  });

  describe('isCompletedStatus', () => {
    it('returns true for completed statuses', () => {
      expect(isCompletedStatus(AgentStatus.COMPLETED)).toBe(true);
      expect(isCompletedStatus(AgentStatus.FAILED)).toBe(true);
      expect(isCompletedStatus(AgentStatus.CANCELLED)).toBe(true);
    });

    it('returns false for non-completed statuses', () => {
      expect(isCompletedStatus(AgentStatus.IDLE)).toBe(false);
      expect(isCompletedStatus(AgentStatus.RUNNING)).toBe(false);
      expect(isCompletedStatus(AgentStatus.PAUSED)).toBe(false);
    });
  });
});
