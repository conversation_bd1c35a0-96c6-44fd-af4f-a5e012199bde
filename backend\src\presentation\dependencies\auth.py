"""
Authentication dependencies for FastAPI.

This module provides dependency functions for authentication
and authorization in FastAPI endpoints.
"""

from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from jose import JW<PERSON>rror
from sqlalchemy.ext.asyncio import AsyncSession

from src.infrastructure.database.connection import get_db_session
from src.infrastructure.logging.setup import get_logger
from src.infrastructure.security.jwt import jwt_manager

logger = get_logger(__name__)

# HTTP Bearer token scheme
security = HTTPBearer(auto_error=False)


class AuthenticationError(HTTPException):
    """Authentication error exception."""

    def __init__(self, detail: str = "Authentication failed"):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=detail,
            headers={"WWW-Authenticate": "Bearer"},
        )


class AuthorizationError(HTTPException):
    """Authorization error exception."""

    def __init__(self, detail: str = "Insufficient permissions"):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=detail,
        )


async def get_current_user_optional(
    credentials: HTTPAuthorizationCredentials | None = Depends(security),
) -> dict | None:
    """
    Get current user from JWT token (optional).

    Args:
        credentials: HTTP authorization credentials

    Returns:
        dict: User information or None if not authenticated
    """
    if not credentials:
        return None

    try:
        payload = jwt_manager.verify_token(credentials.credentials, "access")
        return {
            "user_id": payload["user_id"],
            "subject": payload["sub"],
            "token_id": payload.get("jti"),
        }
    except JWTError as e:
        logger.warning(f"Token verification failed: {e}")
        return None


async def get_current_user(
    credentials: HTTPAuthorizationCredentials | None = Depends(security),
) -> dict:
    """
    Get current user from JWT token (required).

    Args:
        credentials: HTTP authorization credentials

    Returns:
        dict: User information

    Raises:
        AuthenticationError: If authentication fails
    """
    if not credentials:
        raise AuthenticationError("Missing authentication token")

    try:
        payload = jwt_manager.verify_token(credentials.credentials, "access")
        return {
            "user_id": payload["user_id"],
            "subject": payload["sub"],
            "token_id": payload.get("jti"),
        }
    except JWTError as e:
        logger.warning(f"Authentication failed: {e}")
        raise AuthenticationError("Invalid or expired token")


async def get_refresh_token_payload(
    credentials: HTTPAuthorizationCredentials | None = Depends(security),
) -> dict:
    """
    Get refresh token payload.

    Args:
        credentials: HTTP authorization credentials

    Returns:
        dict: Refresh token payload

    Raises:
        AuthenticationError: If token is invalid
    """
    if not credentials:
        raise AuthenticationError("Missing refresh token")

    try:
        payload = jwt_manager.verify_token(credentials.credentials, "refresh")
        return {
            "user_id": payload["user_id"],
            "subject": payload["sub"],
            "token_id": payload.get("jti"),
        }
    except JWTError as e:
        logger.warning(f"Refresh token verification failed: {e}")
        raise AuthenticationError("Invalid or expired refresh token")


def require_permissions(*required_permissions: str):
    """
    Decorator to require specific permissions.

    Args:
        required_permissions: Required permission names

    Returns:
        Dependency function
    """

    async def permission_dependency(
        current_user: dict = Depends(get_current_user),
        db: AsyncSession = Depends(get_db_session),
    ) -> dict:
        """Check if user has required permissions."""
        # Import here to avoid circular imports
        import uuid

        from src.domain.entities.user import Permission
        from src.infrastructure.database.repositories.user_repository import (
            UserRepository,
        )

        try:
            # Get user from database
            user_repo = UserRepository(db)
            user_id = uuid.UUID(current_user["user_id"])
            user = await user_repo.get_by_id(user_id)

            if not user:
                logger.warning(f"User not found in database: {user_id}")
                raise AuthorizationError("User not found")

            # Check if user is active and verified
            if not user.is_active:
                logger.warning(f"Inactive user attempted access: {user_id}")
                raise AuthorizationError("User account is inactive")

            # Convert permission strings to Permission enums
            required_permission_enums = []
            for perm_str in required_permissions:
                try:
                    # Convert string to Permission enum
                    perm_enum = Permission(perm_str)
                    required_permission_enums.append(perm_enum)
                except ValueError:
                    logger.error(f"Invalid permission string: {perm_str}")
                    raise AuthorizationError(f"Invalid permission: {perm_str}")

            # Check if user has all required permissions
            if not user.has_all_permissions(required_permission_enums):
                user_permissions = [p.value for p in user.get_permissions()]
                logger.warning(
                    f"User {user_id} lacks permissions. "
                    f"Required: {required_permissions}, "
                    f"User has: {user_permissions}"
                )
                raise AuthorizationError(
                    f"Insufficient permissions. Required: {', '.join(required_permissions)}"
                )

            logger.debug(
                f"Permission check passed for user {user_id}: {required_permissions}"
            )

            # Add user entity to the current_user dict for downstream use
            current_user["user_entity"] = user
            return current_user

        except Exception as e:
            if isinstance(e, (AuthorizationError, AuthenticationError)):
                raise
            logger.error(
                f"Permission check failed for user {current_user['user_id']}: {e}"
            )
            raise AuthorizationError("Permission check failed")

    return permission_dependency


def require_roles(*required_roles: str):
    """
    Decorator to require specific roles.

    Args:
        required_roles: Required role names

    Returns:
        Dependency function
    """

    async def role_dependency(
        current_user: dict = Depends(get_current_user),
        db: AsyncSession = Depends(get_db_session),
    ) -> dict:
        """Check if user has required roles."""
        # Import here to avoid circular imports
        import uuid

        from src.domain.entities.user import UserRole
        from src.infrastructure.database.repositories.user_repository import (
            UserRepository,
        )

        try:
            # Get user from database
            user_repo = UserRepository(db)
            user_id = uuid.UUID(current_user["user_id"])
            user = await user_repo.get_by_id(user_id)

            if not user:
                logger.warning(f"User not found in database: {user_id}")
                raise AuthorizationError("User not found")

            # Check if user is active
            if not user.is_active:
                logger.warning(f"Inactive user attempted access: {user_id}")
                raise AuthorizationError("User account is inactive")

            # Convert role strings to UserRole enums
            required_role_enums = []
            for role_str in required_roles:
                try:
                    # Convert string to UserRole enum
                    role_enum = UserRole(role_str)
                    required_role_enums.append(role_enum)
                except ValueError:
                    logger.error(f"Invalid role string: {role_str}")
                    raise AuthorizationError(f"Invalid role: {role_str}")

            # Check if user has any of the required roles (OR logic)
            # Admin role has access to everything (hierarchical permissions)
            if user.role == UserRole.ADMIN:
                logger.debug(f"Admin user {user_id} granted access")
            elif not user.has_any_role(required_role_enums):
                logger.warning(
                    f"User {user_id} lacks required roles. "
                    f"Required: {required_roles}, "
                    f"User has: {user.role.value}"
                )
                raise AuthorizationError(
                    f"Insufficient role. Required one of: {', '.join(required_roles)}"
                )

            logger.debug(f"Role check passed for user {user_id}: {required_roles}")

            # Add user entity to the current_user dict for downstream use
            current_user["user_entity"] = user
            return current_user

        except Exception as e:
            if isinstance(e, (AuthorizationError, AuthenticationError)):
                raise
            logger.error(f"Role check failed for user {current_user['user_id']}: {e}")
            raise AuthorizationError("Role check failed")

    return role_dependency


# Common permission dependencies
require_admin = require_roles("admin")
require_user = require_roles("user", "admin")
require_moderator = require_roles("moderator", "admin")
