'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Plus, Search, Filter, MoreVertical, Edit, Trash2, Eye, Link, Tag } from 'lucide-react';
import { useKnowledgeEntities, knowledgeGraphService } from '@/entities/knowledge-graph/api';
import { KnowledgeEntity, EntityType } from '@/shared/types';
import { Button } from '@/shared/ui/button';
import { Input } from '@/shared/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/ui/card';
import { Badge } from '@/shared/ui/badge';
import { Avatar, AvatarFallback } from '@/shared/ui/avatar';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/shared/ui/dropdown-menu';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/shared/ui/select';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/shared/ui/tabs';
import { Progress } from '@/shared/ui/progress';
import { Skeleton } from '@/shared/ui/skeleton';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/shared/ui/dialog';
import { useToast } from '@/shared/hooks/use-toast';

interface EntityManagementProps {
  onEntitySelect?: (entity: KnowledgeEntity) => void;
  onCreateEntity?: () => void;
  className?: string;
}

export function EntityManagement({ onEntitySelect, onCreateEntity, className }: EntityManagementProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [entityTypeFilter, setEntityTypeFilter] = useState<EntityType | 'all'>('all');
  const [selectedEntity, setSelectedEntity] = useState<KnowledgeEntity | null>(null);
  const [showEntityDetails, setShowEntityDetails] = useState(false);
  const { toast } = useToast();

  // Fetch entities with current filters
  const { entities, loading, error, refetch } = useKnowledgeEntities({
    entity_type: entityTypeFilter === 'all' ? undefined : entityTypeFilter,
    limit: 100,
    offset: 0,
  });

  // Filter entities based on search
  const filteredEntities = entities.filter(entity => {
    const matchesSearch = entity.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         entity.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         entity.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    return matchesSearch;
  });

  // Calculate metrics
  const metrics = {
    total: entities.length,
    byType: Object.values(EntityType).reduce((acc, type) => {
      acc[type] = entities.filter(e => e.entity_type === type).length;
      return acc;
    }, {} as Record<EntityType, number>),
    public: entities.filter(e => e.is_public).length,
    private: entities.filter(e => !e.is_public).length,
    avgConfidence: entities.length > 0 
      ? entities.reduce((sum, e) => sum + e.confidence_score, 0) / entities.length 
      : 0,
  };

  const handleEntityAction = async (entity: KnowledgeEntity, action: 'view' | 'edit' | 'delete') => {
    try {
      switch (action) {
        case 'view':
          setSelectedEntity(entity);
          setShowEntityDetails(true);
          onEntitySelect?.(entity);
          break;
        case 'edit':
          // TODO: Open edit dialog
          toast({
            title: 'Edit Entity',
            description: `Editing ${entity.name} - feature coming soon`,
          });
          break;
        case 'delete':
          if (confirm(`Are you sure you want to delete "${entity.name}"?`)) {
            await knowledgeGraphService.deleteEntity(entity.id);
            toast({
              title: 'Entity Deleted',
              description: `${entity.name} has been deleted successfully.`,
            });
            await refetch();
          }
          break;
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: `Failed to ${action} entity: ${error}`,
        variant: 'destructive',
      });
    }
  };

  const getEntityTypeIcon = (entityType: EntityType): string => {
    const icons: Record<EntityType, string> = {
      [EntityType.PERSON]: '👤',
      [EntityType.ORGANIZATION]: '🏢',
      [EntityType.LOCATION]: '📍',
      [EntityType.CONCEPT]: '💡',
      [EntityType.DOCUMENT]: '📄',
      [EntityType.EVENT]: '📅',
      [EntityType.PRODUCT]: '📦',
      [EntityType.TECHNOLOGY]: '⚙️',
      [EntityType.WORKFLOW]: '🔄',
      [EntityType.AGENT]: '🤖',
      [EntityType.CUSTOM]: '🔧',
    };
    return icons[entityType] || '❓';
  };

  const getEntityTypeColor = (entityType: EntityType): string => {
    const colors: Record<EntityType, string> = {
      [EntityType.PERSON]: 'blue',
      [EntityType.ORGANIZATION]: 'green',
      [EntityType.LOCATION]: 'yellow',
      [EntityType.CONCEPT]: 'purple',
      [EntityType.DOCUMENT]: 'red',
      [EntityType.EVENT]: 'cyan',
      [EntityType.PRODUCT]: 'lime',
      [EntityType.TECHNOLOGY]: 'orange',
      [EntityType.WORKFLOW]: 'pink',
      [EntityType.AGENT]: 'indigo',
      [EntityType.CUSTOM]: 'gray',
    };
    return colors[entityType] || 'gray';
  };

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-red-500 mb-4">Failed to load entities</p>
          <Button onClick={refetch}>Retry</Button>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Knowledge Entities</h1>
          <p className="text-muted-foreground">
            Manage and explore your knowledge graph entities
          </p>
        </div>
        <Button onClick={onCreateEntity} className="gap-2">
          <Plus className="h-4 w-4" />
          Create Entity
        </Button>
      </div>

      {/* Metrics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Entities</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.total}</div>
            <p className="text-xs text-muted-foreground">
              {metrics.public} public, {metrics.private} private
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Most Common Type</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Object.entries(metrics.byType).reduce((a, b) => metrics.byType[a[0] as EntityType] > metrics.byType[b[0] as EntityType] ? a : b)[0]}
            </div>
            <p className="text-xs text-muted-foreground">
              {Math.max(...Object.values(metrics.byType))} entities
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Confidence</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{(metrics.avgConfidence * 100).toFixed(1)}%</div>
            <Progress value={metrics.avgConfidence * 100} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Entity Types</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{Object.values(metrics.byType).filter(count => count > 0).length}</div>
            <p className="text-xs text-muted-foreground">
              of {Object.values(EntityType).length} types used
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search entities..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <Select value={entityTypeFilter} onValueChange={(value) => setEntityTypeFilter(value as EntityType | 'all')}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            {Object.values(EntityType).map(type => (
              <SelectItem key={type} value={type}>
                {getEntityTypeIcon(type)} {type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Entity Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <AnimatePresence>
          {loading ? (
            // Loading skeletons
            Array.from({ length: 6 }).map((_, i) => (
              <Card key={i}>
                <CardHeader>
                  <div className="flex items-center space-x-4">
                    <Skeleton className="h-12 w-12 rounded-full" />
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-[200px]" />
                      <Skeleton className="h-4 w-[160px]" />
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-3/4" />
                  </div>
                </CardContent>
              </Card>
            ))
          ) : (
            filteredEntities.map((entity) => (
              <motion.div
                key={entity.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.2 }}
              >
                <EntityCard
                  entity={entity}
                  onAction={handleEntityAction}
                  onClick={() => handleEntityAction(entity, 'view')}
                />
              </motion.div>
            ))
          )}
        </AnimatePresence>
      </div>

      {/* Empty State */}
      {!loading && filteredEntities.length === 0 && (
        <div className="text-center py-12">
          <div className="mx-auto w-24 h-24 bg-muted rounded-full flex items-center justify-center mb-4">
            <Search className="h-8 w-8 text-muted-foreground" />
          </div>
          <h3 className="text-lg font-semibold mb-2">No entities found</h3>
          <p className="text-muted-foreground mb-4">
            {searchQuery || entityTypeFilter !== 'all'
              ? 'Try adjusting your search or filters'
              : 'Create your first knowledge entity to get started'}
          </p>
          <Button onClick={onCreateEntity}>
            <Plus className="h-4 w-4 mr-2" />
            Create Entity
          </Button>
        </div>
      )}

      {/* Entity Details Dialog */}
      <Dialog open={showEntityDetails} onOpenChange={setShowEntityDetails}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Entity Details</DialogTitle>
          </DialogHeader>
          {selectedEntity && (
            <EntityDetails entity={selectedEntity} />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}

interface EntityCardProps {
  entity: KnowledgeEntity;
  onAction: (entity: KnowledgeEntity, action: 'view' | 'edit' | 'delete') => void;
  onClick: () => void;
}

function EntityCard({ entity, onAction, onClick }: EntityCardProps) {
  const getEntityTypeIcon = (entityType: EntityType): string => {
    const icons: Record<EntityType, string> = {
      [EntityType.PERSON]: '👤',
      [EntityType.ORGANIZATION]: '🏢',
      [EntityType.LOCATION]: '📍',
      [EntityType.CONCEPT]: '💡',
      [EntityType.DOCUMENT]: '📄',
      [EntityType.EVENT]: '📅',
      [EntityType.PRODUCT]: '📦',
      [EntityType.TECHNOLOGY]: '⚙️',
      [EntityType.WORKFLOW]: '🔄',
      [EntityType.AGENT]: '🤖',
      [EntityType.CUSTOM]: '🔧',
    };
    return icons[entityType] || '❓';
  };

  return (
    <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={onClick}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Avatar>
              <AvatarFallback className="bg-primary/10 text-lg">
                {getEntityTypeIcon(entity.entity_type)}
              </AvatarFallback>
            </Avatar>
            <div>
              <CardTitle className="text-base">{entity.name}</CardTitle>
              <p className="text-sm text-muted-foreground">{entity.entity_type}</p>
            </div>
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
              <Button variant="ghost" size="sm">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onAction(entity, 'view')}>
                <Eye className="h-4 w-4 mr-2" />
                View Details
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onAction(entity, 'edit')}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => onAction(entity, 'delete')}
                className="text-destructive"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Badge variant={entity.is_public ? 'default' : 'secondary'}>
              {entity.is_public ? 'Public' : 'Private'}
            </Badge>
            <span className="text-sm text-muted-foreground">
              {(entity.confidence_score * 100).toFixed(1)}% confidence
            </span>
          </div>
          
          {entity.description && (
            <p className="text-sm text-muted-foreground line-clamp-2">
              {entity.description}
            </p>
          )}
          
          <div className="flex flex-wrap gap-1">
            {entity.tags.slice(0, 3).map((tag) => (
              <Badge key={tag} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
            {entity.tags.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{entity.tags.length - 3}
              </Badge>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

interface EntityDetailsProps {
  entity: KnowledgeEntity;
}

function EntityDetails({ entity }: EntityDetailsProps) {
  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-4">
        <Avatar className="h-16 w-16">
          <AvatarFallback className="bg-primary/10 text-2xl">
            {entity.entity_type === EntityType.PERSON ? '👤' : 
             entity.entity_type === EntityType.ORGANIZATION ? '🏢' : 
             entity.entity_type === EntityType.CONCEPT ? '💡' : '📄'}
          </AvatarFallback>
        </Avatar>
        <div>
          <h2 className="text-2xl font-bold">{entity.name}</h2>
          <Badge variant="outline" className="mt-1">
            {entity.entity_type}
          </Badge>
        </div>
      </div>

      {entity.description && (
        <div>
          <h3 className="text-lg font-semibold mb-2">Description</h3>
          <p className="text-muted-foreground">{entity.description}</p>
        </div>
      )}

      <div className="grid grid-cols-2 gap-4">
        <div>
          <h3 className="text-lg font-semibold mb-2">Confidence Score</h3>
          <div className="flex items-center space-x-2">
            <Progress value={entity.confidence_score * 100} className="flex-1" />
            <span className="text-sm font-medium">{(entity.confidence_score * 100).toFixed(1)}%</span>
          </div>
        </div>
        
        <div>
          <h3 className="text-lg font-semibold mb-2">Visibility</h3>
          <Badge variant={entity.is_public ? 'default' : 'secondary'}>
            {entity.is_public ? 'Public' : 'Private'}
          </Badge>
        </div>
      </div>

      {entity.tags.length > 0 && (
        <div>
          <h3 className="text-lg font-semibold mb-2">Tags</h3>
          <div className="flex flex-wrap gap-2">
            {entity.tags.map((tag) => (
              <Badge key={tag} variant="outline">
                <Tag className="h-3 w-3 mr-1" />
                {tag}
              </Badge>
            ))}
          </div>
        </div>
      )}

      {entity.categories.length > 0 && (
        <div>
          <h3 className="text-lg font-semibold mb-2">Categories</h3>
          <div className="flex flex-wrap gap-2">
            {entity.categories.map((category) => (
              <Badge key={category} variant="secondary">
                {category}
              </Badge>
            ))}
          </div>
        </div>
      )}

      {Object.keys(entity.properties).length > 0 && (
        <div>
          <h3 className="text-lg font-semibold mb-2">Properties</h3>
          <div className="bg-muted rounded-lg p-4">
            <pre className="text-sm overflow-x-auto">
              {JSON.stringify(entity.properties, null, 2)}
            </pre>
          </div>
        </div>
      )}

      <div className="text-sm text-muted-foreground">
        <div>Created: {new Date(entity.createdAt).toLocaleDateString()}</div>
        <div>Updated: {new Date(entity.updatedAt).toLocaleDateString()}</div>
        {entity.source_id && (
          <div>Source: {entity.source_type} ({entity.source_id})</div>
        )}
      </div>
    </div>
  );
}
