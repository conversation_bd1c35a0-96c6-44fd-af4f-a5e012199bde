"""
Workflow repository implementation.

This module contains the SQLAlchemy implementation of the workflow repository.
"""

import uuid

from sqlalchemy import and_, func, or_, select
from sqlalchemy.ext.asyncio import AsyncSession

from src.domain.entities.workflow import Workflow, WorkflowExecution, WorkflowStatus
from src.domain.repositories.workflow_repository import (
    WorkflowExecutionRepository,
    WorkflowRepository,
)
from src.infrastructure.database.models.workflow import (
    WorkflowExecutionModel,
    WorkflowModel,
)


class WorkflowRepositoryImpl(WorkflowRepository):
    """SQLAlchemy implementation of the workflow repository."""

    def __init__(self, session: AsyncSession) -> None:
        """
        Initialize the repository.

        Args:
            session: The database session
        """
        self._session = session

    async def get_by_id(self, workflow_id: uuid.UUID) -> Workflow | None:
        """Get a workflow by its ID."""
        query = select(WorkflowModel).where(WorkflowModel.id == workflow_id)
        result = await self._session.execute(query)
        workflow_model = result.scalar_one_or_none()

        return workflow_model.to_domain_entity() if workflow_model else None

    async def get_by_owner(
        self,
        owner_id: uuid.UUID,
        status: WorkflowStatus | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[Workflow]:
        """Get workflows by owner ID with optional filtering."""
        query = select(WorkflowModel).where(WorkflowModel.owner_id == owner_id)

        if status:
            query = query.where(WorkflowModel.status == status)

        query = (
            query.order_by(WorkflowModel.updated_at.desc()).limit(limit).offset(offset)
        )

        result = await self._session.execute(query)
        workflow_models = result.scalars().all()

        return [workflow_model.to_domain_entity() for workflow_model in workflow_models]

    async def get_public_workflows(
        self, limit: int = 100, offset: int = 0
    ) -> list[Workflow]:
        """Get public workflows."""
        query = (
            select(WorkflowModel)
            .where(
                and_(
                    WorkflowModel.is_public == True,
                    WorkflowModel.status == WorkflowStatus.ACTIVE,
                )
            )
            .order_by(WorkflowModel.updated_at.desc())
            .limit(limit)
            .offset(offset)
        )

        result = await self._session.execute(query)
        workflow_models = result.scalars().all()

        return [workflow_model.to_domain_entity() for workflow_model in workflow_models]

    async def search_workflows(
        self,
        query: str,
        owner_id: uuid.UUID | None = None,
        include_public: bool = True,
        limit: int = 100,
        offset: int = 0,
    ) -> list[Workflow]:
        """Search workflows by name or description."""
        search_filter = or_(
            WorkflowModel.name.ilike(f"%{query}%"),
            WorkflowModel.description.ilike(f"%{query}%"),
        )

        base_query = select(WorkflowModel).where(search_filter)

        # Apply ownership and public visibility filters
        if owner_id and include_public:
            # User's own workflows OR public workflows
            visibility_filter = or_(
                WorkflowModel.owner_id == owner_id,
                and_(
                    WorkflowModel.is_public == True,
                    WorkflowModel.status == WorkflowStatus.ACTIVE,
                ),
            )
            base_query = base_query.where(visibility_filter)
        elif owner_id:
            # Only user's own workflows
            base_query = base_query.where(WorkflowModel.owner_id == owner_id)
        elif include_public:
            # Only public workflows
            base_query = base_query.where(
                and_(
                    WorkflowModel.is_public == True,
                    WorkflowModel.status == WorkflowStatus.ACTIVE,
                )
            )

        base_query = (
            base_query.order_by(WorkflowModel.updated_at.desc())
            .limit(limit)
            .offset(offset)
        )

        result = await self._session.execute(base_query)
        workflow_models = result.scalars().all()

        return [workflow_model.to_domain_entity() for workflow_model in workflow_models]

    async def create(self, workflow: Workflow) -> Workflow:
        """Create a new workflow."""
        workflow_model = WorkflowModel.from_domain_entity(workflow)

        self._session.add(workflow_model)
        await self._session.flush()
        await self._session.refresh(workflow_model)

        return workflow_model.to_domain_entity()

    async def update(self, workflow: Workflow) -> Workflow:
        """Update an existing workflow."""
        query = select(WorkflowModel).where(WorkflowModel.id == workflow.id)
        result = await self._session.execute(query)
        workflow_model = result.scalar_one_or_none()

        if not workflow_model:
            raise ValueError(f"Workflow with ID {workflow.id} not found")

        workflow_model.update_from_domain_entity(workflow)
        await self._session.flush()
        await self._session.refresh(workflow_model)

        return workflow_model.to_domain_entity()

    async def delete(self, workflow_id: uuid.UUID) -> bool:
        """Delete a workflow."""
        query = select(WorkflowModel).where(WorkflowModel.id == workflow_id)
        result = await self._session.execute(query)
        workflow_model = result.scalar_one_or_none()

        if not workflow_model:
            return False

        await self._session.delete(workflow_model)
        await self._session.flush()

        return True

    async def count_by_owner(self, owner_id: uuid.UUID) -> int:
        """Count workflows by owner."""
        query = select(func.count(WorkflowModel.id)).where(
            WorkflowModel.owner_id == owner_id
        )
        result = await self._session.execute(query)
        return result.scalar() or 0

    async def get_execution_history(
        self,
        workflow_id: uuid.UUID,
        limit: int = 100,
        offset: int = 0,
    ) -> list[WorkflowExecution]:
        """Get execution history for a workflow."""
        query = (
            select(WorkflowExecutionModel)
            .where(WorkflowExecutionModel.workflow_id == workflow_id)
            .order_by(WorkflowExecutionModel.created_at.desc())
            .limit(limit)
            .offset(offset)
        )

        result = await self._session.execute(query)
        execution_models = result.scalars().all()

        return [model.to_domain_entity() for model in execution_models]

    async def create_execution(self, execution: WorkflowExecution) -> WorkflowExecution:
        """Create a new workflow execution."""
        execution_model = WorkflowExecutionModel.from_domain_entity(execution)

        self._session.add(execution_model)
        await self._session.flush()
        await self._session.refresh(execution_model)

        return execution_model.to_domain_entity()

    async def update_execution(self, execution: WorkflowExecution) -> WorkflowExecution:
        """Update a workflow execution."""
        query = select(WorkflowExecutionModel).where(
            WorkflowExecutionModel.id == execution.id
        )
        result = await self._session.execute(query)
        execution_model = result.scalar_one_or_none()

        if not execution_model:
            raise ValueError(f"Execution with ID {execution.id} not found")

        execution_model.update_from_domain_entity(execution)
        await self._session.flush()
        await self._session.refresh(execution_model)

        return execution_model.to_domain_entity()

    async def get_execution_by_id(
        self, execution_id: uuid.UUID
    ) -> WorkflowExecution | None:
        """Get workflow execution by ID."""
        query = select(WorkflowExecutionModel).where(
            WorkflowExecutionModel.id == execution_id
        )
        result = await self._session.execute(query)
        execution_model = result.scalar_one_or_none()

        return execution_model.to_domain_entity() if execution_model else None

    async def get_workflow_statistics(self, workflow_id: uuid.UUID) -> dict:
        """Get workflow execution statistics."""
        # Count executions by status
        from src.domain.entities.workflow import ExecutionStatus

        status_counts = {}
        for status in ExecutionStatus:
            count_query = select(func.count(WorkflowExecutionModel.id)).where(
                and_(
                    WorkflowExecutionModel.workflow_id == workflow_id,
                    WorkflowExecutionModel.status == status,
                )
            )
            result = await self._session.execute(count_query)
            status_counts[status.value] = result.scalar() or 0

        # Get average execution time
        avg_time_query = select(func.avg(WorkflowExecutionModel.execution_time)).where(
            and_(
                WorkflowExecutionModel.workflow_id == workflow_id,
                WorkflowExecutionModel.execution_time.isnot(None),
            )
        )
        avg_time_result = await self._session.execute(avg_time_query)
        avg_execution_time = avg_time_result.scalar() or 0

        # Get total cost
        total_cost_query = select(func.sum(WorkflowExecutionModel.cost)).where(
            WorkflowExecutionModel.workflow_id == workflow_id
        )
        cost_result = await self._session.execute(total_cost_query)
        total_cost = cost_result.scalar() or 0

        total_executions = sum(status_counts.values())
        success_rate = (
            status_counts.get("completed", 0) / max(total_executions, 1)
        ) * 100

        return {
            "status_counts": status_counts,
            "total_executions": total_executions,
            "avg_execution_time": avg_execution_time,
            "total_cost": total_cost,
            "success_rate": success_rate,
        }


class WorkflowExecutionRepositoryImpl(WorkflowExecutionRepository):
    """SQLAlchemy implementation of the workflow execution repository."""

    def __init__(self, session: AsyncSession) -> None:
        """
        Initialize the repository.

        Args:
            session: The database session
        """
        self._session = session

    async def get_by_id(self, execution_id: uuid.UUID) -> WorkflowExecution | None:
        """Get a workflow execution by its ID."""
        query = select(WorkflowExecutionModel).where(
            WorkflowExecutionModel.id == execution_id
        )
        result = await self._session.execute(query)
        execution_model = result.scalar_one_or_none()

        return execution_model.to_domain_entity() if execution_model else None

    async def get_by_workflow(
        self, workflow_id: uuid.UUID, limit: int = 100, offset: int = 0
    ) -> list[WorkflowExecution]:
        """Get executions for a specific workflow."""
        query = (
            select(WorkflowExecutionModel)
            .where(WorkflowExecutionModel.workflow_id == workflow_id)
            .order_by(WorkflowExecutionModel.created_at.desc())
            .limit(limit)
            .offset(offset)
        )

        result = await self._session.execute(query)
        execution_models = result.scalars().all()

        return [
            execution_model.to_domain_entity() for execution_model in execution_models
        ]

    async def get_by_user(
        self, user_id: uuid.UUID, limit: int = 100, offset: int = 0
    ) -> list[WorkflowExecution]:
        """Get executions for a specific user."""
        query = (
            select(WorkflowExecutionModel)
            .where(WorkflowExecutionModel.user_id == user_id)
            .order_by(WorkflowExecutionModel.created_at.desc())
            .limit(limit)
            .offset(offset)
        )

        result = await self._session.execute(query)
        execution_models = result.scalars().all()

        return [
            execution_model.to_domain_entity() for execution_model in execution_models
        ]

    async def get_running_executions(self) -> list[WorkflowExecution]:
        """Get all currently running executions."""
        from src.domain.entities.workflow import ExecutionStatus

        query = select(WorkflowExecutionModel).where(
            WorkflowExecutionModel.status == ExecutionStatus.RUNNING
        )

        result = await self._session.execute(query)
        execution_models = result.scalars().all()

        return [
            execution_model.to_domain_entity() for execution_model in execution_models
        ]

    async def create(self, execution: WorkflowExecution) -> WorkflowExecution:
        """Create a new workflow execution."""
        execution_model = WorkflowExecutionModel.from_domain_entity(execution)

        self._session.add(execution_model)
        await self._session.flush()
        await self._session.refresh(execution_model)

        return execution_model.to_domain_entity()

    async def update(self, execution: WorkflowExecution) -> WorkflowExecution:
        """Update an existing workflow execution."""
        query = select(WorkflowExecutionModel).where(
            WorkflowExecutionModel.id == execution.id
        )
        result = await self._session.execute(query)
        execution_model = result.scalar_one_or_none()

        if not execution_model:
            raise ValueError(f"Execution with ID {execution.id} not found")

        execution_model.update_from_domain_entity(execution)
        await self._session.flush()
        await self._session.refresh(execution_model)

        return execution_model.to_domain_entity()

    async def delete(self, execution_id: uuid.UUID) -> bool:
        """Delete a workflow execution."""
        query = select(WorkflowExecutionModel).where(
            WorkflowExecutionModel.id == execution_id
        )
        result = await self._session.execute(query)
        execution_model = result.scalar_one_or_none()

        if not execution_model:
            return False

        await self._session.delete(execution_model)
        await self._session.flush()

        return True

    async def count_by_workflow(self, workflow_id: uuid.UUID) -> int:
        """Count executions for a workflow."""
        query = select(func.count(WorkflowExecutionModel.id)).where(
            WorkflowExecutionModel.workflow_id == workflow_id
        )
        result = await self._session.execute(query)
        return result.scalar() or 0

    async def count_by_user(self, user_id: uuid.UUID) -> int:
        """Count executions for a user."""
        query = select(func.count(WorkflowExecutionModel.id)).where(
            WorkflowExecutionModel.user_id == user_id
        )
        result = await self._session.execute(query)
        return result.scalar() or 0
