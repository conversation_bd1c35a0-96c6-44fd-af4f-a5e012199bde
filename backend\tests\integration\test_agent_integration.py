"""
Agent Integration Tests

Integration tests for the complete agent system including
API endpoints, service layer, and database operations.
"""

import uuid
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from fastapi import FastAP<PERSON>
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession

from src.domain.entities.agent import Agent<PERSON>tatus, AgentType
from src.domain.entities.user import Permission, User, UserRole, UserStatus
from src.domain.value_objects.email import Email
from src.domain.value_objects.username import Username
from src.presentation.api.routes.agents import router


@pytest.fixture
def app():
    """Create FastAPI app with agent router."""
    app = FastAPI()
    app.include_router(router)
    return app


@pytest.fixture
def client(app):
    """Create test client."""
    return TestClient(app)


@pytest.fixture
def sample_user_id():
    """Sample user ID for testing."""
    return uuid.uuid4()


@pytest.fixture
def admin_user(sample_user_id):
    """Create admin user entity."""
    return User(
        id=str(sample_user_id),
        email=Email("<EMAIL>"),
        username=Username("admin"),
        full_name="Admin User",
        hashed_password="hashed_password",
        role=UserRole.ADMIN,
        status=UserStatus.ACTIVE,
        is_active=True,
        is_verified=True,
    )


@pytest.fixture
def mock_jwt_token():
    """Mock JWT token for testing."""
    return "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.test.token"


class TestAgentLifecycle:
    """Test complete agent lifecycle."""

    @patch("src.presentation.api.routes.agents.get_agent_service")
    @patch("src.infrastructure.database.repositories.user_repository.UserRepository")
    @patch("src.infrastructure.security.jwt.jwt_manager.verify_token")
    @patch("src.infrastructure.database.connection.get_db_session")
    def test_complete_agent_lifecycle(
        self, mock_get_db, mock_verify_token, mock_user_repo_class, mock_get_service,
        client, admin_user, sample_user_id, mock_jwt_token
    ):
        """Test complete agent lifecycle: create, update, activate, chat, delete."""
        agent_id = uuid.uuid4()
        
        # Setup authentication mocks
        mock_verify_token.return_value = {
            "user_id": str(sample_user_id),
            "sub": "<EMAIL>",
            "jti": "token-id"
        }
        
        mock_db = AsyncMock(spec=AsyncSession)
        mock_get_db.return_value.__aenter__.return_value = mock_db
        
        mock_user_repo = AsyncMock()
        mock_user_repo.get_by_id.return_value = admin_user
        mock_user_repo_class.return_value = mock_user_repo

        # Mock agent service
        mock_service = AsyncMock()
        mock_get_service.return_value = mock_service

        # Step 1: Create agent
        create_agent_data = {
            "name": "Research Assistant",
            "description": "An AI agent specialized in research and analysis",
            "agent_type": "research_assistant",
            "version": "1.0.0",
            "configuration": {
                "model_name": "gpt-4",
                "temperature": 0.3,
                "max_tokens": 4096,
                "timeout_seconds": 600,
                "retry_attempts": 3,
                "memory_enabled": True,
                "tools_enabled": True,
                "custom_settings": {
                    "research_depth": "comprehensive",
                    "citation_style": "apa"
                }
            },
            "capabilities": [
                {
                    "name": "web_search",
                    "description": "Search the web for information",
                    "parameters": {"max_results": 10},
                    "required": True
                },
                {
                    "name": "document_analysis",
                    "description": "Analyze documents and extract insights",
                    "parameters": {"supported_formats": ["pdf", "docx", "txt"]},
                    "required": False
                }
            ],
            "tags": ["research", "analysis", "academic"],
            "is_public": False
        }

        # Mock created agent
        mock_created_agent = MagicMock()
        mock_created_agent.to_dict.return_value = {
            "id": str(agent_id),
            "name": "Research Assistant",
            "description": "An AI agent specialized in research and analysis",
            "agent_type": "research_assistant",
            "version": "1.0.0",
            "status": "inactive",
            "current_task_id": None,
            "configuration": create_agent_data["configuration"],
            "capabilities": create_agent_data["capabilities"],
            "metrics": {
                "total_executions": 0,
                "successful_executions": 0,
                "failed_executions": 0,
                "average_execution_time": 0.0,
                "last_execution_time": None,
                "total_tokens_used": 0,
                "total_cost": 0.0
            },
            "created_by": str(sample_user_id),
            "parent_agent_id": None,
            "child_agent_ids": [],
            "tags": create_agent_data["tags"],
            "metadata": {"is_public": False},
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-01T00:00:00Z",
        }
        mock_service.create_agent.return_value = mock_created_agent

        # Create agent
        create_response = client.post(
            "/agents/",
            json=create_agent_data,
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        assert create_response.status_code == 201
        created_data = create_response.json()
        assert created_data["name"] == "Research Assistant"
        assert created_data["agent_type"] == "research_assistant"
        assert created_data["status"] == "inactive"
        assert len(created_data["capabilities"]) == 2
        assert "research" in created_data["tags"]

        # Step 2: Get agent
        mock_service.get_agent.return_value = mock_created_agent

        get_response = client.get(
            f"/agents/{agent_id}",
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        assert get_response.status_code == 200
        get_data = get_response.json()
        assert get_data["id"] == str(agent_id)
        assert get_data["name"] == "Research Assistant"

        # Step 3: Update agent
        update_data = {
            "description": "An advanced AI research assistant with enhanced capabilities",
            "tags": ["research", "analysis", "academic", "enhanced"],
            "configuration": {
                "temperature": 0.2,
                "max_tokens": 8192
            }
        }

        # Mock existing agent for update
        mock_existing_agent = MagicMock()
        mock_existing_agent.id = agent_id
        mock_existing_agent.name = "Research Assistant"
        mock_existing_agent.description = "An AI agent specialized in research and analysis"
        mock_existing_agent.agent_type = AgentType.RESEARCH_ASSISTANT
        mock_existing_agent.version = "1.0.0"
        mock_existing_agent.tags = ["research", "analysis", "academic"]
        mock_existing_agent.configuration = MagicMock()
        mock_existing_agent.configuration.model_name = "gpt-4"
        mock_existing_agent.configuration.temperature = 0.3
        mock_existing_agent.configuration.max_tokens = 4096
        mock_existing_agent.metadata = {"is_public": False}

        # Mock updated agent
        mock_updated_agent = MagicMock()
        mock_updated_agent.to_dict.return_value = {
            "id": str(agent_id),
            "name": "Research Assistant",
            "description": "An advanced AI research assistant with enhanced capabilities",
            "agent_type": "research_assistant",
            "version": "1.0.0",
            "status": "inactive",
            "current_task_id": None,
            "configuration": {
                **create_agent_data["configuration"],
                "temperature": 0.2,
                "max_tokens": 8192
            },
            "capabilities": create_agent_data["capabilities"],
            "metrics": {
                "total_executions": 0,
                "successful_executions": 0,
                "failed_executions": 0,
                "average_execution_time": 0.0,
                "last_execution_time": None,
                "total_tokens_used": 0,
                "total_cost": 0.0
            },
            "created_by": str(sample_user_id),
            "parent_agent_id": None,
            "child_agent_ids": [],
            "tags": ["research", "analysis", "academic", "enhanced"],
            "metadata": {"is_public": False},
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-01T01:00:00Z",
        }

        mock_service.get_agent.return_value = mock_existing_agent
        mock_service.update_agent.return_value = mock_updated_agent

        update_response = client.put(
            f"/agents/{agent_id}",
            json=update_data,
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        assert update_response.status_code == 200
        updated_data = update_response.json()
        assert updated_data["description"] == "An advanced AI research assistant with enhanced capabilities"
        assert "enhanced" in updated_data["tags"]
        assert updated_data["configuration"]["temperature"] == 0.2

        # Step 4: Activate agent
        mock_activated_agent = MagicMock()
        mock_activated_agent.id = agent_id
        mock_activated_agent.status = AgentStatus.ACTIVE
        mock_activated_agent.updated_at = "2024-01-01T02:00:00Z"
        mock_service.activate_agent.return_value = mock_activated_agent

        activate_response = client.post(
            f"/agents/{agent_id}/activate",
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        assert activate_response.status_code == 200
        activate_data = activate_response.json()
        assert activate_data["status"] == "active"
        assert "activated successfully" in activate_data["message"]

        # Step 5: Chat with agent
        chat_data = {
            "message": "Can you help me research the latest developments in quantum computing?",
            "context": {"research_topic": "quantum_computing"}
        }

        mock_service.get_agent.return_value = mock_activated_agent

        chat_response = client.post(
            f"/agents/{agent_id}/chat",
            json=chat_data,
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        assert chat_response.status_code == 200
        chat_response_data = chat_response.json()
        assert "conversation_id" in chat_response_data
        assert "message_id" in chat_response_data
        assert "response" in chat_response_data
        assert "tokens_used" in chat_response_data

        # Step 6: List agents
        mock_service.list_agents.return_value = [mock_updated_agent]

        list_response = client.get(
            "/agents/",
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        assert list_response.status_code == 200
        list_data = list_response.json()
        assert len(list_data["agents"]) == 1
        assert list_data["agents"][0]["id"] == str(agent_id)

        # Step 7: Search agents
        mock_service.search_agents.return_value = [mock_updated_agent]

        search_response = client.get(
            "/agents/search?query=research",
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        assert search_response.status_code == 200
        search_data = search_response.json()
        assert len(search_data["agents"]) == 1
        assert search_data["agents"][0]["name"] == "Research Assistant"

        # Step 8: Get metrics
        mock_metrics = {
            "total_agents": 1,
            "active_agents": 1,
            "total_executions": 5,
            "total_cost": 0.0234,
            "total_tokens_used": 1250,
            "average_success_rate": 100.0
        }
        mock_service.get_agent_metrics.return_value = mock_metrics

        metrics_response = client.get(
            "/agents/metrics/summary",
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        assert metrics_response.status_code == 200
        metrics_data = metrics_response.json()
        assert metrics_data["total_agents"] == 1
        assert metrics_data["active_agents"] == 1
        assert metrics_data["average_success_rate"] == 100.0

        # Step 9: Delete agent
        mock_service.delete_agent.return_value = True

        delete_response = client.delete(
            f"/agents/{agent_id}",
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        assert delete_response.status_code == 200
        delete_data = delete_response.json()
        assert delete_data["success"] is True

        # Verify all service methods were called
        mock_service.create_agent.assert_called_once()
        mock_service.get_agent.assert_called()
        mock_service.update_agent.assert_called_once()
        mock_service.activate_agent.assert_called_once()
        mock_service.list_agents.assert_called_once()
        mock_service.search_agents.assert_called_once()
        mock_service.get_agent_metrics.assert_called_once()
        mock_service.delete_agent.assert_called_once()


class TestAgentPermissions:
    """Test agent permission enforcement."""

    @patch("src.presentation.api.routes.agents.get_agent_service")
    @patch("src.infrastructure.database.repositories.user_repository.UserRepository")
    @patch("src.infrastructure.security.jwt.jwt_manager.verify_token")
    @patch("src.infrastructure.database.connection.get_db_session")
    def test_agent_permissions_enforcement(
        self, mock_get_db, mock_verify_token, mock_user_repo_class, mock_get_service,
        client, sample_user_id, mock_jwt_token
    ):
        """Test that agent permissions are properly enforced."""
        # Create viewer user (limited permissions)
        viewer_user = User(
            id=str(sample_user_id),
            email=Email("<EMAIL>"),
            username=Username("viewer"),
            full_name="Viewer User",
            hashed_password="hashed_password",
            role=UserRole.VIEWER,
            status=UserStatus.ACTIVE,
            is_active=True,
            is_verified=True,
        )

        # Setup mocks
        mock_verify_token.return_value = {
            "user_id": str(sample_user_id),
            "sub": "<EMAIL>",
            "jti": "token-id"
        }
        
        mock_db = AsyncMock(spec=AsyncSession)
        mock_get_db.return_value.__aenter__.return_value = mock_db
        
        mock_user_repo = AsyncMock()
        mock_user_repo.get_by_id.return_value = viewer_user
        mock_user_repo_class.return_value = mock_user_repo

        # Test create agent (should fail - viewer doesn't have create permission)
        create_data = {
            "name": "Test Agent",
            "description": "Test description",
            "agent_type": "conversational",
            "configuration": {
                "model_name": "gpt-4",
                "temperature": 0.7,
                "max_tokens": 2048,
                "timeout_seconds": 300,
                "retry_attempts": 3,
                "memory_enabled": True,
                "tools_enabled": True,
                "custom_settings": {}
            },
            "capabilities": [],
            "tags": [],
            "is_public": False
        }

        create_response = client.post(
            "/agents/",
            json=create_data,
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        assert create_response.status_code == 403
        assert "Insufficient permissions" in create_response.json()["detail"]

        # Test other operations that should fail for viewer
        agent_id = uuid.uuid4()

        # Update agent
        update_response = client.put(
            f"/agents/{agent_id}",
            json={"name": "Updated Name"},
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )
        assert update_response.status_code == 403

        # Delete agent
        delete_response = client.delete(
            f"/agents/{agent_id}",
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )
        assert delete_response.status_code == 403

        # Activate agent
        activate_response = client.post(
            f"/agents/{agent_id}/activate",
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )
        assert activate_response.status_code == 403
