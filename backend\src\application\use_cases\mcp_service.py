"""
Model Context Protocol (MCP) service.

This module contains use cases for MCP operations including
context management and model interactions.
"""

import asyncio
import time
import uuid
from datetime import UTC, datetime, timedelta

import httpx

from src.domain.entities.mcp import (
    MCPContext,
    MCPContextCreate,
    MCPContextResponse,
    MCPContextUpdate,
    MCPMessage,
    MCPModel,
    MCPModelResponse,
    MCPRequest,
    MCPResponse,
    ModelStatus,
    ModelType,
)
from src.infrastructure.logging.setup import LoggerMixin


class MCPService(LoggerMixin):
    """
    MCP service containing business logic for model context protocol operations.

    Orchestrates MCP-related use cases and manages model interactions
    and context sessions.
    """

    def __init__(self) -> None:
        """Initialize MCP service."""
        # In a real implementation, these would be injected dependencies
        self._models: dict[str, MCPModel] = {}
        self._contexts: dict[uuid.UUID, MCPContext] = {}
        self._messages: dict[uuid.UUID, list[MCPMessage]] = {}

        # Initialize with some sample models
        self._initialize_sample_models()

    def _initialize_sample_models(self) -> None:
        """Initialize sample models for demonstration."""
        sample_models = [
            MCPModel(
                id="gpt-4",
                name="GPT-4",
                type=ModelType.TEXT_GENERATION,
                provider="OpenAI",
                version="gpt-4-0613",
                status=ModelStatus.AVAILABLE,
                max_context_length=8192,
                capabilities=["text_generation", "code_generation", "chat_completion"],
                metadata={"cost_per_token": 0.00003},
            ),
            MCPModel(
                id="claude-3",
                name="Claude 3",
                type=ModelType.TEXT_GENERATION,
                provider="Anthropic",
                version="claude-3-sonnet-20240229",
                status=ModelStatus.AVAILABLE,
                max_context_length=200000,
                capabilities=["text_generation", "code_generation", "chat_completion"],
                metadata={"cost_per_token": 0.000015},
            ),
            MCPModel(
                id="codellama",
                name="Code Llama",
                type=ModelType.CODE_GENERATION,
                provider="Meta",
                version="codellama-34b",
                status=ModelStatus.AVAILABLE,
                max_context_length=4096,
                capabilities=["code_generation", "code_completion"],
                metadata={"specialized": "code"},
            ),
        ]

        for model in sample_models:
            self._models[model.id] = model

    async def list_models(self) -> list[MCPModelResponse]:
        """
        List available models.

        Returns:
            List[MCPModelResponse]: List of available models
        """
        models = [
            MCPModelResponse.model_validate(model) for model in self._models.values()
        ]

        self.logger.debug(f"Listed {len(models)} available models")
        return models

    async def get_model(self, model_id: str) -> MCPModelResponse | None:
        """
        Get model by ID.

        Args:
            model_id: Model identifier

        Returns:
            MCPModelResponse: Model information or None if not found
        """
        model = self._models.get(model_id)
        if model:
            self.logger.debug(f"Retrieved model: {model_id}")
            return MCPModelResponse.model_validate(model)

        self.logger.warning(f"Model not found: {model_id}")
        return None

    async def create_context(
        self, user_id: uuid.UUID, context_data: MCPContextCreate
    ) -> MCPContextResponse:
        """
        Create new MCP context.

        Args:
            user_id: User ID creating the context
            context_data: Context creation data

        Returns:
            MCPContextResponse: Created context information

        Raises:
            ValueError: If model not found or invalid data
        """
        # Validate model exists
        if context_data.model_id not in self._models:
            raise ValueError(f"Model not found: {context_data.model_id}")

        model = self._models[context_data.model_id]

        # Calculate expiration time
        expires_at = None
        if context_data.expires_in_hours:
            expires_at = datetime.now(UTC) + timedelta(
                hours=context_data.expires_in_hours
            )

        # Create context
        context = MCPContext(
            user_id=user_id,
            type=context_data.type,
            title=context_data.title,
            description=context_data.description,
            model_id=context_data.model_id,
            max_length=context_data.max_length or model.max_context_length,
            expires_at=expires_at,
            metadata=context_data.metadata,
        )

        # Store context
        self._contexts[context.id] = context
        self._messages[context.id] = []

        self.logger.info(f"Created MCP context: {context.id} for user: {user_id}")

        return MCPContextResponse(
            **context.model_dump(),
            is_expired=context.is_expired(),
            is_full=context.is_full(),
        )

    async def get_context(
        self, context_id: uuid.UUID, user_id: uuid.UUID
    ) -> MCPContextResponse | None:
        """
        Get MCP context by ID.

        Args:
            context_id: Context ID
            user_id: User ID (for authorization)

        Returns:
            MCPContextResponse: Context information or None if not found
        """
        context = self._contexts.get(context_id)

        if not context:
            self.logger.warning(f"Context not found: {context_id}")
            return None

        # Check ownership
        if context.user_id != user_id:
            self.logger.warning(
                f"Unauthorized context access: {context_id} by user: {user_id}"
            )
            return None

        self.logger.debug(f"Retrieved context: {context_id}")

        return MCPContextResponse(
            **context.model_dump(),
            is_expired=context.is_expired(),
            is_full=context.is_full(),
        )

    async def update_context(
        self, context_id: uuid.UUID, user_id: uuid.UUID, update_data: MCPContextUpdate
    ) -> MCPContextResponse | None:
        """
        Update MCP context.

        Args:
            context_id: Context ID
            user_id: User ID (for authorization)
            update_data: Update data

        Returns:
            MCPContextResponse: Updated context information or None if not found
        """
        context = self._contexts.get(context_id)

        if not context:
            return None

        # Check ownership
        if context.user_id != user_id:
            return None

        # Update fields
        if update_data.title:
            context.title = update_data.title

        if update_data.description is not None:
            context.description = update_data.description

        if update_data.metadata:
            context.metadata.update(update_data.metadata)

        context.updated_at = datetime.now(UTC)

        self.logger.info(f"Updated context: {context_id}")

        return MCPContextResponse(
            **context.model_dump(),
            is_expired=context.is_expired(),
            is_full=context.is_full(),
        )

    async def delete_context(self, context_id: uuid.UUID, user_id: uuid.UUID) -> bool:
        """
        Delete MCP context.

        Args:
            context_id: Context ID
            user_id: User ID (for authorization)

        Returns:
            bool: True if deleted, False if not found
        """
        context = self._contexts.get(context_id)

        if not context:
            return False

        # Check ownership
        if context.user_id != user_id:
            return False

        # Delete context and messages
        del self._contexts[context_id]
        if context_id in self._messages:
            del self._messages[context_id]

        self.logger.info(f"Deleted context: {context_id}")
        return True

    async def list_user_contexts(
        self, user_id: uuid.UUID, skip: int = 0, limit: int = 100
    ) -> list[MCPContextResponse]:
        """
        List user's MCP contexts.

        Args:
            user_id: User ID
            skip: Number of contexts to skip
            limit: Maximum number of contexts to return

        Returns:
            List[MCPContextResponse]: List of user's contexts
        """
        user_contexts = [
            context for context in self._contexts.values() if context.user_id == user_id
        ]

        # Sort by creation date (newest first)
        user_contexts.sort(key=lambda x: x.created_at, reverse=True)

        # Apply pagination
        paginated_contexts = user_contexts[skip : skip + limit]

        contexts = [
            MCPContextResponse(
                **context.model_dump(),
                is_expired=context.is_expired(),
                is_full=context.is_full(),
            )
            for context in paginated_contexts
        ]

        self.logger.debug(f"Listed {len(contexts)} contexts for user: {user_id}")
        return contexts

    async def process_request(
        self, user_id: uuid.UUID, request: MCPRequest
    ) -> MCPResponse:
        """
        Process MCP request.

        Args:
            user_id: User ID making the request
            request: MCP request data

        Returns:
            MCPResponse: Generated response

        Raises:
            ValueError: If request is invalid
        """
        # Validate model exists
        if request.model_id not in self._models:
            raise ValueError(f"Model not found: {request.model_id}")

        model = self._models[request.model_id]

        # Check model status
        if model.status != ModelStatus.AVAILABLE:
            raise ValueError(
                f"Model not available: {request.model_id} (status: {model.status})"
            )

        # Get or create context
        context = None
        if request.context_id:
            context = self._contexts.get(request.context_id)
            if not context or context.user_id != user_id:
                raise ValueError("Invalid context ID")

            if context.is_expired():
                raise ValueError("Context has expired")

        # Implement actual model inference
        start_time = time.time()

        try:
            response_content, usage_stats = await self._perform_model_inference(
                model, request.messages, request.max_tokens, request.temperature
            )
        except Exception as e:
            self.logger.error(f"Model inference failed for {model.id}: {e}")
            raise ValueError(f"Model inference failed: {e!s}")

        inference_time = time.time() - start_time
        self.logger.info(f"Model inference completed in {inference_time:.2f}s")

        # Use actual token usage from inference
        prompt_tokens = usage_stats.get("prompt_tokens", 0)
        completion_tokens = usage_stats.get("completion_tokens", 0)
        total_tokens = prompt_tokens + completion_tokens

        # Create response
        response = MCPResponse(
            id=str(uuid.uuid4()),
            context_id=context.id if context else uuid.uuid4(),
            model_id=request.model_id,
            content=response_content,
            usage={
                "prompt_tokens": prompt_tokens,
                "completion_tokens": completion_tokens,
                "total_tokens": total_tokens,
            },
            metadata=request.metadata,
        )

        # Update context if provided
        if context:
            context.add_content_length(total_tokens)

            # Add messages to context
            for msg in request.messages:
                message = MCPMessage(
                    context_id=context.id,
                    role=msg.get("role", "user"),
                    content=msg.get("content", ""),
                    content_length=len(msg.get("content", "").split()),
                )
                self._messages[context.id].append(message)

            # Add response message
            response_message = MCPMessage(
                context_id=context.id,
                role="assistant",
                content=response_content,
                content_length=completion_tokens,
            )
            self._messages[context.id].append(response_message)

        self.logger.info(
            f"Processed MCP request for user: {user_id}, model: {request.model_id}"
        )
        return response

    async def _perform_model_inference(
        self,
        model: MCPModel,
        messages: list[dict[str, str]],
        max_tokens: int | None = None,
        temperature: float | None = None,
    ) -> tuple[str, dict[str, int]]:
        """
        Perform actual model inference.

        This method integrates with local models (Ollama/HuggingFace) or external APIs
        to generate responses based on the model type and provider.

        Args:
            model: The model to use for inference
            messages: List of messages to process
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature

        Returns:
            Tuple of (response_content, usage_stats)

        Raises:
            Exception: If inference fails
        """
        # Set default parameters
        max_tokens = max_tokens or 1000
        temperature = temperature or 0.7

        # Route to appropriate inference method based on provider
        if model.provider.lower() == "ollama":
            return await self._ollama_inference(
                model, messages, max_tokens, temperature
            )
        elif model.provider.lower() in ["openai", "anthropic"]:
            return await self._external_api_inference(
                model, messages, max_tokens, temperature
            )
        elif model.provider.lower() == "huggingface":
            return await self._huggingface_inference(
                model, messages, max_tokens, temperature
            )
        else:
            # Fallback to mock response for unsupported providers
            return await self._mock_inference(model, messages, max_tokens, temperature)

    async def _ollama_inference(
        self,
        model: MCPModel,
        messages: list[dict[str, str]],
        max_tokens: int,
        temperature: float,
    ) -> tuple[str, dict[str, int]]:
        """Perform inference using Ollama local models."""
        try:
            # Ollama API endpoint (default local installation)
            ollama_url = "http://localhost:11434/api/chat"

            # Format messages for Ollama
            formatted_messages = [
                {"role": msg.get("role", "user"), "content": msg.get("content", "")}
                for msg in messages
            ]

            payload = {
                "model": model.id,
                "messages": formatted_messages,
                "options": {
                    "num_predict": max_tokens,
                    "temperature": temperature,
                },
                "stream": False,
            }

            timeout = 30.0  # 30 second timeout

            async with httpx.AsyncClient(timeout=timeout) as client:
                response = await client.post(ollama_url, json=payload)
                if response.status_code != 200:
                    raise Exception(f"Ollama API error: {response.status_code}")

                result = response.json()

                content = result.get("message", {}).get("content", "")

                # Calculate token usage (approximate)
                prompt_tokens = sum(
                    len(msg.get("content", "").split()) for msg in messages
                )
                completion_tokens = len(content.split())

                usage_stats = {
                    "prompt_tokens": prompt_tokens,
                    "completion_tokens": completion_tokens,
                    "total_tokens": prompt_tokens + completion_tokens,
                }

                return content, usage_stats

        except Exception as e:
            self.logger.error(f"Ollama inference failed: {e}")
            # Fallback to mock response
            return await self._mock_inference(model, messages, max_tokens, temperature)

    async def _external_api_inference(
        self,
        model: MCPModel,
        messages: list[dict[str, str]],
        max_tokens: int,
        temperature: float,
    ) -> tuple[str, dict[str, int]]:
        """Perform inference using external APIs (OpenAI, Anthropic)."""
        # For now, return mock response as we don't have API keys configured
        # In production, this would integrate with actual APIs
        self.logger.warning(
            f"External API inference not configured for {model.provider}"
        )
        return await self._mock_inference(model, messages, max_tokens, temperature)

    async def _huggingface_inference(
        self,
        model: MCPModel,
        messages: list[dict[str, str]],
        max_tokens: int,
        temperature: float,
    ) -> tuple[str, dict[str, int]]:
        """Perform inference using HuggingFace models."""
        try:
            # HuggingFace Inference API endpoint
            hf_url = f"https://api-inference.huggingface.co/models/{model.id}"

            # Format input for HuggingFace
            input_text = "\n".join([
                f"{msg.get('role', 'user')}: {msg.get('content', '')}"
                for msg in messages
            ])

            payload = {
                "inputs": input_text,
                "parameters": {
                    "max_new_tokens": max_tokens,
                    "temperature": temperature,
                    "return_full_text": False,
                },
            }

            # Note: In production, you would need HuggingFace API token
            headers = {
                "Authorization": "Bearer YOUR_HF_TOKEN",  # Replace with actual token
                "Content-Type": "application/json",
            }

            timeout = 30.0

            async with httpx.AsyncClient(timeout=timeout) as client:
                response = await client.post(hf_url, json=payload, headers=headers)
                if response.status_code != 200:
                    raise Exception(f"HuggingFace API error: {response.status_code}")

                result = response.json()

                if isinstance(result, list) and len(result) > 0:
                    content = result[0].get("generated_text", "")
                else:
                    content = str(result)

                # Calculate token usage (approximate)
                prompt_tokens = sum(
                    len(msg.get("content", "").split()) for msg in messages
                )
                completion_tokens = len(content.split())

                usage_stats = {
                    "prompt_tokens": prompt_tokens,
                    "completion_tokens": completion_tokens,
                    "total_tokens": prompt_tokens + completion_tokens,
                }

                return content, usage_stats

        except Exception as e:
            self.logger.error(f"HuggingFace inference failed: {e}")
            # Fallback to mock response
            return await self._mock_inference(model, messages, max_tokens, temperature)

    async def _mock_inference(
        self,
        model: MCPModel,
        messages: list[dict[str, str]],
        max_tokens: int,
        temperature: float,
    ) -> tuple[str, dict[str, int]]:
        """Fallback mock inference for testing and unsupported providers."""
        # Simulate processing time
        await asyncio.sleep(0.1)

        # Generate mock response based on model type
        if model.type == ModelType.CODE_GENERATION:
            response_content = f"""
# Generated by {model.name}
def example_function():
    '''
    This is a mock code generation response.
    Temperature: {temperature}, Max tokens: {max_tokens}
    '''
    return "Hello from {model.name}!"
"""
        else:
            response_content = f"""
This is a mock response from {model.name}.

I've processed {len(messages)} message(s) with the following parameters:
- Max tokens: {max_tokens}
- Temperature: {temperature}

In a real implementation, this would be the actual AI model response.
"""

        # Calculate mock token usage
        prompt_tokens = sum(len(msg.get("content", "").split()) for msg in messages)
        completion_tokens = len(response_content.split())

        usage_stats = {
            "prompt_tokens": prompt_tokens,
            "completion_tokens": completion_tokens,
            "total_tokens": prompt_tokens + completion_tokens,
        }

        return response_content.strip(), usage_stats
