'use client';

import React, { useEffect, useRef, useState, useCallback } from 'react';
import * as d3 from 'd3';
import { motion, AnimatePresence } from 'framer-motion';
import { ZoomIn, ZoomOut, RotateCcw, Maximize2, Search, Filter, Settings } from 'lucide-react';
import { KnowledgeEntity, KnowledgeRelationship, KnowledgeGraph, EntityType, RelationType } from '@/shared/types';
import { Button } from '@/shared/ui/button';
import { Input } from '@/shared/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/ui/card';
import { Badge } from '@/shared/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/shared/ui/select';
import { Slider } from '@/shared/ui/slider';
import { Switch } from '@/shared/ui/switch';
import { Label } from '@/shared/ui/label';
import { Separator } from '@/shared/ui/separator';
import { useToast } from '@/shared/hooks/use-toast';

interface GraphNode extends d3.SimulationNodeDatum {
  id: string;
  entity: KnowledgeEntity;
  x?: number;
  y?: number;
  fx?: number | null;
  fy?: number | null;
}

interface GraphLink extends d3.SimulationLinkDatum<GraphNode> {
  id: string;
  relationship: KnowledgeRelationship;
  source: GraphNode;
  target: GraphNode;
}

interface GraphVisualizationProps {
  graph: KnowledgeGraph;
  onEntitySelect?: (entity: KnowledgeEntity) => void;
  onRelationshipSelect?: (relationship: KnowledgeRelationship) => void;
  className?: string;
}

export function GraphVisualization({ 
  graph, 
  onEntitySelect, 
  onRelationshipSelect, 
  className 
}: GraphVisualizationProps) {
  const svgRef = useRef<SVGSVGElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [selectedEntity, setSelectedEntity] = useState<KnowledgeEntity | null>(null);
  const [selectedRelationship, setSelectedRelationship] = useState<KnowledgeRelationship | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [entityTypeFilter, setEntityTypeFilter] = useState<EntityType | 'all'>('all');
  const [relationTypeFilter, setRelationTypeFilter] = useState<RelationType | 'all'>('all');
  const [linkDistance, setLinkDistance] = useState([100]);
  const [chargeStrength, setChargeStrength] = useState([-300]);
  const [showLabels, setShowLabels] = useState(true);
  const [showRelationshipLabels, setShowRelationshipLabels] = useState(false);
  const { toast } = useToast();

  // D3 simulation and zoom
  const simulationRef = useRef<d3.Simulation<GraphNode, GraphLink> | null>(null);
  const zoomRef = useRef<d3.ZoomBehavior<SVGSVGElement, unknown> | null>(null);

  // Entity type colors
  const getEntityColor = (entityType: EntityType): string => {
    const colors: Record<EntityType, string> = {
      [EntityType.PERSON]: '#3b82f6',
      [EntityType.ORGANIZATION]: '#10b981',
      [EntityType.LOCATION]: '#f59e0b',
      [EntityType.CONCEPT]: '#8b5cf6',
      [EntityType.DOCUMENT]: '#ef4444',
      [EntityType.EVENT]: '#06b6d4',
      [EntityType.PRODUCT]: '#84cc16',
      [EntityType.TECHNOLOGY]: '#f97316',
      [EntityType.WORKFLOW]: '#ec4899',
      [EntityType.AGENT]: '#6366f1',
      [EntityType.CUSTOM]: '#6b7280',
    };
    return colors[entityType] || '#6b7280';
  };

  // Relationship type colors
  const getRelationshipColor = (relationType: RelationType): string => {
    const colors: Record<RelationType, string> = {
      [RelationType.RELATED_TO]: '#6b7280',
      [RelationType.PART_OF]: '#3b82f6',
      [RelationType.CONTAINS]: '#10b981',
      [RelationType.DEPENDS_ON]: '#f59e0b',
      [RelationType.WORKS_FOR]: '#8b5cf6',
      [RelationType.KNOWS]: '#ef4444',
      [RelationType.CREATED_BY]: '#06b6d4',
      [RelationType.OWNS]: '#84cc16',
      [RelationType.MANAGES]: '#f97316',
      [RelationType.COLLABORATES_WITH]: '#ec4899',
      [RelationType.PRECEDES]: '#6366f1',
      [RelationType.FOLLOWS]: '#6366f1',
      [RelationType.OCCURS_DURING]: '#06b6d4',
      [RelationType.IS_A]: '#8b5cf6',
      [RelationType.INSTANCE_OF]: '#8b5cf6',
      [RelationType.SIMILAR_TO]: '#10b981',
      [RelationType.OPPOSITE_OF]: '#ef4444',
      [RelationType.TRIGGERS]: '#f59e0b',
      [RelationType.USES]: '#3b82f6',
      [RelationType.PRODUCES]: '#84cc16',
      [RelationType.CUSTOM]: '#6b7280',
    };
    return colors[relationType] || '#6b7280';
  };

  // Filter entities and relationships
  const filteredEntities = graph.entities.filter(entity => {
    const matchesSearch = entity.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         entity.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         entity.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    const matchesType = entityTypeFilter === 'all' || entity.entity_type === entityTypeFilter;
    return matchesSearch && matchesType;
  });

  const filteredRelationships = graph.relationships.filter(relationship => {
    const matchesType = relationTypeFilter === 'all' || relationship.relation_type === relationTypeFilter;
    // Only include relationships where both entities are in filtered entities
    const sourceExists = filteredEntities.some(e => e.id === relationship.source_entity_id);
    const targetExists = filteredEntities.some(e => e.id === relationship.target_entity_id);
    return matchesType && sourceExists && targetExists;
  });

  // Create graph data
  const nodes: GraphNode[] = filteredEntities.map(entity => ({
    id: entity.id,
    entity,
  }));

  const links: GraphLink[] = filteredRelationships.map(relationship => {
    const source = nodes.find(n => n.id === relationship.source_entity_id)!;
    const target = nodes.find(n => n.id === relationship.target_entity_id)!;
    return {
      id: relationship.id,
      relationship,
      source,
      target,
    };
  });

  // Initialize D3 visualization
  useEffect(() => {
    if (!svgRef.current || !containerRef.current) return;

    const svg = d3.select(svgRef.current);
    const container = d3.select(containerRef.current);
    const width = containerRef.current.clientWidth;
    const height = containerRef.current.clientHeight;

    // Clear previous content
    svg.selectAll('*').remove();

    // Create zoom behavior
    const zoom = d3.zoom<SVGSVGElement, unknown>()
      .scaleExtent([0.1, 10])
      .on('zoom', (event) => {
        g.attr('transform', event.transform);
      });

    zoomRef.current = zoom;
    svg.call(zoom);

    // Create main group
    const g = svg.append('g');

    // Create simulation
    const simulation = d3.forceSimulation<GraphNode>(nodes)
      .force('link', d3.forceLink<GraphNode, GraphLink>(links)
        .id(d => d.id)
        .distance(linkDistance[0])
        .strength(0.1))
      .force('charge', d3.forceManyBody().strength(chargeStrength[0]))
      .force('center', d3.forceCenter(width / 2, height / 2))
      .force('collision', d3.forceCollide().radius(30));

    simulationRef.current = simulation;

    // Create links
    const link = g.append('g')
      .attr('class', 'links')
      .selectAll('line')
      .data(links)
      .enter().append('line')
      .attr('stroke', d => getRelationshipColor(d.relationship.relation_type))
      .attr('stroke-opacity', 0.6)
      .attr('stroke-width', d => Math.sqrt(d.relationship.weight * 3))
      .style('cursor', 'pointer')
      .on('click', (event, d) => {
        event.stopPropagation();
        setSelectedRelationship(d.relationship);
        onRelationshipSelect?.(d.relationship);
      })
      .on('mouseover', function(event, d) {
        d3.select(this).attr('stroke-opacity', 1);
        
        // Show tooltip
        const tooltip = d3.select('body').append('div')
          .attr('class', 'tooltip')
          .style('position', 'absolute')
          .style('background', 'rgba(0, 0, 0, 0.8)')
          .style('color', 'white')
          .style('padding', '8px')
          .style('border-radius', '4px')
          .style('font-size', '12px')
          .style('pointer-events', 'none')
          .style('z-index', '1000')
          .html(`
            <strong>${d.relationship.relation_type}</strong><br/>
            Weight: ${d.relationship.weight}<br/>
            Confidence: ${(d.relationship.confidence_score * 100).toFixed(1)}%
          `)
          .style('left', (event.pageX + 10) + 'px')
          .style('top', (event.pageY - 10) + 'px');

        setTimeout(() => tooltip.remove(), 3000);
      })
      .on('mouseout', function() {
        d3.select(this).attr('stroke-opacity', 0.6);
      });

    // Create relationship labels
    const linkLabels = g.append('g')
      .attr('class', 'link-labels')
      .selectAll('text')
      .data(links)
      .enter().append('text')
      .attr('class', 'link-label')
      .attr('text-anchor', 'middle')
      .attr('font-size', '10px')
      .attr('fill', '#666')
      .style('display', showRelationshipLabels ? 'block' : 'none')
      .text(d => d.relationship.relation_type);

    // Create nodes
    const node = g.append('g')
      .attr('class', 'nodes')
      .selectAll('circle')
      .data(nodes)
      .enter().append('circle')
      .attr('r', d => Math.sqrt(d.entity.confidence_score * 400) + 10)
      .attr('fill', d => getEntityColor(d.entity.entity_type))
      .attr('stroke', '#fff')
      .attr('stroke-width', 2)
      .style('cursor', 'pointer')
      .call(d3.drag<SVGCircleElement, GraphNode>()
        .on('start', (event, d) => {
          if (!event.active) simulation.alphaTarget(0.3).restart();
          d.fx = d.x;
          d.fy = d.y;
        })
        .on('drag', (event, d) => {
          d.fx = event.x;
          d.fy = event.y;
        })
        .on('end', (event, d) => {
          if (!event.active) simulation.alphaTarget(0);
          d.fx = null;
          d.fy = null;
        }))
      .on('click', (event, d) => {
        event.stopPropagation();
        setSelectedEntity(d.entity);
        onEntitySelect?.(d.entity);
      })
      .on('mouseover', function(event, d) {
        d3.select(this).attr('stroke-width', 4);
      })
      .on('mouseout', function() {
        d3.select(this).attr('stroke-width', 2);
      });

    // Create node labels
    const nodeLabels = g.append('g')
      .attr('class', 'node-labels')
      .selectAll('text')
      .data(nodes)
      .enter().append('text')
      .attr('class', 'node-label')
      .attr('text-anchor', 'middle')
      .attr('dy', '.35em')
      .attr('font-size', '12px')
      .attr('font-weight', 'bold')
      .attr('fill', '#333')
      .style('display', showLabels ? 'block' : 'none')
      .style('pointer-events', 'none')
      .text(d => d.entity.name.length > 15 ? d.entity.name.substring(0, 15) + '...' : d.entity.name);

    // Update positions on simulation tick
    simulation.on('tick', () => {
      link
        .attr('x1', d => (d.source as GraphNode).x!)
        .attr('y1', d => (d.source as GraphNode).y!)
        .attr('x2', d => (d.target as GraphNode).x!)
        .attr('y2', d => (d.target as GraphNode).y!);

      linkLabels
        .attr('x', d => ((d.source as GraphNode).x! + (d.target as GraphNode).x!) / 2)
        .attr('y', d => ((d.source as GraphNode).y! + (d.target as GraphNode).y!) / 2);

      node
        .attr('cx', d => d.x!)
        .attr('cy', d => d.y!);

      nodeLabels
        .attr('x', d => d.x!)
        .attr('y', d => d.y! + 25);
    });

    // Clear selection on background click
    svg.on('click', () => {
      setSelectedEntity(null);
      setSelectedRelationship(null);
    });

    return () => {
      simulation.stop();
    };
  }, [nodes, links, linkDistance, chargeStrength, showLabels, showRelationshipLabels]);

  // Update simulation forces when parameters change
  useEffect(() => {
    if (simulationRef.current) {
      simulationRef.current
        .force('link', d3.forceLink<GraphNode, GraphLink>(links)
          .id(d => d.id)
          .distance(linkDistance[0])
          .strength(0.1))
        .force('charge', d3.forceManyBody().strength(chargeStrength[0]))
        .alpha(0.3)
        .restart();
    }
  }, [linkDistance, chargeStrength]);

  // Zoom controls
  const handleZoomIn = () => {
    if (svgRef.current && zoomRef.current) {
      d3.select(svgRef.current).transition().call(
        zoomRef.current.scaleBy, 1.5
      );
    }
  };

  const handleZoomOut = () => {
    if (svgRef.current && zoomRef.current) {
      d3.select(svgRef.current).transition().call(
        zoomRef.current.scaleBy, 1 / 1.5
      );
    }
  };

  const handleResetZoom = () => {
    if (svgRef.current && zoomRef.current) {
      d3.select(svgRef.current).transition().call(
        zoomRef.current.transform,
        d3.zoomIdentity
      );
    }
  };

  const handleFitToScreen = () => {
    if (svgRef.current && zoomRef.current && containerRef.current) {
      const svg = d3.select(svgRef.current);
      const bounds = svg.select('g').node()?.getBBox();
      
      if (bounds) {
        const width = containerRef.current.clientWidth;
        const height = containerRef.current.clientHeight;
        const scale = Math.min(width / bounds.width, height / bounds.height) * 0.9;
        const translate = [
          width / 2 - scale * (bounds.x + bounds.width / 2),
          height / 2 - scale * (bounds.y + bounds.height / 2)
        ];

        svg.transition().call(
          zoomRef.current.transform,
          d3.zoomIdentity.translate(translate[0], translate[1]).scale(scale)
        );
      }
    }
  };

  return (
    <div className={`flex h-full ${className}`}>
      {/* Main visualization area */}
      <div className="flex-1 relative">
        <div ref={containerRef} className="w-full h-full">
          <svg
            ref={svgRef}
            width="100%"
            height="100%"
            className="border border-border rounded-lg bg-background"
          />
        </div>

        {/* Zoom controls */}
        <div className="absolute top-4 right-4 flex flex-col gap-2">
          <Button variant="outline" size="sm" onClick={handleZoomIn}>
            <ZoomIn className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="sm" onClick={handleZoomOut}>
            <ZoomOut className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="sm" onClick={handleResetZoom}>
            <RotateCcw className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="sm" onClick={handleFitToScreen}>
            <Maximize2 className="h-4 w-4" />
          </Button>
        </div>

        {/* Graph stats */}
        <div className="absolute bottom-4 left-4">
          <Card className="w-48">
            <CardContent className="p-3">
              <div className="text-sm space-y-1">
                <div>Entities: {filteredEntities.length}</div>
                <div>Relationships: {filteredRelationships.length}</div>
                <div>Total: {graph.entities.length} entities, {graph.relationships.length} relationships</div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Control panel */}
      <div className="w-80 border-l border-border bg-muted/50 p-4 space-y-6 overflow-y-auto">
        <div>
          <h3 className="text-lg font-semibold mb-4">Graph Controls</h3>
          
          {/* Search */}
          <div className="space-y-2">
            <Label>Search</Label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search entities..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          <Separator className="my-4" />

          {/* Filters */}
          <div className="space-y-4">
            <div>
              <Label>Entity Type</Label>
              <Select value={entityTypeFilter} onValueChange={(value) => setEntityTypeFilter(value as EntityType | 'all')}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  {Object.values(EntityType).map(type => (
                    <SelectItem key={type} value={type}>
                      {type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Relationship Type</Label>
              <Select value={relationTypeFilter} onValueChange={(value) => setRelationTypeFilter(value as RelationType | 'all')}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  {Object.values(RelationType).map(type => (
                    <SelectItem key={type} value={type}>
                      {type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <Separator className="my-4" />

          {/* Layout controls */}
          <div className="space-y-4">
            <div>
              <Label>Link Distance: {linkDistance[0]}</Label>
              <Slider
                value={linkDistance}
                onValueChange={setLinkDistance}
                min={50}
                max={300}
                step={10}
                className="mt-2"
              />
            </div>

            <div>
              <Label>Charge Strength: {Math.abs(chargeStrength[0])}</Label>
              <Slider
                value={[Math.abs(chargeStrength[0])]}
                onValueChange={(value) => setChargeStrength([-value[0]])}
                min={100}
                max={1000}
                step={50}
                className="mt-2"
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="show-labels"
                checked={showLabels}
                onCheckedChange={setShowLabels}
              />
              <Label htmlFor="show-labels">Show Entity Labels</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="show-relationship-labels"
                checked={showRelationshipLabels}
                onCheckedChange={setShowRelationshipLabels}
              />
              <Label htmlFor="show-relationship-labels">Show Relationship Labels</Label>
            </div>
          </div>
        </div>

        {/* Selected entity/relationship info */}
        <AnimatePresence>
          {(selectedEntity || selectedRelationship) && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">
                    {selectedEntity ? 'Selected Entity' : 'Selected Relationship'}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {selectedEntity && (
                    <>
                      <div>
                        <h4 className="font-medium">{selectedEntity.name}</h4>
                        <Badge variant="outline" className="mt-1">
                          {selectedEntity.entity_type}
                        </Badge>
                      </div>
                      {selectedEntity.description && (
                        <p className="text-sm text-muted-foreground">
                          {selectedEntity.description}
                        </p>
                      )}
                      <div className="text-sm">
                        <div>Confidence: {(selectedEntity.confidence_score * 100).toFixed(1)}%</div>
                        <div>Tags: {selectedEntity.tags.length}</div>
                      </div>
                    </>
                  )}
                  
                  {selectedRelationship && (
                    <>
                      <div>
                        <h4 className="font-medium">{selectedRelationship.relation_type}</h4>
                        <Badge variant="outline" className="mt-1">
                          Weight: {selectedRelationship.weight}
                        </Badge>
                      </div>
                      {selectedRelationship.description && (
                        <p className="text-sm text-muted-foreground">
                          {selectedRelationship.description}
                        </p>
                      )}
                      <div className="text-sm">
                        <div>Confidence: {(selectedRelationship.confidence_score * 100).toFixed(1)}%</div>
                        <div>Directed: {selectedRelationship.is_directed ? 'Yes' : 'No'}</div>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}
