/**
 * Shared TypeScript types for the Lonors AI Agent Platform
 */

// Base entity interface
export interface BaseEntity {
  id: string;
  createdAt: string;
  updatedAt: string;
}

// User types
export interface User extends BaseEntity {
  email: string;
  username: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
  role: UserRole;
  isActive: boolean;
  lastLoginAt?: string;
}

export enum UserRole {
  ADMIN = 'admin',
  USER = 'user',
  AGENT = 'agent',
}

// Authentication types
export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  username: string;
  password: string;
  firstName?: string;
  lastName?: string;
}

// AI Agent types - Aligned with backend domain entities
export interface Agent extends BaseEntity {
  name: string;
  description: string;
  agent_type: AgentType;
  version: string;
  status: AgentStatus;
  configuration: AgentConfiguration;
  capabilities: AgentCapability[];
  current_task_id?: string;
  created_by: string;
  metrics: AgentMetrics;
  parent_agent_id?: string;
  child_agent_ids: string[];
  tags: string[];
  metadata: Record<string, any>;
}

// Knowledge Graph types
export enum EntityType {
  PERSON = 'person',
  ORGANIZATION = 'organization',
  LOCATION = 'location',
  CONCEPT = 'concept',
  DOCUMENT = 'document',
  EVENT = 'event',
  PRODUCT = 'product',
  TECHNOLOGY = 'technology',
  WORKFLOW = 'workflow',
  AGENT = 'agent',
  CUSTOM = 'custom',
}

export enum RelationType {
  RELATED_TO = 'related_to',
  PART_OF = 'part_of',
  CONTAINS = 'contains',
  DEPENDS_ON = 'depends_on',
  WORKS_FOR = 'works_for',
  KNOWS = 'knows',
  CREATED_BY = 'created_by',
  OWNS = 'owns',
  MANAGES = 'manages',
  COLLABORATES_WITH = 'collaborates_with',
  PRECEDES = 'precedes',
  FOLLOWS = 'follows',
  OCCURS_DURING = 'occurs_during',
  IS_A = 'is_a',
  INSTANCE_OF = 'instance_of',
  SIMILAR_TO = 'similar_to',
  OPPOSITE_OF = 'opposite_of',
  TRIGGERS = 'triggers',
  USES = 'uses',
  PRODUCES = 'produces',
  CUSTOM = 'custom',
}

export interface KnowledgeEntity extends BaseEntity {
  name: string;
  description?: string;
  entity_type: EntityType;
  properties: Record<string, any>;
  attributes: Record<string, any>;
  tags: string[];
  categories: string[];
  confidence_score: number;
  source_id?: string;
  source_type?: string;
  source_metadata: Record<string, any>;
  created_by: string;
  is_public: boolean;
}

export interface KnowledgeRelationship extends BaseEntity {
  source_entity_id: string;
  target_entity_id: string;
  relation_type: RelationType;
  properties: Record<string, any>;
  weight: number;
  confidence_score: number;
  is_directed: boolean;
  description?: string;
  tags: string[];
  source_id?: string;
  source_type?: string;
  source_metadata: Record<string, any>;
  created_by: string;
}

export interface KnowledgeGraph extends BaseEntity {
  name: string;
  description?: string;
  entities: KnowledgeEntity[];
  relationships: KnowledgeRelationship[];
  tags: string[];
  categories: string[];
  created_by: string;
  is_public: boolean;
}

export enum AgentType {
  CONVERSATIONAL = 'conversational',
  TASK_EXECUTOR = 'task_executor',
  KNOWLEDGE_WORKER = 'knowledge_worker',
  CODE_ASSISTANT = 'code_assistant',
  DATA_ANALYST = 'data_analyst',
  CREATIVE_WRITER = 'creative_writer',
  RESEARCH_ASSISTANT = 'research_assistant',
  CUSTOM = 'custom',
}

export enum AgentStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  TRAINING = 'training',
  ERROR = 'error',
  MAINTENANCE = 'maintenance',
}

export interface AgentCapability {
  name: string;
  description: string;
  parameters: Record<string, any>;
  required: boolean;
}

export interface AgentMetrics {
  total_executions: number;
  successful_executions: number;
  failed_executions: number;
  average_execution_time: number;
  last_execution_time?: string;
  total_tokens_used: number;
  total_cost: number;
}

export interface AgentConfiguration {
  model_name: string;
  temperature: number;
  max_tokens: number;
  timeout_seconds: number;
  retry_attempts: number;
  memory_enabled: boolean;
  tools_enabled: boolean;
  custom_settings: Record<string, any>;
}

// Workflow types
export interface Workflow extends BaseEntity {
  name: string;
  description: string;
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
  status: WorkflowStatus;
  ownerId: string;
  isPublic: boolean;
  tags: string[];
}

export interface WorkflowNode {
  id: string;
  type: NodeType;
  position: { x: number; y: number };
  data: NodeData;
}

export interface WorkflowEdge {
  id: string;
  source: string;
  target: string;
  sourceHandle?: string;
  targetHandle?: string;
}

export enum NodeType {
  INPUT = 'input',
  OUTPUT = 'output',
  AGENT = 'agent',
  CONDITION = 'condition',
  TRANSFORM = 'transform',
  API_CALL = 'api_call',
  DELAY = 'delay',
}

export interface NodeData {
  label: string;
  description?: string;
  configuration: Record<string, any>;
}

export enum WorkflowStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  PAUSED = 'paused',
  ARCHIVED = 'archived',
}

// Knowledge Graph types
export interface KnowledgeEntity extends BaseEntity {
  name: string;
  type: string;
  properties: Record<string, any>;
  description?: string;
}

export interface KnowledgeRelationship extends BaseEntity {
  sourceId: string;
  targetId: string;
  type: string;
  properties: Record<string, any>;
  weight?: number;
}

export interface KnowledgeGraph {
  entities: KnowledgeEntity[];
  relationships: KnowledgeRelationship[];
}

// UI Component types
export interface ComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export interface ButtonProps extends ComponentProps {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
}

export interface InputProps extends ComponentProps {
  type?: string;
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  disabled?: boolean;
  error?: string;
}

// Animation types
export interface AnimationConfig {
  duration?: number;
  delay?: number;
  easing?: string;
  direction?: 'normal' | 'reverse' | 'alternate' | 'alternate-reverse';
  loop?: boolean | number;
}

// Theme types
export interface Theme {
  colors: {
    primary: string;
    secondary: string;
    background: string;
    foreground: string;
    muted: string;
    accent: string;
    destructive: string;
    border: string;
    input: string;
    ring: string;
  };
  fonts: {
    sans: string;
    mono: string;
  };
  spacing: Record<string, string>;
  borderRadius: Record<string, string>;
}

// Error types
export interface AppError {
  message: string;
  code?: string;
  status?: number;
  details?: Record<string, any>;
}

// Loading states
export interface LoadingState {
  isLoading: boolean;
  error?: AppError | null;
}

// Pagination types
export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  search?: string;
}

export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

// WebSocket types
export interface WebSocketMessage {
  type: string;
  payload: any;
  timestamp: string;
  id?: string;
}

export interface WebSocketConnection {
  url: string;
  protocols?: string[];
  reconnect?: boolean;
  maxReconnectAttempts?: number;
  reconnectInterval?: number;
}

// File upload types
export interface FileUpload {
  file: File;
  progress: number;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  error?: string;
}

// Search types
export interface SearchResult<T = any> {
  items: T[];
  total: number;
  query: string;
  filters?: Record<string, any>;
  facets?: Record<string, any>;
}

// Notification types
export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  duration?: number;
  actions?: NotificationAction[];
}

export interface NotificationAction {
  label: string;
  action: () => void;
}

// Feature flags
export interface FeatureFlags {
  [key: string]: boolean;
}

// Environment configuration
export interface AppConfig {
  apiUrl: string;
  appName: string;
  appVersion: string;
  environment: 'development' | 'staging' | 'production';
  features: FeatureFlags;
}

// Model Management types
export enum ModelType {
  LANGUAGE_MODEL = 'language_model',
  EMBEDDING_MODEL = 'embedding_model',
  VISION_MODEL = 'vision_model',
  AUDIO_MODEL = 'audio_model',
  MULTIMODAL_MODEL = 'multimodal_model',
  CODE_MODEL = 'code_model',
  CUSTOM = 'custom',
}

export enum ModelStatus {
  AVAILABLE = 'available',
  DOWNLOADING = 'downloading',
  DOWNLOADED = 'downloaded',
  LOADING = 'loading',
  LOADED = 'loaded',
  SERVING = 'serving',
  ERROR = 'error',
  STOPPED = 'stopped',
}

export enum ModelProvider {
  HUGGINGFACE = 'huggingface',
  OLLAMA = 'ollama',
  OPENAI = 'openai',
  ANTHROPIC = 'anthropic',
  GOOGLE = 'google',
  CUSTOM = 'custom',
}

export interface ModelInfo extends BaseEntity {
  name: string;
  display_name: string;
  description?: string;
  model_type: ModelType;
  provider: ModelProvider;
  version: string;
  size_bytes?: number;
  size_display: string;
  parameter_count?: number;
  parameter_display: string;
  context_length?: number;
  min_ram_gb?: number;
  min_vram_gb?: number;
  gpu_required: boolean;
  capabilities: string[];
  supported_formats: string[];
  languages: string[];
  tags: string[];
  license?: string;
  homepage_url?: string;
  repository_url?: string;
  paper_url?: string;
  provider_metadata: Record<string, any>;
}

export interface ModelInstance extends BaseEntity {
  model_id: string;
  status: ModelStatus;
  download_url?: string;
  local_path?: string;
  download_progress: number;
  download_speed?: number;
  serving_port?: number;
  serving_url?: string;
  api_endpoint?: string;
  load_time_seconds?: number;
  memory_usage_mb?: number;
  gpu_memory_usage_mb?: number;
  configuration: Record<string, any>;
  error_message?: string;
  error_details: Record<string, any>;
  created_by: string;
  last_used_at?: string;
}

export interface ModelDownloadTask extends BaseEntity {
  instance_id: string;
  model_id: string;
  status: string;
  progress: number;
  download_url: string;
  destination_path: string;
  total_size?: number;
  downloaded_size: number;
  download_speed?: number;
  started_at?: string;
  completed_at?: string;
  estimated_completion?: string;
  error_message?: string;
  retry_count: number;
  max_retries: number;
  created_by: string;
}

export interface ModelHealth {
  status: string;
  message?: string;
  response_time_ms?: string;
  memory_usage_mb?: number;
  gpu_memory_usage_mb?: number;
  uptime_seconds?: number;
  requests_served?: number;
  errors_count?: number;
}
