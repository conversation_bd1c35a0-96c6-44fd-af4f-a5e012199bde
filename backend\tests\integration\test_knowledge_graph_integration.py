"""
Knowledge Graph Integration Tests

Integration tests for the complete knowledge graph system including
API endpoints, service layer, and database operations.
"""

import uuid
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from fastapi import FastAPI
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession

from src.domain.entities.knowledge_graph import EntityType, RelationType
from src.domain.entities.user import Permission, User, UserRole, UserStatus
from src.domain.value_objects.email import Email
from src.domain.value_objects.username import Username
from src.presentation.api.routes.knowledge_graph import router


@pytest.fixture
def app():
    """Create FastAPI app with knowledge graph router."""
    app = FastAPI()
    app.include_router(router)
    return app


@pytest.fixture
def client(app):
    """Create test client."""
    return TestClient(app)


@pytest.fixture
def sample_user_id():
    """Sample user ID for testing."""
    return uuid.uuid4()


@pytest.fixture
def admin_user(sample_user_id):
    """Create admin user entity."""
    return User(
        id=str(sample_user_id),
        email=Email("<EMAIL>"),
        username=Username("admin"),
        full_name="Admin User",
        hashed_password="hashed_password",
        role=UserRole.ADMIN,
        status=UserStatus.ACTIVE,
        is_active=True,
        is_verified=True,
    )


@pytest.fixture
def mock_jwt_token():
    """Mock JWT token for testing."""
    return "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.test.token"


class TestKnowledgeGraphLifecycle:
    """Test complete knowledge graph lifecycle."""

    @patch("src.presentation.api.routes.knowledge_graph.get_knowledge_graph_service")
    @patch("src.infrastructure.database.repositories.user_repository.UserRepository")
    @patch("src.infrastructure.security.jwt.jwt_manager.verify_token")
    @patch("src.infrastructure.database.connection.get_db_session")
    def test_complete_knowledge_graph_lifecycle(
        self, mock_get_db, mock_verify_token, mock_user_repo_class, mock_get_service,
        client, admin_user, sample_user_id, mock_jwt_token
    ):
        """Test complete knowledge graph lifecycle: create entities, relationships, query graph."""
        entity_id_1 = uuid.uuid4()
        entity_id_2 = uuid.uuid4()
        relationship_id = uuid.uuid4()
        
        # Setup authentication mocks
        mock_verify_token.return_value = {
            "user_id": str(sample_user_id),
            "sub": "<EMAIL>",
            "jti": "token-id"
        }
        
        mock_db = AsyncMock(spec=AsyncSession)
        mock_get_db.return_value.__aenter__.return_value = mock_db
        
        mock_user_repo = AsyncMock()
        mock_user_repo.get_by_id.return_value = admin_user
        mock_user_repo_class.return_value = mock_user_repo

        # Mock knowledge graph service
        mock_service = AsyncMock()
        mock_get_service.return_value = mock_service

        # Step 1: Create first entity (AI Concept)
        create_entity_1_data = {
            "name": "Artificial Intelligence",
            "description": "The simulation of human intelligence in machines",
            "entity_type": "concept",
            "properties": {
                "field": "computer_science",
                "complexity": "high",
                "applications": ["automation", "decision_making", "pattern_recognition"]
            },
            "attributes": {
                "importance": 9,
                "maturity": "established",
                "growth_rate": "exponential"
            },
            "tags": ["technology", "machine_learning", "automation", "intelligence"],
            "categories": ["computer_science", "technology", "research"],
            "confidence_score": 0.95,
            "source_id": "wikipedia_ai",
            "source_type": "encyclopedia",
            "source_metadata": {
                "url": "https://en.wikipedia.org/wiki/Artificial_intelligence",
                "last_updated": "2024-01-01",
                "reliability": "high"
            },
            "is_public": True
        }

        # Mock created entity 1
        mock_created_entity_1 = MagicMock()
        mock_created_entity_1.id = entity_id_1
        mock_created_entity_1.name = "Artificial Intelligence"
        mock_created_entity_1.entity_type = EntityType.CONCEPT
        mock_created_entity_1.confidence_score = 0.95
        mock_created_entity_1.is_public = True
        mock_created_entity_1.tags = ["technology", "machine_learning", "automation", "intelligence"]
        mock_created_entity_1.created_by = sample_user_id
        mock_service.create_entity.return_value = mock_created_entity_1

        # Create first entity
        create_response_1 = client.post(
            "/knowledge-graph/entities",
            json=create_entity_1_data,
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        assert create_response_1.status_code == 201
        created_data_1 = create_response_1.json()
        assert created_data_1["name"] == "Artificial Intelligence"
        assert created_data_1["entity_type"] == "concept"
        assert created_data_1["confidence_score"] == 0.95
        assert len(created_data_1["tags"]) == 4

        # Step 2: Create second entity (Machine Learning)
        create_entity_2_data = {
            "name": "Machine Learning",
            "description": "A subset of AI that enables computers to learn without explicit programming",
            "entity_type": "concept",
            "properties": {
                "field": "computer_science",
                "complexity": "high",
                "learning_types": ["supervised", "unsupervised", "reinforcement"]
            },
            "attributes": {
                "importance": 8,
                "maturity": "established",
                "growth_rate": "exponential"
            },
            "tags": ["ai", "algorithms", "data_science", "learning"],
            "categories": ["computer_science", "artificial_intelligence"],
            "confidence_score": 0.92,
            "source_id": "ml_textbook",
            "source_type": "academic",
            "source_metadata": {
                "isbn": "978-0262018029",
                "author": "Tom Mitchell",
                "publication_year": 1997
            },
            "is_public": True
        }

        # Mock created entity 2
        mock_created_entity_2 = MagicMock()
        mock_created_entity_2.id = entity_id_2
        mock_created_entity_2.name = "Machine Learning"
        mock_created_entity_2.entity_type = EntityType.CONCEPT
        mock_created_entity_2.confidence_score = 0.92
        mock_created_entity_2.is_public = True
        mock_created_entity_2.tags = ["ai", "algorithms", "data_science", "learning"]
        mock_created_entity_2.created_by = sample_user_id
        mock_service.create_entity.return_value = mock_created_entity_2

        # Create second entity
        create_response_2 = client.post(
            "/knowledge-graph/entities",
            json=create_entity_2_data,
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        assert create_response_2.status_code == 201
        created_data_2 = create_response_2.json()
        assert created_data_2["name"] == "Machine Learning"
        assert created_data_2["entity_type"] == "concept"

        # Step 3: Create relationship between entities
        create_relationship_data = {
            "source_entity_id": str(entity_id_1),
            "target_entity_id": str(entity_id_2),
            "relation_type": "contains",
            "properties": {
                "strength": "strong",
                "context": "hierarchical",
                "bidirectional": False
            },
            "weight": 0.9,
            "confidence_score": 0.88,
            "is_directed": True,
            "description": "AI contains machine learning as a major subfield",
            "tags": ["hierarchy", "containment", "subfield"],
            "source_id": "ai_ml_relationship",
            "source_type": "expert_knowledge",
            "source_metadata": {
                "expert": "AI researcher",
                "confidence_level": "high",
                "validation_date": "2024-01-01"
            }
        }

        # Mock created relationship
        mock_created_relationship = MagicMock()
        mock_created_relationship.id = relationship_id
        mock_created_relationship.source_entity_id = entity_id_1
        mock_created_relationship.target_entity_id = entity_id_2
        mock_created_relationship.relation_type = RelationType.CONTAINS
        mock_created_relationship.weight = 0.9
        mock_created_relationship.confidence_score = 0.88
        mock_created_relationship.is_directed = True
        mock_created_relationship.created_by = sample_user_id
        mock_service.create_relationship.return_value = mock_created_relationship

        # Create relationship
        create_rel_response = client.post(
            "/knowledge-graph/relationships",
            json=create_relationship_data,
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        assert create_rel_response.status_code == 201
        created_rel_data = create_rel_response.json()
        assert created_rel_data["relation_type"] == "contains"
        assert created_rel_data["weight"] == 0.9
        assert created_rel_data["is_directed"] == True

        # Step 4: Get entity by ID
        mock_service.get_entity.return_value = mock_created_entity_1

        get_entity_response = client.get(
            f"/knowledge-graph/entities/{entity_id_1}",
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        assert get_entity_response.status_code == 200
        get_entity_data = get_entity_response.json()
        assert get_entity_data["name"] == "Artificial Intelligence"

        # Step 5: List entities
        mock_service.list_entities.return_value = [mock_created_entity_1, mock_created_entity_2]

        list_entities_response = client.get(
            "/knowledge-graph/entities",
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        assert list_entities_response.status_code == 200
        list_entities_data = list_entities_response.json()
        assert len(list_entities_data) == 2
        assert any(entity["name"] == "Artificial Intelligence" for entity in list_entities_data)
        assert any(entity["name"] == "Machine Learning" for entity in list_entities_data)

        # Step 6: Search entities
        mock_service.search_entities.return_value = [mock_created_entity_1]

        search_response = client.get(
            "/knowledge-graph/entities/search?query=artificial&include_public=true",
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        assert search_response.status_code == 200
        search_data = search_response.json()
        assert len(search_data) == 1
        assert search_data[0]["name"] == "Artificial Intelligence"

        # Step 7: Get entity relationships
        mock_service.get_entity_relationships.return_value = [mock_created_relationship]

        relationships_response = client.get(
            f"/knowledge-graph/entities/{entity_id_1}/relationships",
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        assert relationships_response.status_code == 200
        relationships_data = relationships_response.json()
        assert len(relationships_data) == 1
        assert relationships_data[0]["relation_type"] == "contains"

        # Step 8: Get subgraph
        from src.domain.entities.knowledge_graph import KnowledgeGraph
        
        mock_subgraph = KnowledgeGraph(
            name="Test Subgraph",
            description="Subgraph for testing",
            entities=[mock_created_entity_1.to_domain(), mock_created_entity_2.to_domain()],
            relationships=[mock_created_relationship.to_domain()],
            created_by=sample_user_id,
        )
        mock_service.get_subgraph.return_value = mock_subgraph

        subgraph_response = client.post(
            "/knowledge-graph/subgraph",
            json={"entity_ids": [str(entity_id_1), str(entity_id_2)], "max_depth": 2},
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        assert subgraph_response.status_code == 200
        subgraph_data = subgraph_response.json()
        assert len(subgraph_data["entities"]) == 2
        assert len(subgraph_data["relationships"]) == 1

        # Step 9: Search entities in context
        mock_service.search_entities_in_context.return_value = [mock_created_entity_2]

        context_search_response = client.get(
            f"/knowledge-graph/entities/search-in-context?query=learning&context_entity_ids={entity_id_1}",
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        assert context_search_response.status_code == 200
        context_search_data = context_search_response.json()
        assert len(context_search_data) == 1
        assert context_search_data[0]["name"] == "Machine Learning"

        # Step 10: Find entity paths
        mock_service.find_entity_paths.return_value = [[entity_id_1, entity_id_2]]

        paths_response = client.get(
            f"/knowledge-graph/paths/{entity_id_1}/{entity_id_2}?max_depth=3",
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        assert paths_response.status_code == 200
        paths_data = paths_response.json()
        assert len(paths_data) == 1
        assert len(paths_data[0]) == 2
        assert paths_data[0][0] == str(entity_id_1)
        assert paths_data[0][1] == str(entity_id_2)

        # Step 11: Update entity
        update_entity_data = {
            "description": "Advanced AI technology that simulates human intelligence",
            "confidence_score": 0.97,
            "tags": ["technology", "machine_learning", "automation", "intelligence", "advanced"]
        }

        # Mock updated entity
        mock_updated_entity = MagicMock()
        mock_updated_entity.id = entity_id_1
        mock_updated_entity.name = "Artificial Intelligence"
        mock_updated_entity.description = "Advanced AI technology that simulates human intelligence"
        mock_updated_entity.confidence_score = 0.97
        mock_updated_entity.tags = ["technology", "machine_learning", "automation", "intelligence", "advanced"]
        
        mock_service.get_entity.return_value = mock_created_entity_1
        mock_service.update_entity.return_value = mock_updated_entity

        update_response = client.put(
            f"/knowledge-graph/entities/{entity_id_1}",
            json=update_entity_data,
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        assert update_response.status_code == 200
        updated_data = update_response.json()
        assert updated_data["description"] == "Advanced AI technology that simulates human intelligence"
        assert updated_data["confidence_score"] == 0.97
        assert len(updated_data["tags"]) == 5

        # Step 12: Delete entity
        mock_service.delete_entity.return_value = True

        delete_response = client.delete(
            f"/knowledge-graph/entities/{entity_id_2}",
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        assert delete_response.status_code == 200
        delete_data = delete_response.json()
        assert delete_data["success"] is True

        # Verify all service methods were called
        mock_service.create_entity.assert_called()
        mock_service.get_entity.assert_called()
        mock_service.list_entities.assert_called()
        mock_service.search_entities.assert_called()
        mock_service.create_relationship.assert_called()
        mock_service.get_entity_relationships.assert_called()
        mock_service.get_subgraph.assert_called()
        mock_service.search_entities_in_context.assert_called()
        mock_service.find_entity_paths.assert_called()
        mock_service.update_entity.assert_called()
        mock_service.delete_entity.assert_called()


class TestKnowledgeGraphValidation:
    """Test knowledge graph validation and error handling."""

    @patch("src.presentation.api.routes.knowledge_graph.get_knowledge_graph_service")
    @patch("src.infrastructure.database.repositories.user_repository.UserRepository")
    @patch("src.infrastructure.security.jwt.jwt_manager.verify_token")
    @patch("src.infrastructure.database.connection.get_db_session")
    def test_entity_validation_errors(
        self, mock_get_db, mock_verify_token, mock_user_repo_class, mock_get_service,
        client, admin_user, sample_user_id, mock_jwt_token
    ):
        """Test entity validation and error handling."""
        # Setup mocks
        mock_verify_token.return_value = {
            "user_id": str(sample_user_id),
            "sub": "<EMAIL>",
            "jti": "token-id"
        }
        
        mock_db = AsyncMock(spec=AsyncSession)
        mock_get_db.return_value.__aenter__.return_value = mock_db
        
        mock_user_repo = AsyncMock()
        mock_user_repo.get_by_id.return_value = admin_user
        mock_user_repo_class.return_value = mock_user_repo

        mock_service = AsyncMock()
        mock_get_service.return_value = mock_service

        # Test invalid entity type
        invalid_entity_data = {
            "name": "Test Entity",
            "description": "Test description",
            "entity_type": "invalid_type",
            "confidence_score": 0.8
        }

        response = client.post(
            "/knowledge-graph/entities",
            json=invalid_entity_data,
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        assert response.status_code == 422  # Validation error

        # Test missing required fields
        incomplete_entity_data = {
            "description": "Test description"
            # Missing name and entity_type
        }

        response = client.post(
            "/knowledge-graph/entities",
            json=incomplete_entity_data,
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        assert response.status_code == 422  # Validation error

        # Test invalid confidence score
        invalid_confidence_data = {
            "name": "Test Entity",
            "entity_type": "concept",
            "confidence_score": 1.5  # Should be between 0 and 1
        }

        response = client.post(
            "/knowledge-graph/entities",
            json=invalid_confidence_data,
            headers={"Authorization": f"Bearer {mock_jwt_token}"}
        )

        assert response.status_code == 422  # Validation error
