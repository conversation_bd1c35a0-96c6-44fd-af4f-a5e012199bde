"""
Workflow service.

This module contains use cases for workflow operations including
creation, execution, and management.
"""

import asyncio
import uuid
from datetime import UTC, datetime
from typing import Any

from src.application.use_cases.mcp_service import MCPService
from src.domain.entities.user import Permission, User
from src.domain.entities.workflow import (
    NodeType,
    Workflow,
    WorkflowExecution,
    WorkflowStatus,
)
from src.domain.repositories.workflow_repository import (
    WorkflowExecutionRepository,
    WorkflowRepository,
)
from src.infrastructure.logging.setup import get_logger

logger = get_logger(__name__)


class WorkflowService:
    """Service for workflow operations."""

    def __init__(
        self,
        workflow_repository: WorkflowRepository,
        execution_repository: WorkflowExecutionRepository,
        mcp_service: MCPService,
    ) -> None:
        """
        Initialize the workflow service.

        Args:
            workflow_repository: Repository for workflow operations
            execution_repository: Repository for execution operations
            mcp_service: MCP service for AI model operations
        """
        self.workflow_repository = workflow_repository
        self.execution_repository = execution_repository
        self.mcp_service = mcp_service

    async def create_workflow(self, workflow: Workflow, user: User) -> Workflow:
        """
        Create a new workflow.

        Args:
            workflow: The workflow to create
            user: The user creating the workflow

        Returns:
            The created workflow

        Raises:
            PermissionError: If user lacks permission
            ValueError: If workflow validation fails
        """
        # Check permissions
        if not user.has_permission(Permission.WORKFLOW_CREATE):
            raise PermissionError("User lacks permission to create workflows")

        # Validate workflow structure
        is_valid, errors = workflow.validate_structure()
        if not is_valid:
            raise ValueError(f"Workflow validation failed: {'; '.join(errors)}")

        # Set owner and update complexity
        workflow.owner_id = uuid.UUID(user.id)
        workflow.metadata.complexity = workflow.get_complexity()

        logger.info(f"Creating workflow '{workflow.name}' for user {user.id}")

        # Create workflow
        created_workflow = await self.workflow_repository.create(workflow)

        logger.info(f"Successfully created workflow {created_workflow.id}")
        return created_workflow

    async def get_workflow(self, workflow_id: uuid.UUID, user: User) -> Workflow | None:
        """
        Get a workflow by ID.

        Args:
            workflow_id: The workflow ID
            user: The requesting user

        Returns:
            The workflow if found and accessible, None otherwise

        Raises:
            PermissionError: If user lacks permission
        """
        # Check permissions
        if not user.has_permission(Permission.WORKFLOW_READ):
            raise PermissionError("User lacks permission to read workflows")

        workflow = await self.workflow_repository.get_by_id(workflow_id)

        if not workflow:
            return None

        # Check if user can access this workflow
        if workflow.owner_id != user.id and not workflow.is_public:
            raise PermissionError("User cannot access this workflow")

        return workflow

    async def list_workflows(
        self,
        user: User,
        status: WorkflowStatus | None = None,
        include_public: bool = True,
        limit: int = 100,
        offset: int = 0,
    ) -> list[Workflow]:
        """
        List workflows for a user.

        Args:
            user: The requesting user
            status: Optional status filter
            include_public: Whether to include public workflows
            limit: Maximum number of workflows to return
            offset: Number of workflows to skip

        Returns:
            List of workflows

        Raises:
            PermissionError: If user lacks permission
        """
        # Check permissions
        if not user.has_permission(Permission.WORKFLOW_READ):
            raise PermissionError("User lacks permission to read workflows")

        # Get user's own workflows
        user_workflows = await self.workflow_repository.get_by_owner(
            uuid.UUID(user.id), status, limit, offset
        )

        if not include_public:
            return user_workflows

        # Get public workflows if requested
        public_workflows = await self.workflow_repository.get_public_workflows(
            limit, offset
        )

        # Combine and deduplicate (user's public workflows might appear in both lists)
        all_workflows = user_workflows + [
            w for w in public_workflows if w.owner_id != user.id
        ]

        # Sort by updated_at descending and apply limit
        all_workflows.sort(key=lambda w: w.updated_at, reverse=True)
        return all_workflows[:limit]

    async def update_workflow(self, workflow: Workflow, user: User) -> Workflow:
        """
        Update a workflow.

        Args:
            workflow: The workflow to update
            user: The user updating the workflow

        Returns:
            The updated workflow

        Raises:
            PermissionError: If user lacks permission
            ValueError: If workflow validation fails or not found
        """
        # Check permissions
        if not user.has_permission(Permission.WORKFLOW_UPDATE):
            raise PermissionError("User lacks permission to update workflows")

        # Get existing workflow to check ownership
        existing_workflow = await self.workflow_repository.get_by_id(workflow.id)
        if not existing_workflow:
            raise ValueError(f"Workflow {workflow.id} not found")

        if existing_workflow.owner_id != user.id:
            raise PermissionError("User can only update their own workflows")

        # Validate workflow structure
        is_valid, errors = workflow.validate_structure()
        if not is_valid:
            raise ValueError(f"Workflow validation failed: {'; '.join(errors)}")

        # Update complexity and timestamp
        workflow.metadata.complexity = workflow.get_complexity()
        workflow.updated_at = datetime.now(UTC)

        logger.info(f"Updating workflow {workflow.id} for user {user.id}")

        # Update workflow
        updated_workflow = await self.workflow_repository.update(workflow)

        logger.info(f"Successfully updated workflow {workflow.id}")
        return updated_workflow

    async def delete_workflow(self, workflow_id: uuid.UUID, user: User) -> bool:
        """
        Delete a workflow.

        Args:
            workflow_id: The workflow ID to delete
            user: The user deleting the workflow

        Returns:
            True if deleted, False if not found

        Raises:
            PermissionError: If user lacks permission
        """
        # Check permissions
        if not user.has_permission(Permission.WORKFLOW_DELETE):
            raise PermissionError("User lacks permission to delete workflows")

        # Get workflow to check ownership
        workflow = await self.workflow_repository.get_by_id(workflow_id)
        if not workflow:
            return False

        if workflow.owner_id != user.id:
            raise PermissionError("User can only delete their own workflows")

        logger.info(f"Deleting workflow {workflow_id} for user {user.id}")

        # Delete workflow
        deleted = await self.workflow_repository.delete(workflow_id)

        if deleted:
            logger.info(f"Successfully deleted workflow {workflow_id}")
        else:
            logger.warning(f"Failed to delete workflow {workflow_id}")

        return deleted

    async def execute_workflow(
        self, workflow_id: uuid.UUID, input_data: dict[str, Any], user: User
    ) -> WorkflowExecution:
        """
        Execute a workflow.

        Args:
            workflow_id: The workflow ID to execute
            input_data: Input data for the workflow
            user: The user executing the workflow

        Returns:
            The workflow execution

        Raises:
            PermissionError: If user lacks permission
            ValueError: If workflow not found or invalid
        """
        # Check permissions
        if not user.has_permission(Permission.WORKFLOW_EXECUTE):
            raise PermissionError("User lacks permission to execute workflows")

        # Get workflow
        workflow = await self.workflow_repository.get_by_id(workflow_id)
        if not workflow:
            raise ValueError(f"Workflow {workflow_id} not found")

        # Check if user can execute this workflow
        if workflow.owner_id != user.id and not workflow.is_public:
            raise PermissionError("User cannot execute this workflow")

        if workflow.status != WorkflowStatus.ACTIVE:
            raise ValueError(f"Workflow {workflow_id} is not active")

        # Validate workflow structure
        is_valid, errors = workflow.validate_structure()
        if not is_valid:
            raise ValueError(f"Workflow validation failed: {'; '.join(errors)}")

        # Create execution
        execution = WorkflowExecution(
            workflow_id=workflow_id,
            user_id=uuid.UUID(user.id),
            input_data=input_data,
        )

        logger.info(f"Starting execution {execution.id} for workflow {workflow_id}")

        # Save execution
        execution = await self.execution_repository.create(execution)

        # Start execution asynchronously
        asyncio.create_task(self._execute_workflow_async(workflow, execution))

        return execution

    async def get_execution(
        self, execution_id: uuid.UUID, user: User
    ) -> WorkflowExecution | None:
        """
        Get a workflow execution by ID.

        Args:
            execution_id: The execution ID
            user: The requesting user

        Returns:
            The execution if found and accessible, None otherwise

        Raises:
            PermissionError: If user lacks permission
        """
        # Check permissions
        if not user.has_permission(Permission.WORKFLOW_READ):
            raise PermissionError("User lacks permission to read workflow executions")

        execution = await self.execution_repository.get_by_id(execution_id)

        if not execution:
            return None

        # Check if user can access this execution
        if execution.user_id != user.id:
            # Check if user owns the workflow
            workflow = await self.workflow_repository.get_by_id(execution.workflow_id)
            if not workflow or workflow.owner_id != user.id:
                raise PermissionError("User cannot access this execution")

        return execution

    async def list_executions(
        self,
        user: User,
        workflow_id: uuid.UUID | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[WorkflowExecution]:
        """
        List workflow executions for a user.

        Args:
            user: The requesting user
            workflow_id: Optional workflow filter
            limit: Maximum number of executions to return
            offset: Number of executions to skip

        Returns:
            List of executions

        Raises:
            PermissionError: If user lacks permission
        """
        # Check permissions
        if not user.has_permission(Permission.WORKFLOW_READ):
            raise PermissionError("User lacks permission to read workflow executions")

        if workflow_id:
            # Get executions for specific workflow
            workflow = await self.workflow_repository.get_by_id(workflow_id)
            if not workflow:
                return []

            # Check if user can access this workflow
            if workflow.owner_id != user.id and not workflow.is_public:
                raise PermissionError("User cannot access this workflow")

            return await self.execution_repository.get_by_workflow(
                workflow_id, limit, offset
            )
        else:
            # Get all executions for user
            return await self.execution_repository.get_by_user(
                uuid.UUID(user.id), limit, offset
            )

    async def _execute_workflow_async(
        self, workflow: Workflow, execution: WorkflowExecution
    ) -> None:
        """
        Execute a workflow asynchronously.

        Args:
            workflow: The workflow to execute
            execution: The execution context
        """
        try:
            # Start execution
            execution.start_execution()
            execution.add_log_entry(
                "INFO", f"Starting workflow execution: {workflow.name}"
            )
            await self.execution_repository.update(execution)

            # Execute workflow nodes
            output_data = await self._execute_workflow_nodes(workflow, execution)

            # Complete execution
            execution.complete_execution(output_data)
            execution.add_log_entry("INFO", "Workflow execution completed successfully")

            # Update workflow performance metrics
            workflow.update_performance(
                execution.execution_time or 0.0, True, execution.cost
            )
            await self.workflow_repository.update(workflow)

        except Exception as e:
            logger.error(f"Workflow execution {execution.id} failed: {e}")

            # Fail execution
            error_data = {"error": str(e), "type": type(e).__name__}
            execution.fail_execution(error_data)
            execution.add_log_entry("ERROR", f"Workflow execution failed: {e}")

            # Update workflow performance metrics
            workflow.update_performance(
                execution.execution_time or 0.0, False, execution.cost
            )
            await self.workflow_repository.update(workflow)

        finally:
            # Save final execution state
            await self.execution_repository.update(execution)

    async def _execute_workflow_nodes(
        self, workflow: Workflow, execution: WorkflowExecution
    ) -> dict[str, Any]:
        """
        Execute workflow nodes in order.

        Args:
            workflow: The workflow to execute
            execution: The execution context

        Returns:
            The workflow output data
        """
        # Find input nodes
        input_nodes = [node for node in workflow.nodes if node.type == NodeType.INPUT]
        if not input_nodes:
            raise ValueError("Workflow has no input nodes")

        # Initialize execution context
        context = {"input": execution.input_data, "variables": {}}

        # Execute nodes starting from input nodes
        for input_node in input_nodes:
            await self._execute_node(input_node, workflow, execution, context)

        # Find output nodes and collect results
        output_nodes = [node for node in workflow.nodes if node.type == NodeType.OUTPUT]
        output_data = {}

        for output_node in output_nodes:
            node_output = context["variables"].get(output_node.id, {})
            output_data[output_node.id] = node_output

        return output_data

    async def _execute_node(
        self,
        node,
        workflow: Workflow,
        execution: WorkflowExecution,
        context: dict[str, Any],
    ) -> Any:
        """
        Execute a single workflow node.

        Args:
            node: The node to execute
            workflow: The workflow being executed
            execution: The execution context
            context: The execution context data

        Returns:
            The node output
        """
        execution.current_node_id = node.id
        execution.add_log_entry("INFO", f"Executing node: {node.id} ({node.type})")
        await self.execution_repository.update(execution)

        try:
            if node.type == NodeType.INPUT:
                # Input node - pass through input data
                output = context["input"]

            elif node.type == NodeType.OUTPUT:
                # Output node - collect data from previous nodes
                output = self._collect_node_inputs(node, workflow, context)

            elif node.type == NodeType.AGENT:
                # Agent node - call MCP service for AI inference
                output = await self._execute_agent_node(
                    node, workflow, execution, context
                )

            elif node.type == NodeType.CONDITION:
                # Condition node - evaluate condition and route
                output = await self._execute_condition_node(
                    node, workflow, execution, context
                )

            elif node.type == NodeType.TRANSFORM:
                # Transform node - apply data transformation
                output = await self._execute_transform_node(
                    node, workflow, execution, context
                )

            elif node.type == NodeType.DELAY:
                # Delay node - wait for specified time
                output = await self._execute_delay_node(
                    node, workflow, execution, context
                )

            else:
                # Default handling for other node types
                execution.add_log_entry(
                    "WARNING", f"Unsupported node type: {node.type}"
                )
                output = self._collect_node_inputs(node, workflow, context)

            # Store node output in context
            context["variables"][node.id] = output
            execution.completed_nodes.append(node.id)

            execution.add_log_entry("INFO", f"Node {node.id} completed successfully")

            # Execute connected nodes
            await self._execute_connected_nodes(node, workflow, execution, context)

            return output

        except Exception as e:
            execution.add_log_entry("ERROR", f"Node {node.id} failed: {e}")
            raise

    def _collect_node_inputs(
        self, node, workflow: Workflow, context: dict[str, Any]
    ) -> dict[str, Any]:
        """
        Collect inputs for a node from connected nodes.

        Args:
            node: The target node
            workflow: The workflow
            context: The execution context

        Returns:
            Collected input data
        """
        inputs = {}

        # Find edges that target this node
        input_edges = [edge for edge in workflow.edges if edge.target == node.id]

        for edge in input_edges:
            source_output = context["variables"].get(edge.source, {})
            handle_key = edge.target_handle or "default"
            inputs[handle_key] = source_output

        return inputs

    async def _execute_connected_nodes(
        self,
        node,
        workflow: Workflow,
        execution: WorkflowExecution,
        context: dict[str, Any],
    ) -> None:
        """
        Execute nodes connected to the current node.

        Args:
            node: The current node
            workflow: The workflow
            execution: The execution context
            context: The execution context data
        """
        # Find edges that start from this node
        output_edges = [edge for edge in workflow.edges if edge.source == node.id]

        for edge in output_edges:
            target_node = next((n for n in workflow.nodes if n.id == edge.target), None)

            if target_node and target_node.id not in execution.completed_nodes:
                await self._execute_node(target_node, workflow, execution, context)

    async def _execute_agent_node(
        self,
        node,
        workflow: Workflow,
        execution: WorkflowExecution,
        context: dict[str, Any],
    ) -> dict[str, Any]:
        """
        Execute an agent node using MCP service.

        Args:
            node: The agent node to execute
            workflow: The workflow being executed
            execution: The execution context
            context: The execution context data

        Returns:
            The agent output
        """
        # Collect inputs from connected nodes
        inputs = self._collect_node_inputs(node, workflow, context)

        # Get node configuration
        config = node.data.get("configuration", {})
        model_id = config.get("model_id", "gpt-4")
        max_tokens = config.get("max_tokens", 1000)
        temperature = config.get("temperature", 0.7)

        # Prepare messages for MCP service
        messages = []

        # Add system message if configured
        if "system_prompt" in config:
            messages.append({"role": "system", "content": config["system_prompt"]})

        # Add user message from inputs
        user_content = inputs.get("default", {})
        if isinstance(user_content, dict) or not isinstance(user_content, str):
            user_content = str(user_content)

        messages.append({"role": "user", "content": user_content})

        # Create MCP request
        from src.domain.entities.mcp import MCPRequest

        mcp_request = MCPRequest(
            model_id=model_id,
            messages=messages,
            max_tokens=max_tokens,
            temperature=temperature,
            context_id=None,  # No specific context for workflow nodes
            metadata={"node_id": node.id, "workflow_id": str(workflow.id)},
        )

        execution.add_log_entry("INFO", f"Calling MCP service with model {model_id}")

        # Call MCP service
        try:
            response = await self.mcp_service.process_request(
                execution.user_id, mcp_request
            )

            # Update execution cost
            execution.cost += (
                response.usage.get("total_tokens", 0) * 0.001
            )  # Mock cost calculation

            execution.add_log_entry("INFO", "MCP service call completed successfully")

            return {
                "content": response.content,
                "usage": response.usage,
                "model": response.model_id,
            }

        except Exception as e:
            execution.add_log_entry("ERROR", f"MCP service call failed: {e}")
            raise

    async def _execute_condition_node(
        self,
        node,
        workflow: Workflow,
        execution: WorkflowExecution,
        context: dict[str, Any],
    ) -> dict[str, Any]:
        """
        Execute a condition node.

        Args:
            node: The condition node to execute
            workflow: The workflow being executed
            execution: The execution context
            context: The execution context data

        Returns:
            The condition result
        """
        # Collect inputs from connected nodes
        inputs = self._collect_node_inputs(node, workflow, context)

        # Get condition configuration
        config = node.data.get("configuration", {})
        condition_type = config.get("type", "equals")
        left_value = config.get("left_value", "")
        right_value = config.get("right_value", "")

        # Evaluate condition
        try:
            if condition_type == "equals":
                result = str(inputs.get("left", left_value)) == str(
                    inputs.get("right", right_value)
                )
            elif condition_type == "not_equals":
                result = str(inputs.get("left", left_value)) != str(
                    inputs.get("right", right_value)
                )
            elif condition_type == "contains":
                result = str(inputs.get("right", right_value)) in str(
                    inputs.get("left", left_value)
                )
            elif condition_type == "greater_than":
                result = float(inputs.get("left", left_value)) > float(
                    inputs.get("right", right_value)
                )
            elif condition_type == "less_than":
                result = float(inputs.get("left", left_value)) < float(
                    inputs.get("right", right_value)
                )
            else:
                result = bool(inputs.get("default", False))

            execution.add_log_entry("INFO", f"Condition evaluated to: {result}")

            return {
                "result": result,
                "condition_type": condition_type,
                "inputs": inputs,
            }

        except Exception as e:
            execution.add_log_entry("ERROR", f"Condition evaluation failed: {e}")
            return {"result": False, "error": str(e)}

    async def _execute_transform_node(
        self,
        node,
        workflow: Workflow,
        execution: WorkflowExecution,
        context: dict[str, Any],
    ) -> dict[str, Any]:
        """
        Execute a transform node.

        Args:
            node: The transform node to execute
            workflow: The workflow being executed
            execution: The execution context
            context: The execution context data

        Returns:
            The transformed data
        """
        # Collect inputs from connected nodes
        inputs = self._collect_node_inputs(node, workflow, context)

        # Get transform configuration
        config = node.data.get("configuration", {})
        transform_type = config.get("type", "passthrough")

        try:
            if transform_type == "passthrough":
                # Simply pass through the input
                output = inputs.get("default", {})

            elif transform_type == "extract_field":
                # Extract a specific field from input
                field_name = config.get("field_name", "")
                input_data = inputs.get("default", {})
                if isinstance(input_data, dict):
                    output = input_data.get(field_name, None)
                else:
                    output = None

            elif transform_type == "format_string":
                # Format a string template with input data
                template = config.get("template", "{input}")
                input_data = inputs.get("default", {})
                output = template.format(input=input_data, **inputs)

            elif transform_type == "json_parse":
                # Parse JSON string
                import json

                input_str = str(inputs.get("default", "{}"))
                output = json.loads(input_str)

            elif transform_type == "json_stringify":
                # Convert to JSON string
                import json

                input_data = inputs.get("default", {})
                output = json.dumps(input_data)

            else:
                # Default: pass through
                output = inputs.get("default", {})

            execution.add_log_entry("INFO", f"Transform '{transform_type}' completed")

            return {
                "output": output,
                "transform_type": transform_type,
                "inputs": inputs,
            }

        except Exception as e:
            execution.add_log_entry("ERROR", f"Transform failed: {e}")
            return {"output": inputs.get("default", {}), "error": str(e)}

    async def _execute_delay_node(
        self,
        node,
        workflow: Workflow,
        execution: WorkflowExecution,
        context: dict[str, Any],
    ) -> dict[str, Any]:
        """
        Execute a delay node.

        Args:
            node: The delay node to execute
            workflow: The workflow being executed
            execution: The execution context
            context: The execution context data

        Returns:
            The delay result
        """
        # Collect inputs from connected nodes
        inputs = self._collect_node_inputs(node, workflow, context)

        # Get delay configuration
        config = node.data.get("configuration", {})
        delay_seconds = config.get("delay_seconds", 1.0)

        execution.add_log_entry(
            "INFO", f"Delaying execution for {delay_seconds} seconds"
        )

        # Perform delay
        await asyncio.sleep(delay_seconds)

        execution.add_log_entry("INFO", "Delay completed")

        return {"delay_seconds": delay_seconds, "inputs": inputs}
